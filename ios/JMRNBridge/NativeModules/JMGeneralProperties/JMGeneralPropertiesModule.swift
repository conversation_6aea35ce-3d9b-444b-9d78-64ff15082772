//
//  JMGeneralPropertiesModule.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 20/06/25.
//

import Foundation

@objc(JMGeneralPropertiesModule)
class JMGeneralPropertiesModule: NSObject {
  
  @objc
  func getDeviceInfo(_ resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
    let device = UIDevice.current
    let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
    
    let result: [String: Any] = [
      "device_model": device.model,
      "device_name": device.name,
      "device_os": "iOS",
      "os_version": device.systemVersion,
      "device_type": 3,
      "unique_id": device.identifierForVendor?.uuidString ?? "",
      "app_version": appVersion
    ]
    
    resolver(result)
  }
}
