//
//  JMInfoPlist.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> Sa<PERSON> on 26/04/25.
//

import Foundation

enum InfoPlist {
  static let googleMapKey = "GOOGLE_MAPS_API_KEY"
}

public enum JMInfoPlist {
  
    public static var infoPlistDictionary: [String: Any] = {
        guard let infoPlistDictionary = Bundle.main.infoDictionary else {
            fatalError("Plist file not found")
        }
        return infoPlistDictionary
    }()
    
    // MARK: - Plist values
    static let googleMapKey: String = {
      guard let value = JMInfoPlist.infoPlistDictionary[InfoPlist.googleMapKey] as? String else {
            return "AIzaSyCdP5Kx1XxidSUsy4vTArUFrfqGKsmPbh0"
        }
        return value
    }()

}
