//
//  JMAddressModel.swift
//  JioMart
//
//  Created by Manish4 Sah on 25/04/25.
//


import Foundation
import GoogleMaps

class JMAddress: NSObject {
  var address: JMAddressModel?;
  
  override init() {
    self.address = JMAddressModel.init(city: "", state: "", pin: "")
  }
  
  init?(from address: GMSAddress){
    guard let country = address.country,
             let city = address.locality,
             let state = address.administrativeArea,
             let pin = address.postalCode else {
           return nil
       }
    self.address = JMAddressModel(city: city, state: state, pin: pin, country: country)
    self.address?.coordinate = CLLocationCoordinate2D(latitude: address.coordinate.latitude, longitude: address.coordinate.longitude)
    self.address?.address = address.lines?.joined(separator: ", ")
    self.address?.area = address.subLocality ?? ""
    self.address?.lat = address.coordinate.latitude
    self.address?.lon = address.coordinate.longitude
  }
  
  func reverseGeocode(latitude: CLLocationDegrees, longitude: CLLocationDegrees, completion: @escaping (JMAddressModel?) -> Void) {
    let geocoder = GMSGeocoder();
    let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    geocoder.reverseGeocodeCoordinate(coordinate){response, _ in
        guard let address = response?.firstResult() else {
          completion(nil)
          return
        }
        if let results = response?.results(){
          var foundValidAddress = false
          for result in results {
            if let _ = result.postalCode {
              let locality = result.locality ?? ""
              let administrativeArea = result.administrativeArea ?? ""
              if (LanguageDetector.identifyLanguage(of: locality) == .english && LanguageDetector.identifyLanguage(of: administrativeArea) == .english) {
                if let jmAddress = JMAddress.init(from: result)?.address {
                  completion(jmAddress)
                  foundValidAddress = true
                  return
                }
              }
            }
          }
          if !foundValidAddress {
            completion(nil)
          }
        }
      
    }
  }
}
