import { GestureHandlerRootView } from 'react-native-gesture-handler';
import ScreenSlot, { DeeplinkHandler } from '../../jiomart-general/src/ui/JMScreenSlot';
import AllCategories from './components/AllCategories';
import useAllCategoriesScreenController from './controller/useAllCategoriesScreenController';
import AllCategoriesShimmer from './skeleton/AllCategoriesShimmer';
import { JMAllCategoriesScreenProps } from './types/JMAllCategoriesScreenProps';

const AllCategoriesScreen = (props: JMAllCategoriesScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    category,
    JMRNNavigatorModule,
    insets,
    handleL1CategoryPress,
    handleL3CategoryPress,
  } = useAllCategoriesScreenController(props);

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          children={_ => {
            return (
              <GestureHandlerRootView>
                {category.data?.length > 0 ? (
                  <AllCategories
                    allCategory={category.data}
                    handleScroll={undefined}
                    onL1CategoryPress={cat => {
                      handleL1CategoryPress(cat, config?.l1_category?.cta);
                    }}
                    onL3CategoryPress={cat => {
                      handleL3CategoryPress(cat, config?.l3_category?.cta);
                    }}
                  />
                ) : (
                  <>
                    {

                      // (category?.isLoading) ?
                      //   <AllCategoriesShimmer />
                      //   :
                      <AllCategoriesShimmer />


                    /* {
                      <NegativeScreenUI
                        title={{ text: 'Coming Soon...' }}
                        subTitle={{ text: 'Products are currently not available at the selected PIN code' }}
                        isButtonVisible
                        button={{ title: 'Go Back to Home' }}
                        onPress={() => {
                          JMRNNavigatorModule.nativeBackPress();
                        }}
                        offset={insets.bottom}
                      />
                    } */}
                  </>
                )}
              </GestureHandlerRootView>
            );
          }}
        />
      )}
    />
  );
};

export default AllCategoriesScreen;
