import React from 'react';
import {ImageSourcePropType, TouchableOpacity} from 'react-native';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import categoriesStyles from '../styles/categories';
import CustomImage from './CustomImage';
import { getDivisionDimension } from '../../../jiomart-common/src/JMResponsive';

type CategoriesPropsType = {
  data: any;
  index: number;
  selectedIndex: number;
  onPress: () => void;
};

type ImageType = ImageSourcePropType | {uri: string} | undefined;

const Width = getDivisionDimension.width;
const Height = getDivisionDimension.height;

const Categories = (props: CategoriesPropsType) => {
  const {data, index, selectedIndex, onPress} = props;
  const imageUrl: ImageType = {uri: data?.banners?.portrait?.url};
  const imageRatio = Width < Height ? Width : Height;

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={index != selectedIndex ? onPress : undefined}
      style={[categoriesStyles.categories_container]}>
      <CustomImage
        style={{width: 48 * imageRatio, height: 48 * imageRatio}}
        source={imageUrl}
      />
      <JioText
        appearance={JioTypography.BODY_XXS_BOLD}
        style={[categoriesStyles.categories_text]}
        text={data.name}
        color={selectedIndex == index ? 'primary_grey_100' : 'primary_grey_80'}
        maxLines={2}
        textAlign="center"
      />
    </TouchableOpacity>
  );
};

export default Categories;
