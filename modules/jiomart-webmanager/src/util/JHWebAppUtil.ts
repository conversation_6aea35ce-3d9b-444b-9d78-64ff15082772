import {
  isNullOrUndefinedOrEmpty,
  NullableObject,
  NullableString,
} from '../../../jiomart-common/src/JMObjectUtility';
import {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import JMWebGlobalInfo from '../WebGlobalInfo';

export const extractQueryIntoParams = (
  bundle: NullableString,
): NullableObject => {
  if (isNullOrUndefinedOrEmpty(bundle)) {
    return null;
  }
  let params: Record<string, any> = {};
  bundle!.split('&').forEach(str => {
    const indexOfEqual = str.indexOf('=');
    const firstPart = str.substring(0, indexOfEqual);
    const secondPart = str.substring(indexOfEqual + 1);
    params[firstPart] = secondPart;
  });
  console.log({params});
  return params;
};

export function addQueryParamsToURL(bean: NavigationBean) {
  let webURL = bean?.actionUrl;
  if(!isNullOrUndefinedOrEmpty(bean?.deeplinkIdentifier)){
    webURL = webURL + "/" + bean?.deeplinkIdentifier;
    if(!isNullOrUndefinedOrEmpty(bean?.deeplinkParam)){
      webURL = webURL+ bean?.deeplinkParam;
    }
  }
  let baseDomain = getBaseURL();
  const containsParam = webURL?.includes('?') ?? false;
  if (webURL?.startsWith(baseDomain)) {
    return webURL + addQueryParams(containsParam);
  } else if (webURL?.startsWith('https://')) {
    if (webURL?.includes('?tab')) {
      return webURL;
    }
    return webURL + addQueryParams(containsParam);
  } else {
    let url =
      baseDomain + webURL?.replace(/^\/+/, '') + addQueryParams(containsParam);
    return url;
  }
}

function addQueryParams(containsParam: boolean) {
  const query = JMWebGlobalInfo.Instance.getQuery();
  return `${containsParam ? '&' : '?'}${query}`;
}

export const getModuleFromUrl = (url: NullableString): NullableString => {
  if (isNullOrUndefinedOrEmpty(url)) {
    return null;
  }
  const com = url!.split('.com');
  return com.length > 1 ? com[1].split('?')[0] : url;
};
