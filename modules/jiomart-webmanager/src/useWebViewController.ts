import {useFocusEffect} from '@react-navigation/native';
import {useEffect, useRef, useState} from 'react';
import {BackHandler, Platform} from 'react-native';
import WebView, {
  WebViewMessageEvent,
  WebViewNavigation,
} from 'react-native-webview';
import {useDispatch} from 'react-redux';
import {base64Decode} from '../../jiomart-common/src/Helper';
import {AppScreens} from '../../jiomart-common/src/JMAppScreenEntry';
import {
  ActionType,
  navBeanObj,
  NavigationBean,
  NavigationType,
} from '../../jiomart-common/src/JMNavGraphUtil';
import {
  AsyncStorageKeys,
  EventEmitterKeys,
} from '../../jiomart-common/src/JMConstants';
import {JMSharedViewModel} from '../../jiomart-common/src/JMSharedViewModel';
import {isNullOrUndefinedOrEmpty} from '../../jiomart-common/src/JMObjectUtility';
import {JMLogger} from '../../jiomart-common/src/utils/JMLogger';
import {
  navigateTo,
  openLogin,
} from '../../jiomart-general/src/navigation/JMNavGraph';
import {useGlobalState} from '../../jiomart-general/src/context/JMGlobalStateProvider';
import {addQueryParamsToURL} from './util/JHWebAppUtil';
import {WebViewEventConstant} from './WebEventConstants';
import {JProps} from './WebViewScreen';
import {getBaseURL} from '../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {
  GenericToast,
  HeaderType,
  mergeGenericToastTypeData,
} from '../../jiomart-common/src/JMScreenSlot.types';
import JMWebGlobalInfo from './WebGlobalInfo';
import useCartOperation from '../../jiomart-cart/src/hooks/useCartOperation';
import {JMDatabaseManager} from '../../jiomart-networkmanager/src/db/JMDatabaseManager';
import useWishlistOperation from '../../jiomart-wishlist/src/hooks/useWishlistOperation';
import {addStringPref} from '../../jiomart-common/src/JMAsyncStorageHelper';
import {handleDeeplinkIntent} from '../../jiomart-general/src/deeplink/JMDeeplinkUtility';
import {useConfigFile} from '../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

interface PageParams {
  slug?: string[];
  group?: string[];
}

interface PageQuery {
  url?: string[];
  dept?: string[];
  l2?: string[];
  l3_category_names?: string[];
  tab?: string[];
  [key: string]: any;
}

interface OpenActionPage {
  type: string;
  params: PageParams;
  query: PageQuery;
}

interface OpenActionValue {
  page: OpenActionPage;
}

const enum WebToastType {
  SUCCES = 0,
  ERROR = 1,
  WARNING = 2,
  INFO = 3,
}

const useWebViewController = (props: JProps) => {
  const {route, navigation} = props;
  const navigationBean = route.params;
  const webViewRef = useRef<WebView | null>(null);
  const [bean, setBean] = useState<NavigationBean>(navigationBean);
  const [webState, setWebState] = useState<WebViewState>(setInitialState());
  const [webCanGoBack, setWebCanGoBack] = useState(false);
  const [statusBarColor, setStatusBarColor] = useState('#0078AD');
  const {event, setEvent, setQCDetails, setToastTypeData} = useGlobalState();
  const {getCart} = useCartOperation();
  const {getWishlistIds} = useWishlistOperation();

  const [screenSpecificQcMessage, setScreenSpecificQcMessage] = useState('');

  const clearWebViewData = () => {
    if (webViewRef.current) {
      webViewRef?.current?.clearCache?.(true);
      webViewRef?.current?.clearHistory?.();

      webViewRef.current.injectJavaScript(`   
        document.cookie.split(";").forEach(function(c) { 
          document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
        });
        
       
        localStorage.clear();
        sessionStorage.clear();
        
        true; 
      `);
    }
  };

  useFocusEffect(() => {
    if (JMWebGlobalInfo.Instance.getWebUserLogout()) {
      clearWebViewData();
      JMWebGlobalInfo.Instance.setWebUserLogout(false);
      webViewRef?.current?.reload();
    }
  });
  useEffect(() => {
    if (navigationBean) {
      setBean(navigationBean);
    }
  }, [navigationBean]);

  const enum OpenActionEventConstant {
    COLLECTION = 'collection',
    PRODUCTS = 'products',
    PRODUCT = 'product',
    SECTIONS = 'sections',
    CUSTOM = 'custom',
    ORDERS = 'orders',
    HOME = 'home',
    BRAND = 'brand',
  }

  const commanConfigData = useConfigFile(
    JMConfigFileName.JMCommonContentFileName,
  );
  const quickCommerceConfig = commanConfigData?.quickCommerceConfig;

  useEffect(() => {
    console.log('webview event', event);
    if (!isNullOrUndefinedOrEmpty(event)) {
      const key = Object.keys(event)[0];
      switch (key) {
        case EventEmitterKeys.WEB_VIEW_EVENT_EMITT:
          const value = event[key];
          handleWebViewEvent(value);
          setEvent([]);
          break;

        default:
          break;
      }
    }
  }, [event]);
  useEffect(() => {
    if (statusBarColor != '#0078AD') {
      setStatusBarColor('#0078AD');
    }
  }, []);

  const handleWebViewEvent = (data: {[key: string]: any}) => {
    Object.entries(data).forEach(([key, value]) => {
      try {
        // const parsedValue =
        //   typeof value === 'string' ? JSON.parse(value) : value;
        const eventFunctionCall = `(function(param) {
        try {
          if (typeof ${key} === 'function') {
            ${key}(param);
          } else {
            console.error('Function ${key} not found in WebView');
          }
        } catch (e) {
          console.error('WebView execution error:', e);
        }
      })(${value})`;
        // '${JSON.stringify(parsedValue)}'
        // ${JSON.stringify(parsedValue)}
        console.log('eventFunctionCall', eventFunctionCall);
        if (webViewRef?.current?.injectJavaScript) {
          console.log('eventFunctionCall 2', eventFunctionCall);
          webViewRef.current.injectJavaScript(eventFunctionCall);
        } else {
          JMLogger.log('WebView reference not available');
        }
      } catch (error) {}
    });
  };

  function setInitialState(): WebViewState {
    if (bean) {
      switch (bean.actionType) {
        case ActionType.OPEN_WEB_HTML:
          return {state: WebStateType.HTML, content: addQueryParamsToURL(bean)};
        case ActionType.OPEN_WEB_URL:
          return {
            state: WebStateType.URL,
            content: addQueryParamsToURL(bean),
          };
        case ActionType.OPEN_WEB_URL_WITH_TOKEN: {
          return {state: WebStateType.LOADING};
        }
      }
    }
    return {state: WebStateType.ERROR};
  }

  const handleNavigationStateChange = (navState: WebViewNavigation) => {
    const {url, canGoBack} = navState;

    // if (isNullOrUndefinedOrEmpty(mainWebUrl)) {
    //   setMainWebUrl(url);
    // }
    // setCurrentWebUrl(url);
    setWebCanGoBack(canGoBack);
  };

  const presentHeaderHandler = (headerType: number) => {
    try {
      JMLogger.log(
        'presentHeaderHandler beanheaderType ' +
          bean.headerType +
          ' headerType ' +
          headerType,
      );
      if (bean.headerType !== headerType) {
        setBean(prev => {
          return {
            ...prev,
            headerType,
          };
        });
      }
    } catch (error) {}
  };

  const setMenuTitle = (navTitle: string) => {
    try {
      JMLogger.log('setMenuTitle ' + bean.navTitle + ' navTitle ' + navTitle);
      if (bean.navTitle !== navTitle) {
        setBean(prev => {
          return {
            ...prev,
            navTitle,
          };
        });
      }
    } catch (error) {}
  };

  const setShouldShowDeliverToBar = (val: boolean) => {
    try {
      JMLogger.log(
        'setShouldShowDeliverToBar shouldShowDeliverToBar ' +
          bean.shouldShowDeliverToBar +
          ' shouldShowDeliverToBar ' +
          val,
      );
      if (bean.shouldShowDeliverToBar !== val) {
        setBean(prev => {
          return {
            ...prev,
            shouldShowDeliverToBar: val,
          };
        });
      }
    } catch (error) {}
  };

  const setShouldShowBottomNavBar = (val: boolean) => {
    try {
      JMLogger.log(
        'shouldShowBottomNavBar ' +
          bean.shouldShowBottomNavBar +
          ' shouldShowBottomNavBar ' +
          val,
      );
      if (bean.shouldShowBottomNavBar !== val) {
        setBean(prev => {
          return {
            ...prev,
            shouldShowBottomNavBar: val,
          };
        });
      }
    } catch (error) {}
  };

  const handleBackPress = () => {
    // if (JMSharedViewModel.Instance.previousPageURL !== '') {
    //   console.log(
    //     'back pressed url -- ',
    //     JMSharedViewModel.Instance.previousPageURL,
    //   );
    //   JMSharedViewModel.Instance.setPreviousPageURL('');
    // } else
    if (webCanGoBack) {
      webViewRef.current?.goBack();
      JMSharedViewModel.Instance.setPreviousPageURL('');
    } else if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      BackHandler.exitApp();
    }
  };

  const navigateToWebView = (url: string) => {
    navigateTo(
      navBeanObj({
        actionType: ActionType.OPEN_WEB_URL,
        destination: AppScreens.COMMON_WEB_VIEW,
        headerVisibility: HeaderType.CUSTOM,
        navigationType: NavigationType.PUSH,
        loginRequired: false,
        actionUrl: url,
        headerType: 9,
      }),
      navigation,
    );
  };

  const buildURLWithQueryParams = (
    basePath: string,
    query: PageQuery,
  ): string => {
    const baseURL = String(getBaseURL());
    let url = `${baseURL}/${basePath}`;
    if (Object.keys(query).length > 0) {
      url += '?';
      for (const key in query) {
        if (Array.isArray(query[key])) {
          url += `${key}=${query[key][0]}&`;
        }
      }
    }
    return url;
  };

  const handleCollectionPage = (slug: string, query: PageQuery) => {
    const baseURL = String(getBaseURL());
    let pageURL = `${baseURL}/collection/${slug}`;

    if (query.l3_category_names?.length) {
      pageURL += '?';
      query.l3_category_names.forEach((category: string) => {
        pageURL += `l3_category_names=${category}&`;
      });
    }

    navigateToWebView(pageURL);
  };

  const handleProductsPage = (slug: string, query: PageQuery) => {
    const pageURL = buildURLWithQueryParams('products', query);
    navigateToWebView(pageURL);
  };

  const handleProductPage = (slug: string) => {
    const baseURL = String(getBaseURL());
    const productPageURL = `${baseURL}/product/${slug}`;
    navigateToWebView(productPageURL);
  };

  const handleSectionsPage = (slug: string) => {
    const baseURL = String(getBaseURL());
    const pageURL = `${baseURL}/sections/${slug}`;
    navigateToWebView(pageURL);
  };

  const handleCustomPage = (query: PageQuery) => {
    const baseURL = String(getBaseURL());
    let pageURL = baseURL;
    if (query.url) {
      pageURL += query.url[0];
      if (!pageURL.includes('/collection/')) {
        navigateToWebView(pageURL);
        return;
      }
    }

    const urlParts = query.url?.[0].split('/') || [];
    const slug = urlParts[urlParts.length - 1];
    const newQuery = {
      department: query.dept?.[0],
      category: query.l2?.[0],
      type: 'products',
    };

    handleProductsPage(slug, newQuery);
  };

  const handleHomePage = (query: PageQuery) => {
    if (query.tab?.[0] === 'fashion') {
      const baseURL = String(getBaseURL());
      const pageURL = `${baseURL}/?tab=${query.tab[0]}`;
      navigateToWebView(pageURL);
    } else {
      // navigation.navigate(bottomTabRoute.Home);
    }
  };

  const handleBrandPage = (slug: string) => {
    const baseURL = String(getBaseURL());
    const pageURL = `${baseURL}/brand/${slug}`;
    navigateToWebView(pageURL);
  };
  const handleOpenActionEvent = (openActionValue: OpenActionValue) => {
    const {page} = openActionValue;
    const slug = page.params.slug?.[0] || page.params.group?.[0] || '';

    switch (page.type) {
      case OpenActionEventConstant.COLLECTION:
        handleCollectionPage(slug, page.query);
        break;
      case OpenActionEventConstant.PRODUCTS:
        handleProductsPage(slug, page.query);
        break;
      case OpenActionEventConstant.PRODUCT:
        handleProductPage(slug);
        break;
      case OpenActionEventConstant.SECTIONS:
        handleSectionsPage(slug);
        break;
      case OpenActionEventConstant.CUSTOM:
        handleCustomPage(page.query);
        break;
      case OpenActionEventConstant.HOME:
        handleHomePage(page.query);
        break;
      case OpenActionEventConstant.ORDERS:
        navigation.popToTop();
        break;
      case OpenActionEventConstant.BRAND:
        handleBrandPage(slug);
        break;
    }
  };

  const handleDeeplink = async (eventsData: any) => {
    if (eventsData.value) {
      const deeplinkData = {
        mUri:
          eventsData.value === 'openUrlInNativeApp'
            ? eventsData.url
            : 'jiomart://com.jpl.jiomart/' + eventsData.value,
      };
      const bean = await handleDeeplinkIntent(deeplinkData);
      if (bean) {
        navigateTo(bean, navigation);
      }
    }
  };
  const handleSelectedSadoOption = (selectedSadoOption: string) => {
    JMSharedViewModel.Instance.setIsQcJourneySelected(
      selectedSadoOption === quickCommerceConfig?.quickDeliveryKey,
    );
    setQCDetails(prev => {
      return {
        ...prev,
        journey: selectedSadoOption,
      };
    });
  };

  const handleOrderConfirmed = (data: any) => {
    JMDatabaseManager.cart.deleteDBCart();
    getCart.mutate();
  };
  const handleStatusBarColor = (color: string) => {
    setStatusBarColor(color);
  };
  const handleCartCount = (data: any) => {
    getCart.mutate();
  };
  const handleWishlistUpdated = (data: any) => {
    getWishlistIds.mutate();
  };

  const handleSaveDetailsForSearchEndpoint = data => {
    setScreenSpecificQcMessage(data?.hyperlocalDeliveryMessage);
  };

  const handleToast = async (data: any) => {
    let modifiedToastData: any;
    if (data?.ctaActionUrl) {
      const deeplinkData = {
        mUri:
          data?.ctaActionUrl?.startsWith('https://') ||
          data?.ctaActionUrl?.startsWith('http://')
            ? data?.ctaActionUrl
            : 'jiomart://com.jpl.jiomart/' + data?.ctaActionUrl,
      };
      const cta = await handleDeeplinkIntent(deeplinkData);
      modifiedToastData = {
        showButton: true,
        buttonText: data?.ctaTitle ?? '',
        cta,
      };
    }
    modifiedToastData = {
      message: data?.message ?? '',
    };

    switch (data?.toastType) {
      case WebToastType.ERROR:
        setToastTypeData(
          mergeGenericToastTypeData(GenericToast.ERROR, modifiedToastData),
        );
        break;
      case WebToastType.WARNING:
        setToastTypeData(
          mergeGenericToastTypeData(GenericToast.WARNING, modifiedToastData),
        );
        break;
      case WebToastType.INFO:
        setToastTypeData(
          mergeGenericToastTypeData(GenericToast.INFO, modifiedToastData),
        );
        break;
      default:
        setToastTypeData(
          mergeGenericToastTypeData(GenericToast.SUCCESS, modifiedToastData),
        );
        break;
    }
  };

  const handleJSEvents = async (e: WebViewMessageEvent) => {
    try {
      let events;
      try {
        events = JSON.parse(e.nativeEvent.data);
      } catch (jsonError) {
        events = base64Decode(e.nativeEvent.data);
      }
      JMLogger.log('BRIDGE EVENT RECEIVED => ', events);
      switch (events.type) {
        case WebViewEventConstant.OPEN_ACTION:
          handleOpenActionEvent(events.value);
          break;

        case WebViewEventConstant.PRESENT_HEADER:
          presentHeaderHandler(events.value);
          break;
        case WebViewEventConstant.UPDATE_NAVIGATION_DETAILS:
          setMenuTitle(events.value.menuTitle);
          JMSharedViewModel.Instance.setPreviousPageURL(
            events.value.previousPageURL,
          );
          break;
        case WebViewEventConstant.IS_DELIVER_TO_BAR_VISIBLE:
          setShouldShowDeliverToBar(!!events.value);
          break;
        case WebViewEventConstant.IS_BNB_VISIBLE:
          setShouldShowBottomNavBar(!!events.value);
          break;
        case WebViewEventConstant.HANDLE_DEEP_LINK:
          handleDeeplink(events);
          break;
        case WebViewEventConstant.INIT_CRA:
          openLogin(navigation);
          break;
        case WebViewEventConstant.UPDATED_CART_COUNT:
          handleCartCount(events?.value);
          break;
        case WebViewEventConstant.WISHLIST_UPDATED:
          handleWishlistUpdated(events?.value);
          break;
        case WebViewEventConstant.LOADING_COMPLETED:
          break;
        case WebViewEventConstant.GET_PSP_APP_LIST:
          break;
        case WebViewEventConstant.STATUS_BAR_COLOR:
          handleStatusBarColor(events.value);
          break;
        case WebViewEventConstant.ORDER_CONFIRMED:
          handleOrderConfirmed(events.value);
          break;
        case WebViewEventConstant.SAVE_SESSION_DETAILS:
          break;
        case WebViewEventConstant.SAVEDETAILSFORSEARCHENDPOINT:
          handleSaveDetailsForSearchEndpoint(events.value);
          const data = JSON.stringify(events.value);
          await addStringPref(AsyncStorageKeys.SEARCH_END_POINT_DETAILS, data);
          break;
        case WebViewEventConstant.ISBNBVISIBLE:
          break;
        case WebViewEventConstant.SHOW_TOAST:
          handleToast(events.value);
          break;
        case WebViewEventConstant.APPSFLYER_EVENT_TRIGGER:
          break;
        case WebViewEventConstant.SCROLL_POSITION:
          // dispatch(updateScrollPosition(dataObject.value));
          break;
        case WebViewEventConstant.lAUNCH_PSP_APP_FOR_UPI_PAYMENT:
          break;
        case WebViewEventConstant.SHARE:
          break;
        case WebViewEventConstant.DISABLESORTANDFILTER:
          break;
        case WebViewEventConstant.SELECTED_SADO_OPTION:
          handleSelectedSadoOption(events.value);
          break;
      }
    } catch (error) {
      JMLogger.log('Error in handleJSEvents - ', error);
    }
  };

  const onUrlOverride = (event: {navigationType: string; url: string}) => {
    if (
      Platform.OS === 'android' ||
      event.navigationType === 'click' ||
      event.navigationType === 'other'
    ) {
      return true;
    }
    return true;
  };

  const onError = () => {
    setWebState(prev => ({...prev, state: WebStateType.ERROR}));
  };

  return {
    webState,
    webViewRef,
    setWebState,
    handleJSEvents,
    presentHeaderHandler,
    onUrlOverride,
    onError,
    handleBackPress,
    setMenuTitle,
    handleNavigationStateChange,
    navigationBean: navigationBean,
    eventTriggerBean: bean,
    ...props,
    statusBarColor,
    screenSpecificQcMessage,
  };
};

export interface WebViewState {
  state: WebStateType;
  content?: string;
}

export enum WebStateType {
  LOADING,
  URL,
  HTML,
  ERROR,
}

export default useWebViewController;
