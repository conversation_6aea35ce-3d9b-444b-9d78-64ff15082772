import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {resetStore} from '../../jiomart-main/src/store/JMGlobalReduxAction';

export interface WebCallBack {
  identifier: string | undefined;
  event: string | undefined;
}

export interface WebState {
  WEB_CALLBACK: WebCallBack | null;
  scrollPosition: number;
}

const initialState: WebState = {
  WEB_CALLBACK: null,
  scrollPosition: 0,
};

const webSlice = createSlice({
  name: 'web',
  initialState,
  extraReducers: builder => builder.addCase(resetStore, () => initialState),
  reducers: {
    setWebCallback: (state, action: PayloadAction<WebCallBack | null>) => {
      state.WEB_CALLBACK = action.payload;
    },
    updateScrollPosition: (state, action: PayloadAction<number>) => {
      state.scrollPosition = action.payload;
    },
  },
});

export const {setWebCallback} = webSlice.actions;
export const { updateScrollPosition } = webSlice.actions;
export default webSlice.reducer;
