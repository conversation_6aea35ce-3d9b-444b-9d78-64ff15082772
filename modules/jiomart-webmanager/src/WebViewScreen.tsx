import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useRef} from 'react';
import {
  AppScreens,
  type ScreenProps,
} from '../../jiomart-common/src/JMAppScreenEntry';
import ScreenSlot, {
  DeeplinkHandler,
  createEventFinalBean,
} from '../../jiomart-general/src/ui/JMScreenSlot';
import GlobalBottomSheet from '../../jiomart-main/src/features/StartUp/GlobalBottomSheet';
import useGlobalBottomSheetController from '../../jiomart-main/src/features/StartUp/controllers/useGlobalBottomSheetController';
import {getBaseURL} from '../../jiomart-networkmanager/src/JMEnvironmentConfig';
import WebViewUtility from './WebViewUtility';
import useWebViewController from './useWebViewController';

export type JProps = ScreenProps<typeof AppScreens.COMMON_WEB_VIEW>;

const CommonWebViewScreen = (props: JProps) => {
  const {
    navigation,
    navigationBean,
    eventTriggerBean,
    webState,
    webViewRef,
    statusBarColor,
    handleJSEvents,
    onUrlOverride,
    onError,
    handleBackPress,
    handleNavigationStateChange,
    screenSpecificQcMessage
  } = useWebViewController(props);
  const {checkAndOpenNextSheet} = useGlobalBottomSheetController();

  const webViewMainUi = () => {
    console.log('webState.content', webState.content);
    return (
      <WebViewUtility
        source={{uri: webState.content || getBaseURL()}}
        onMessage={handleJSEvents}
        onRef={ref => {
          webViewRef.current = ref;
        }}
        onError={onError}
        javaScriptEnabled={true}
        onNavigationStateChange={handleNavigationStateChange}
        onShouldStartLoadWithRequest={onUrlOverride}
      />
    );
  };

  const isHomeUrl = (url: string) => {
    const base = getBaseURL();

    const normalizedUrl = url.split('?')[0].replace(/\/+$/, '');
    const normalizedBase = base.replace(/\/+$/, '');

    return normalizedUrl === normalizedBase;
  };

  useFocusEffect(
    useCallback(() => {
      if (isHomeUrl(webState.content ?? '')) {
        checkAndOpenNextSheet();
      }
    }, []),
  );

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={createEventFinalBean(bean, eventTriggerBean)}
          navigation={navigation}
          onCustomBackPress={handleBackPress}
          statusBarColor={statusBarColor}
          bottomSheetContent={({openDeliverToBarPincodeBtmSheet}) => (
            <GlobalBottomSheet
              openPincodeBottomSheet={() => {
                openDeliverToBarPincodeBtmSheet();
              }}
            />
          )}
          children={_ => {
            return webViewMainUi();
          }}
          deliverToBarData={{qcMessage : screenSpecificQcMessage}}
        />
      )}
    />
  );
};

export default CommonWebViewScreen;
