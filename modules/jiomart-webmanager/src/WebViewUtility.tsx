import { JioWebViewProps } from '@jio/rn_webview';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  View
} from 'react-native';
import WebView from 'react-native-webview';
import BottomNavBarAnimationHandler from '../../jiomart-general/src/ui/BottomNavBar/BottomNavBarAnimationHandler';

export function addHTMLBoilerPlate(
  data: string,
  style: string = '',
  script: string = '',
) {
  return `<!DOCTYPE html>
    <html>
      <head>
        <meta name='viewport' content='minimum-scale=1, initial-scale=1, width=device-width, shrink-to-fit=no, maximum-scale=1'/>
        <style>
        ${style}
        </style>
      </head>
      <body>
       ${data}
       <script>
       ${script}
       </script>
      </body>
    </html>`;
}

function WebViewUtility(props: JioWebViewProps) {
  const [loading, setLoading] = useState(false);
  // let webViewRef = useRef<WebView>(null);

  // useEffect(() => {
  //   const eventListener = DeviceEventEmitter.addListener(
  //     'commonJsEvent',
  //     data => {
  //       const event = JSON.parse(JSON.stringify(data));
  //       const eventValue = JSON.parse(event.eventValue);
  //       if (webViewRef != null) {
  //         webViewRef?.current?.injectJavaScript(eventValue.value);
  //       }
  //     },
  //   );
  //   return () => {
  //     eventListener.remove();
  //   };
  // });

   const {handleBottomNavBarAnimation} = BottomNavBarAnimationHandler();

  const handleLoadStart = () => {
    console.log('WebView load started');
    // setLoading(true);
  };

  const handleLoadEnd = () => {
    console.log('WebView load ended');
    // setLoading(false);
  };

  return (
    <View style={styles.container}>
      <WebView
        ref={ref => {
          props.onRef?.(ref);
        }}
        {...props}
        bounces={false}
        originWhitelist={['*']}
        onLoad={event => { }}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        javaScriptEnabled={true}
        webviewDebuggingEnabled={true}
        domStorageEnabled={true}
        style={styles.webview}
        onScroll={e => {
            if(e.nativeEvent.layoutMeasurement.height +
              e.nativeEvent.contentOffset.y <=
            e.nativeEvent.contentSize.height - 80){
              handleBottomNavBarAnimation(e);
            }
          }}
      />
      {loading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="small" />
        </View>
      )}
    </View>
  );
}

export const getParamsFromURL = (url: string) => {
  let payResult = '';

  try {
    if (url.trim().includes('&a=')) {
      payResult = decodeURI(url.split('&a=')[1]);
    } else {
      payResult = decodeURI(url.split('a=')[1].split('&i=')[0]);
    }
    return payResult;
  } catch {
    return payResult;
  }
};

export default WebViewUtility;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  webview: {
    flex: 1,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    zIndex: 999,
  },
});
