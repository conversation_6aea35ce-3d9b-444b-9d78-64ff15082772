
import {Platform} from 'react-native';
import { JMSharedViewModel } from '../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../jiomart-common/src/SourceType';

class JMWebGlobalInfo {
  private static _instance: JMWebGlobalInfo;

  private constructor() {}

  public static get Instance() {
    return this._instance || (this._instance = new this());
  }
  private webUserLogout = false;
  private sourceAttribution: {[key in AppSourceType]?: string} = {
    [AppSourceType.JM_JCP]: `source_attribution=JioMartApp-CPS&os=${Platform.OS}&version=${JMSharedViewModel.Instance.appVersion}`,
    [AppSourceType.JM_BAU]: `source_attribution=JioMartApp-CPS&utm_source=JioMartApp-CPS&utm_medium=CPS&utm_campaign=JioMartApp&os=${Platform.OS}&version=${JMSharedViewModel.Instance.appVersion}`,
  };

  public setWebUserLogout(status: boolean) {
    this.webUserLogout = status;
  }

  public getWebUserLogout() {
    return this.webUserLogout;
  }

  public getQuery() {
    return this.sourceAttribution[JMSharedViewModel.Instance.appSource];
  }
}

export default JMWebGlobalInfo;
