import {
  AndroidPermission,
  IOSPermission,
} from '../../../jiomart-common/src/JMConstants';
import {
  checkAndRequestSinglePermission,
  checkSinglePermission,
  openAppSetting,
  type JMPermissionType,
} from '../../../jiomart-common/src/JMPermission';
import {RESULTS} from 'react-native-permissions';
import {getCurrentPosition} from '../../../jiomart-common/src/GeolocationUtility';
import {getReverseGeoCodeFromLatLongNB} from '../../../jiomart-general/src/bridge/JMRNBridge';
import { useState } from 'react';
import { Alert, Platform } from 'react-native';

interface UseCurrentLocationProps {
  alertBlocked: {
    title: string;
    message?: string;
    button?: {
      text?: string;
    };
  };
}

type Handlers = {
  onGranted: () => Promise<any>;
  onLimited?: () => Promise<any>;
  onBlocked?: () => any;
  onDenied?: () => any;
  onUnavailable?: () => any;
};

const useCurrentLocation = (props: UseCurrentLocationProps) => {
  const { alertBlocked } = props;
  const [isFetchingLocation, setIsFetchingLocation] = useState(false);
  const LocationPermission = Platform.select({
    ios: IOSPermission.LOCATION_WHEN_IN_USE,
    android: AndroidPermission.ACCESS_FINE_LOCATION,
  });

  const checkLocationPermission = async ({
    onGranted,
    onLimited,
    onBlocked,
    onDenied,
    onUnavailable,
  }: Handlers) => {
    try {
      const status = await checkSinglePermission(
        LocationPermission as JMPermissionType,
      );

      switch (status) {
        case RESULTS.GRANTED:
          return await onGranted();

        case RESULTS.LIMITED:
          return onLimited ? await onLimited() : RESULTS.LIMITED;

        case RESULTS.BLOCKED:
          return onBlocked ? onBlocked() : RESULTS.BLOCKED;

        case RESULTS.DENIED:
          return onDenied ? onDenied() : RESULTS.DENIED;

        default:
          return status;
      }
    } catch (error) {
      return onUnavailable ? onUnavailable() : Promise.reject(RESULTS.UNAVAILABLE);
    }
  };

  const fetchCurrentLocation = async () => {
    try {
      setIsFetchingLocation(true);
      const status = await checkAndRequestSinglePermission(
        LocationPermission as JMPermissionType,
      );
      switch (status) {
        case RESULTS.GRANTED:
        case RESULTS.LIMITED:
          return await getCurrentPosition();
        case RESULTS.BLOCKED:
          Alert.alert(alertBlocked.title, alertBlocked.message, [
            {
              text: alertBlocked.button?.text,
              onPress: () => {
                openAppSetting();
              },
            },
          ]);
          return Promise.resolve(status);
        default:
          return Promise.resolve(status);
      }
    } catch (error) {
      return Promise.reject(RESULTS.UNAVAILABLE);
    } finally {
      setIsFetchingLocation(false);
    }
  };

  const fetchLocationFromReverseGeoCodeFromLatLong = async () => {
    const geoCords: any = await fetchCurrentLocation();
    const lat = geoCords?.coords?.latitude ?? 0;
    const long = geoCords?.coords?.longitude ?? 0;
    const address = await getReverseGeoCodeFromLatLongNB(lat, long);
    return {
      address: JSON.parse(address ?? ''),
      coords: geoCords?.coords,
    };
  };

  return {
    isFetchingLocation,
    fetchCurrentLocation,
    fetchLocationFromReverseGeoCodeFromLatLong,
    checkLocationPermission,
  };
};

export default useCurrentLocation;
