import {useState} from 'react';
import type {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';

export interface DeliverToBarBtmSheet {
  type: 'Deliver' | 'Pincode';
  visible: boolean;
  nav: NavigationBean;
}

export interface UseDeliverToBarBtmSheet {
  showDeliverToBarBtmSheet: boolean;
  showDeliverToBarPincodeBtmSheet: boolean;
  openDeliverToBarBtmSheet: () => void;
  openDeliverToBarPincodeBtmSheet: () => void;
  closeDeliverToBarBtmSheet: () => void;
  closeDeliverToBarPincodeBtmSheet: () => void;
}

const useDeliverToBarBtmSheet = () => {
  const [showDeliverToBarBtmSheet, setShowDeliverToBarBtmSheet] =
    useState(false);
  const [showDeliverToBarPincodeBtmSheet, setShowDeliverToBarPincodeBtmSheet] =
    useState(false);
  const openDeliverToBarBtmSheet = () => {
    setShowDeliverToBarBtmSheet(true);
  };
  const openDeliverToBarPincodeBtmSheet = () => {
    setShowDeliverToBarPincodeBtmSheet(true);
  };
  const closeDeliverToBarBtmSheet = () => {
    setShowDeliverToBarBtmSheet(false);
  };
  const closeDeliverToBarPincodeBtmSheet = () => {
    setShowDeliverToBarPincodeBtmSheet(false);
  };

  return {
    showDeliverToBarBtmSheet,
    showDeliverToBarPincodeBtmSheet,
    openDeliverToBarBtmSheet,
    openDeliverToBarPincodeBtmSheet,
    closeDeliverToBarBtmSheet,
    closeDeliverToBarPincodeBtmSheet,
  };
};

export default useDeliverToBarBtmSheet;
