import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JioText} from '@jio/rn_components';
import {
  ButtonKind,
  ButtonSize,
  ButtonState,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import React, {useCallback} from 'react';
import {
  FlatList,
  Platform,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import BottomSheet from '../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import JMMessageBox from '../../../jiomart-general/src/ui/JMMessageBox';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import NegativeScreenUI from '../../../jiomart-general/src/ui/NegativeScreenUI';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import JMDeleteAddressBtmSheet from '../BottomSheet/JMDeleteAddressBtmSheet';
import JMAddressCard from '../components/JMAddressCard';
import useJMAddressListScreenController from '../controller/useJMAddressListScreenController';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type {JMAddressListScreenProps} from '../types/JMAddressListScreenType';
import {getFormatterAddress} from '../utils';

const JMAddressListScreen = (props: JMAddressListScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    card,
    showAlertAddressBook,
    address,
    handleEmptyAddress,
    handleAddAddres,
    handleOpenMap,
    menuInfo,
    setMenuInfo,
    iconRefs,
    onIconPress,
    insets,
    flatListRef,
    generateMenuList,
    deleteBtmSheet,
    setDeleteBtmSheet,
    handleDeleteAddress,
  } = useJMAddressListScreenController(props);

  const {checkLocationPermission} = useCurrentLocation({
    alertBlocked: {title: ''},
  });

  const {userData} = useUserProfile();

  const renderAddressItem = useCallback(
    ({item, index}: {item: JMAddressModel; index: number}) => {
      return (
        <View pointerEvents={menuInfo ? 'none' : 'auto'}>
          <JMAddressCard
            onRef={ref => {
              if (ref) {
                iconRefs.current[`${item?.id}_${index}`] = ref;
              }
            }}
            key={`address-${index}`}
            name={{
              ...card?.name,
              text: item?.name ?? '',
            }}
            type={{
              ...card?.type,
              text: item?.address_type ?? '',
            }}
            icon={{
              ...card?.icon,
            }}
            // iconStyle={{
            //   padding: 8,
            //   borderWidth: 1,
            //   borderRadius: 100,
            //   borderColor: '#E0E0E0',
            // }}
            address={{
              ...card?.address,
              text: getFormatterAddress(item, card?.hideAddress) ?? '',
            }}
            phone={{
              ...card?.phone,
              text: card?.phone?.text?.replace('[TEXT]', item?.phone),
            }}
            subText={{
              ...card?.subText,
              text: item?.is_default_address ? card?.subText?.text : '',
            }}
            onIconPress={() =>
              onIconPress(`${item.id}`, index, item?.is_default_address)
            }
            isDefault={item?.id === userData?.preferred_shipping_address}
          />
        </View>
      );
    },
    [
      card?.address,
      card?.hideAddress,
      card?.icon,
      card?.name,
      card?.phone,
      card?.subText,
      card?.type,
      iconRefs,
      menuInfo,
      onIconPress,
      userData?.preferred_shipping_address,
    ],
  );
  const renderListHeaderComponent = () => {
    return (
      <>
        <JioText {...config?.headerTitle} />
        {showAlertAddressBook ? (
          <JMMessageBox
            {...config?.alert?.addressBook}
            style={styles.message}
          />
        ) : null}
      </>
    );
  };

  const bottomSheetContent = () => {
    return (
      <>
        <BottomSheet
          onBackDropClick={() => {
            setDeleteBtmSheet(null);
          }}
          onDrag={() => {
            setDeleteBtmSheet(null);
          }}
          visible={deleteBtmSheet}>
          <JMDeleteAddressBtmSheet
            config={config?.bottomSheet?.delete}
            name={address?.[deleteBtmSheet - 1]?.name}
            address={getFormatterAddress(address?.[deleteBtmSheet - 1])}
            description={`Phone: ${address?.[deleteBtmSheet - 1]?.phone}`}
            onClose={() => {
              setDeleteBtmSheet(null);
            }}
            onDelete={handleDeleteAddress}
            // onDeleteAddress={() => {
            //   removeFromAddressList(
            //     address?.[selectedAddressOptionIndex]?.id,
            //   );
            // }}
            // onCloseBtmSheet={() => {
            //   closeDeleteAddressBtmSheetRef();
            //   setSelectedAddressOptionIndex(null);
            // }}
          />
        </BottomSheet>
      </>
    );
  };

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          bottomSheetContent={bottomSheetContent}
          children={_ => {
            return (
              <TouchableWithoutFeedback
                style={styles.container}
                onPress={() => {
                  setMenuInfo(null);
                }}>
                <View style={styles.container}>
                  {address && address?.length > 0 ? (
                    <>
                      <FlatList
                        data={address}
                        ref={flatListRef}
                        renderItem={renderAddressItem}
                        keyExtractor={(item, index) => `address-${index}`}
                        showsVerticalScrollIndicator={false}
                        pointerEvents={menuInfo ? 'none' : 'auto'}
                        bounces={false}
                        ListHeaderComponent={renderListHeaderComponent}
                        contentContainerStyle={styles.contentContainerStyle}
                      />
                      {menuInfo ? (
                        <View style={[styles.dropdown, {top: menuInfo?.y}]}>
                          {generateMenuList(config?.dropDown)?.map(
                            (item: any) => {
                              return (
                                <TouchableOpacity
                                  style={[styles.dropdownOption]}
                                  onPress={item?.onPress}>
                                  <JioIcon {...item?.icon} />
                                  <JioText
                                    appearance={JioTypography.BODY_XS}
                                    {...item?.label}
                                  />
                                </TouchableOpacity>
                              );
                            },
                          )}
                        </View>
                      ) : null}

                      <View
                        style={[
                          styles.bottomBtnView,
                          {paddingBottom: insets.bottom + 10},
                        ]}>
                        <JioButton
                          title=""
                          size={ButtonSize.LARGE}
                          stretch
                          kind={ButtonKind.SECONDARY}
                          state={
                            showAlertAddressBook
                              ? ButtonState.DISABLED
                              : ButtonState.NORMAL
                          }
                          {...config?.addAddressButton}
                          style={styles.addButton}
                          onClick={() => {
                            handleAddAddres();
                            // checkLocationPermission({
                            //   onGranted: async () => {
                            //     handleOpenMap()
                            //     console.warn('Permission Granted');
                            //   },
                            //   onLimited: async () => {
                            //     handleOpenMap()
                            //     console.warn('Permission Limited');
                            //   },
                            //   onBlocked: () => {
                            //     handleAddAddres()
                            //     console.warn('Permission blocked, suggest opening settings');
                            //   },
                            //   onDenied: () => {
                            //     handleAddAddres()
                            //     console.warn('Permission denied by user');
                            //   },
                            //   onUnavailable: () => {
                            //     handleAddAddres()
                            //     console.error('Location permission not available');
                            //   },
                            // })
                          }}
                        />
                      </View>
                    </>
                  ) : (
                    <NegativeScreenUI
                      {...config?.negativeCases?.emptyAddress}
                      onPress={handleEmptyAddress}
                    />
                  )}
                </View>
              </TouchableWithoutFeedback>
            );
          }}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {backgroundColor: '#ffffff', flex: 1},
  addButton: {
    marginHorizontal: 24,
  },
  bottomBtnView: {
    borderTopWidth: 1,
    paddingVertical: 10,
    borderColor: '#E0E0E0',
    zIndex: -1,
  },
  contentContainerStyle: {
    rowGap: 16,
    marginHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
  message: {
    marginTop: 16,
  },
  typeView: {
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginLeft: 12,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typeViewText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#141414',
    fontFamily: 'JioType-Medium',
    marginRight: 8,
    marginLeft: 8,
  },
  rightButtonStyle: {
    position: 'absolute',
    right: 0,
  },

  dropdown: {
    backgroundColor: '#ffffff',
    position: 'absolute',
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.1,
        shadowRadius: 16,
      },
      android: {
        elevation: 1,
      },
    }),
    borderRadius: 8,
    top: 30,
    right: 32,
  },
  dropdownOption: {
    paddingLeft: 16,
    paddingRight: 6,
    paddingVertical: 8,
    flexDirection: 'row',
    columnGap: 12,
    width: rw(183),
  },
});

export default JMAddressListScreen;
