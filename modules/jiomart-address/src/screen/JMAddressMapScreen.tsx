import React from 'react';
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import type {JMAddressMapScreenProps} from '../types/JMAddressMapScreenType';
import useJMAddressMapScreenController from '../controller/useJMAddressMapScreenController';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import {rh} from '../../../jiomart-common/src/JMResponsive';
import JMSearchBar from '../../../jiomart-general/src/ui/JMSearchBar';
import Divider, {
  DividerGap,
  DividerType,
} from '../../../jiomart-general/src/ui/Divider';
import MapView, {Marker} from 'react-native-maps';
import JMSearchSuggestionItem from '../../../jiomart-general/src/ui/JMSearchSugestionItem';
import {JioButton, JioIcon, JioText} from '@jio/rn_components';
import {ButtonState} from '@jio/rn_components/src/index.types';
import JMMessageBox from '../../../jiomart-general/src/ui/JMMessageBox';
import JMAddressLocationShimmer from '../components/shimmer/JMAddressLocationShimmer';
import {
  MapLocationMarker,
  MapTooltipNegative,
  MapTooltipPositive,
} from '../assets/icons';

const JMAddressMapScreen = (props: JMAddressMapScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    isFetchingLocation,
    animateCamera,
    mapViewRef,
    regionCamera,
    location,
    search,
    handleChangeText,
    suggestion,
    currentAddress,
    insets,
    isMoving,
    isLocationLoading,
    onRegionChange,
    onRegionChangeComplete,
    onMapReady,
    handleSearchSuggestionClick,
    suggestionClick,
    setSuggestionClick,
    handleConfirmLocation,
  } = useJMAddressMapScreenController(props);

  const renderItemSeperator = () => (
    <Divider
      type={DividerType.THIN}
      horizontal={DividerGap.GAP24}
      top={DividerGap.GAP12}
    />
  );
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          children={() => (
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
              <View style={{ ...styles.container }}>
                {isLocationLoading &&
                  <View style={styles.loaderContainer}>
                    <ActivityIndicator size="small" />
                  </View>
                }

                <>
                  <View style={styles.locationSearchBar}>
                    <JMSearchBar
                      style={styles.search}
                      textInput={{
                        enablesReturnKeyAutomatically: true,
                        value: search,
                        onChangeText: handleChangeText,
                        placeholder: config?.searchBar?.placeholder,
                        onFocus: () => {
                          setSuggestionClick(false);
                        },
                      }}
                    />
                    {search && !suggestionClick && suggestion?.length > 0 ? (
                      <FlatList
                        data={suggestion}
                        bounces={false}
                        renderItem={({ item }) => {
                          return (
                            <JMSearchSuggestionItem
                              icon={{ ...config?.searchSuggestion?.icon }}
                              title={{
                                ...config?.searchSuggestion?.title,
                                text: item?.primaryText,
                              }}
                              subTitle={{
                                ...config?.searchSuggestion?.subTitle,
                                text: item?.description,
                              }}
                              onPress={handleSearchSuggestionClick(item?.placeID)}
                              style={styles.searchSuggestion}
                            />
                          );
                        }}
                        ItemSeparatorComponent={renderItemSeperator}
                        contentContainerStyle={{ marginBottom: 8 }}
                      />
                    ) : null}
                  </View>

                  <View style={styles.mapContainer}>
                    <MapView
                      ref={mapViewRef}
                      {...config?.map}
                      initialCamera={regionCamera}
                      style={styles.map}
                      onMapReady={onMapReady}
                      onRegionChange={onRegionChange}
                      onRegionChangeComplete={onRegionChangeComplete}>
                      <Marker
                        draggable
                        tappable
                        opacity={0}
                        coordinate={{
                          latitude: location.latitude,
                          longitude: location.longitude,
                        }}
                      />
                    </MapView>
                    <TouchableOpacity
                      style={styles.fabIcon}
                      onPress={animateCamera}
                      activeOpacity={1}>
                      <View style={styles.cameraFocus}>
                        {isFetchingLocation ? (
                          <ActivityIndicator {...config?.cameraFocus?.loader} />
                        ) : (
                          <JioIcon {...config?.cameraFocus?.icon} />
                        )}
                      </View>
                    </TouchableOpacity>

                    <View style={[styles.markerFixed]} pointerEvents="none">
                      {currentAddress ? (
                        <MapTooltipPositive />
                      ) : (
                        <MapTooltipNegative />
                      )}
                      <MapLocationMarker />
                    </View>
                  </View>

                  <View
                    style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
                    {isMoving ? (
                      <JMAddressLocationShimmer />
                    ) : currentAddress ? (
                      <>
                        <JioText {...config?.addressBox?.title} />
                        <JMSearchSuggestionItem
                          icon={{ ...config?.addressBox?.address?.icon }}
                          subTitle={{
                            ...config?.addressBox?.address?.subTitle,
                            text: currentAddress?.address,
                          }}
                        />
                      </>
                    ) : (
                      <JMMessageBox
                        {...config?.alert?.invalidLocation}
                        style={{ marginBottom: 4 }}
                      />
                    )}
                    <JioButton
                      {...config?.addressBox?.address?.button}
                      state={
                        !currentAddress || isMoving
                          ? ButtonState.DISABLED
                          : ButtonState.NORMAL
                      }
                      onClick={handleConfirmLocation}
                    />
                  </View>
                </>

              </View>
            </TouchableWithoutFeedback>
          )}
        />
      )}
    />
  );
};

export default JMAddressMapScreen;

const styles = StyleSheet.create({
  map: {
    flex: 1,
    width: '100%',
  },
  cameraFocus: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  locationSearchBar: {
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    position: 'absolute',
    zIndex: 50,
  },

  fabIcon: {
    width: 56,
    height: 56,
    borderRadius: 45,
    position: 'absolute',
    bottom: '30%',
    right: 10,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  break: {
    height: rh(8),
    width: '100%',
    backgroundColor: '#F5F5F5',
  },
  container: {
    justifyContent: 'flex-start',
    flex: 1,
    flexDirection: 'column',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 1,
  },
  markerFixed: {
    marginTop: -100,
    position: 'absolute',
    top: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  footer: {
    backgroundColor: '#fff',
    bottom: 0,
    position: 'absolute',
    width: '100%',
    paddingTop: 16,
    paddingHorizontal: 24,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    rowGap: 8,
  },
  region: {
    color: '#fff',
    lineHeight: 20,
    margin: 20,
  },
  search: {
    marginHorizontal: 24,
    marginTop: 16,
    marginBottom: 8,
  },
  searchSuggestion: {
    marginHorizontal: 24,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    zIndex: 999,
  },
});
