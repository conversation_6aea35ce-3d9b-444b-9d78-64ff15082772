import React from 'react';
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  Platform,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import useJMAddressSearchScreenController from '../controller/useJMAddressSearchScreenController';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import type {JMAddressSearchScreenProps} from '../types/JMAddressSearchScreenType';
import {JioIcon, JioText} from '@jio/rn_components';
import Divider, {
  DividerGap,
  DividerType,
} from '../../../jiomart-general/src/ui/Divider';
import JMSearchBar from '../../../jiomart-general/src/ui/JMSearchBar';
import JMSearchSuggestionItem from '../../../jiomart-general/src/ui/JMSearchSugestionItem';
import {rw} from '../../../jiomart-common/src/JMResponsive';

const JMAddressSearchScreen = (props: JMAddressSearchScreenProps) => {
  const {
    navigationBean,
    navigation,
    config,
    addressListConfig,
    handleSearchSuggestionClick,
    search,
    handleChangeText,
    suggestion,
    isFetchingLocation,
    handleCurrentLocation,
    suggestionClick,
    setSuggestionClick,
  } = useJMAddressSearchScreenController(props);

  // const {
  //   card,
  //   showAlertAddressBook,
  //   address,
  //   menuInfo,
  //   setMenuInfo,
  //   iconRefs,
  //   onIconPress,
  //   flatListRef,
  //   generateMenuList,
  //   deleteBtmSheet,
  //   setDeleteBtmSheet,
  //   handleDeleteAddress,
  // } = useJMAddressListScreenController(props);

  const renderItemSeperator = () => (
    <Divider
      type={DividerType.THIN}
      horizontal={DividerGap.GAP24}
      top={DividerGap.GAP12}
    />
  );

  // const renderAddressItem = useCallback(
  //   ({ item, index }: { item: JMAddressModel; index: number }) => {
  //     return (
  //       <View pointerEvents={menuInfo ? 'none' : 'auto'}>
  //         <JMAddressCard
  //           onRef={ref => {
  //             if (ref) {
  //               iconRefs.current[`${item?.id}_${index}`] = ref;
  //             }
  //           }}
  //           key={`address-${index}`}
  //           name={{
  //             ...card?.name,
  //             text: item?.name ?? '',
  //           }}
  //           type={{
  //             ...card?.type,
  //             text: item?.address_type ?? '',
  //           }}
  //           icon={{
  //             ...card?.icon,
  //           }}
  //           address={{
  //             ...card?.address,
  //             text: getFormatterAddress(item, card?.hideAddress) ?? '',
  //           }}
  //           phone={{
  //             ...card?.phone,
  //             text: card?.phone?.text?.replace('[TEXT]', item?.phone),
  //           }}
  //           subText={{
  //             ...card?.subText,
  //             text: item?.is_default_address ? card?.subText?.text : '',
  //           }}
  //           onIconPress={() =>
  //             onIconPress(`${item.id}`, index, item?.is_default_address)
  //           }
  //           isDefault={item?.is_default_address}
  //         />
  //       </View>
  //     );
  //   },
  //   [
  //     card?.address,
  //     card?.hideAddress,
  //     card?.icon,
  //     card?.name,
  //     card?.phone,
  //     card?.subText,
  //     card?.type,
  //     iconRefs,
  //     menuInfo,
  //     onIconPress,
  //   ],
  // );

  // const renderListHeaderComponent = () => {
  //   return (
  //     <>
  //       <JioText {...config?.addressListHeaderTitle}
  //         text='Your Saved Addresses'
  //         appearance='overline'
  //         color='primary_grey_60' />
  //       {showAlertAddressBook ? (
  //         <JMMessageBox
  //           {...addressListConfig?.alert?.addressBook}
  //           style={styles.message}
  //         />
  //       ) : null}
  //     </>
  //   );
  // };

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          // bottomSheetContent={
          //   <>
          //     <BottomSheet
          //       onBackDropClick={() => {
          //         setDeleteBtmSheet(null);
          //       }}
          //       onDrag={() => {
          //         setDeleteBtmSheet(null);
          //       }}
          //       visible={deleteBtmSheet}>
          //       <JMDeleteAddressBtmSheet
          //         config={addressListConfig?.bottomSheet?.delete}
          //         name={address?.[deleteBtmSheet - 1]?.name}
          //         address={getFormatterAddress(address?.[deleteBtmSheet - 1])}
          //         description={`Phone: ${address?.[deleteBtmSheet - 1]?.phone}`}
          //         onClose={() => {
          //           setDeleteBtmSheet(null);
          //         }}
          //         onDelete={handleDeleteAddress}
          //       />
          //     </BottomSheet>
          //   </>
          // }
          children={_ => {
            return (
              <TouchableWithoutFeedback
                onPress={() => {
                  Keyboard.dismiss;
                  // setMenuInfo(null)
                }}>
                <View style={styles.container}>
                  <JioText
                    style={{marginTop: 16, marginHorizontal: 24}}
                    {...config?.headerTitle}
                  />
                  <JMSearchBar
                    style={styles.search}
                    textInput={{
                      value: search,
                      onChangeText: val => {
                        handleChangeText(val);
                      },
                      onFocus: () => {
                        setSuggestionClick(false);
                      },
                      placeholder: config?.searchBar?.placeholder,
                    }}
                  />
                  {search && !suggestionClick && suggestion?.length > 0 ? (
                    <FlatList
                      data={suggestion ?? []}
                      bounces={false}
                      renderItem={({item}) => {
                        return (
                          <JMSearchSuggestionItem
                            icon={{...config?.searchSuggestion?.icon}}
                            title={{
                              ...config?.searchSuggestion?.title,
                              text: item?.primaryText,
                            }}
                            subTitle={{
                              ...config?.searchSuggestion?.subTitle,
                              text: item?.description,
                            }}
                            onPress={handleSearchSuggestionClick(item?.placeID)}
                            style={styles.searchSuggestion}
                          />
                        );
                      }}
                      ItemSeparatorComponent={renderItemSeperator}
                    />
                  ) : (
                    <>
                      <Divider
                        text="OR"
                        type={DividerType.THIN}
                        right={DividerGap.GAP16}
                        left={DividerGap.GAP24}
                        top={DividerGap.GAP12}
                      />
                      {isFetchingLocation ? (
                        <ActivityIndicator
                          style={styles.loader}
                          {...config?.location?.loader}
                        />
                      ) : (
                        <TouchableOpacity
                          onPress={handleCurrentLocation}
                          activeOpacity={1}>
                          <View style={styles.location}>
                            <JioIcon {...config?.location?.icon} />
                            <View>
                              <JioText {...config?.location?.title} />
                              <JioText {...config?.location?.subTitle} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      )}

                      {/* <>

                        <View style={{ height: 20 }} />

                        {address && address?.length > 0 ? (
                          <>
                            <FlatList
                              data={address}
                              ref={flatListRef}
                              renderItem={renderAddressItem}
                              keyExtractor={(item, index) => `address-${index}`}
                              showsVerticalScrollIndicator={false}
                              pointerEvents={menuInfo ? 'none' : 'auto'}
                              bounces={false}
                              ListHeaderComponent={renderListHeaderComponent}
                              contentContainerStyle={styles.contentContainerStyle}
                            />
                            {menuInfo ? (
                              <View style={[styles.dropdown, { top: menuInfo?.y }]}>
                                {generateMenuList(addressListConfig?.dropDown)?.map(
                                  (item: any) => {
                                    return (
                                      <TouchableOpacity
                                        style={[styles.dropdownOption]}
                                        onPress={item?.onPress}>
                                        <JioIcon {...item?.icon} />
                                        <JioText
                                          appearance={JioTypography.BODY_XS}
                                          {...item?.label}
                                        />
                                      </TouchableOpacity>
                                    );
                                  },
                                )}
                              </View>
                            ) : null}
                          </>
                        ) : (
                          <></>
                        )}
                      </> */}
                    </>
                  )}
                </View>
              </TouchableWithoutFeedback>
            );
          }}
        />
      )}
    />
  );
};

export default JMAddressSearchScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  title: {marginTop: 16, marginHorizontal: 24},
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  location: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 12,
    paddingTop: 12,
    paddingBottom: 6,
    marginHorizontal: 24,
  },
  search: {
    marginHorizontal: 24,
    marginTop: 12,
    marginBottom: 16,
  },
  searchSuggestion: {
    marginHorizontal: 24,
  },
  loader: {
    alignSelf: 'flex-start',
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  contentContainerStyle: {
    rowGap: 16,
    marginHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
  dropdown: {
    backgroundColor: '#ffffff',
    position: 'absolute',
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.1,
        shadowRadius: 16,
      },
      android: {
        elevation: 1,
      },
    }),
    borderRadius: 8,
    top: 30,
    right: 32,
  },
  dropdownOption: {
    paddingLeft: 16,
    paddingRight: 6,
    paddingVertical: 8,
    flexDirection: 'row',
    columnGap: 12,
    width: rw(183),
  },
  bottomBtnView: {
    borderTopWidth: 1,
    paddingVertical: 10,
    borderColor: '#E0E0E0',
    zIndex: -1,
  },
  addButton: {
    marginHorizontal: 24,
  },
  message: {
    marginTop: 16,
  },
});
