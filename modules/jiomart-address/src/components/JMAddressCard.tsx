import React from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {JioText} from '@jio/rn_components';
import {
  JioTypography,
  type JioButtonProps,
  type JioIconProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {StyleProp} from 'react-native';
import {ViewStyle} from 'react-native';
import {JioButton} from '@jio/rn_components';
import {JioIcon} from '@jio/rn_components';

export interface JMAddressCardProps {
  onRef?: (ref: any) => any;
  style?: StyleProp<ViewStyle>;
  iconStyle?: StyleProp<ViewStyle>;
  name?: JioTextProps;
  type?: JioTextProps;
  icon?: JioIconProps;
  address?: JioTextProps;
  phone?: JioTextProps;
  subText?: JioTextProps;
  isDefault?: boolean;
  enableButton?: boolean;
  button?: JioButtonProps;
  onIconPress?: () => void;
  onButtonPress?: () => void;
  onPress?: () => void;
}

const JMAddressCard = ({
  style,
  iconStyle,
  name,
  type,
  icon,
  address,
  phone,
  subText,
  isDefault,
  enableButton = false,
  button,
  onIconPress,
  onButtonPress,
  onPress,
  onRef,
}: JMAddressCardProps) => {
  return (
    <TouchableOpacity
      ref={onRef}
      style={[style, styles.container]}
      activeOpacity={1}
      onPress={onPress}
      onLongPress={() => {}}>
      <View style={styles.addressContainer}>
        <View style={styles.nameContainer}>
          <JioText
            style={styles.name}
            maxLines={2}
            appearance={JioTypography.BODY_XS_BOLD}
            color="primary_grey_100"
            {...name}
          />
          {type?.text ? (
            <View style={[styles.type, isDefault ? styles.defaultType : null]}>
              <JioText
                appearance={JioTypography.BODY_XXS_BOLD}
                maxLines={1}
                color={isDefault ? 'primary_inverse' : 'black'}
                {...type}
              />
            </View>
          ) : null}
          {icon?.ic ? (
            <TouchableOpacity
              style={[styles.icon, iconStyle]}
              hitSlop={10}
              onLongPress={() => {}}
              onPress={onIconPress}
              activeOpacity={1}>
              <JioIcon {...icon} />
            </TouchableOpacity>
          ) : null}
        </View>
        <View style={styles.descriptionContainer}>
          <JioText
            appearance={JioTypography.BODY_XXS}
            color="primary_grey_80"
            {...address}
          />
          <JioText
            appearance={JioTypography.BODY_XXS}
            color="primary_grey_80"
            {...phone}
          />
          <JioText
            style={styles.default}
            appearance={JioTypography.BODY_XXS}
            color="primary_grey_60"
            {...subText}
          />
        </View>
        {enableButton ? (
          <JioButton
            title=""
            stretch
            style={styles.button}
            {...button}
            onClick={onButtonPress}
          />
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

export default JMAddressCard;

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: 16,
    borderColor: '#E0E0E0',
    backgroundColor: '#ffff',
  },
  type: {
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    maxWidth: '30%',
    backgroundColor: '#E0E0E0',
  },
  icon: {
    marginLeft: 'auto',
  },
  button: {
    marginTop: 12,
    marginBottom: 4,
  },
  defaultType: {backgroundColor: '#0C5273'},
  addressContainer: {
    flex: 1,
    position: 'relative',
    padding: 12,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    marginRight: 12,
    maxWidth: '60%',
  },
  default: {
    marginTop: 8,
  },
  descriptionContainer: {
    marginRight: 52,
    marginTop: 4,
  },
});
