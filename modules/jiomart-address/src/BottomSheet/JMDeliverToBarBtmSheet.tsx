import {<PERSON>o<PERSON><PERSON><PERSON>, <PERSON>o<PERSON>con, JioText} from '@jio/rn_components';
import {
  ButtonSize,
  JioIconProps,
  JioTypography,
  type JioColor,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import React, {useEffect, useState} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {FlatList, TouchableOpacity} from 'react-native-gesture-handler';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {navBeanObj} from '../../../jiomart-common/src/JMNavGraphUtil';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import JMBtmSheetHeader from '../../../jiomart-general/src/ui/JMBtmSheetHeader';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import useAddressList from '../hooks/useAddressList';
import useAddressOperation from '../hooks/useAddressOperation';
import useCurrentLocation from '../hooks/useCurrentLocation';
import usePincodeChange from '../hooks/usePincodeChange';
import {getFormatterAddress, getSortedAddressList} from '../utils';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
const enum AddressOptionType {
  PINCODE = 'PINCODE',
  DETECT_MY_LOCATION = 'DETECT_MY_LOCATION',
}

interface AddressOptionProps {
  list: {
    icon: JioIconProps;
    text: JioTextProps;
    onPress: () => void;
  }[];
}

interface AddressBoxProps {
  name: string;
  address: string;
  pincode: string;
  location: string;
  selected?: boolean;
  onPress?: () => void;
}
interface AddAddressBoxProps {
  icon: JioIconProps;
  text: JioTextProps;
  onPress: () => void;
}

export interface JMDeliverScrollViewInterface {
  scrollToSlide: () => void;
}

interface JMDeliverToBarBtmSheetProps extends BottomSheetChildren {
  onClose?: () => void;
  openDeliverToBarPincode: () => void;
}

const JMDeliverToBarBtmSheet = (props: JMDeliverToBarBtmSheetProps) => {
  const {onClose, openDeliverToBarPincode, close} = props;
  const insets = useSafeAreaInsets();
  // const config = CONFIG?.bottomSheet?.deliverToBar;
  const config = useConfigFile(JMConfigFileName.JMAddressConfigurationFileNAme)
    ?.bottomSheet?.deliverToBar;
  const {fetchLocationFromReverseGeoCodeFromLatLong, checkLocationPermission} =
    useCurrentLocation({
      alertBlocked: config?.alert?.blockedLocation,
    });
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const {
    setDefaultAddress,
    generateDefaultAddressRequestBody,
    checkAndSetPincode,
  } = useAddressOperation();
  const addressData = useAddressList();
  const [pincode, setPincode] = useState(null);
  const {userData} = useUserProfile();
  const [defaultAddressId, setDefaultAddressId] = useState(
    userData?.preferred_shipping_address,
  );

  const address = addressData;

  const reorderAddresses = (
    addressList: JMAddressModel[],
    currentPincode: string,
  ) => {
    const index = addressList?.findIndex(ad => ad.pin === currentPincode);
    if (index !== -1) {
      const [selectedAddress] = addressList?.splice(index, 1);
      addressList?.unshift(selectedAddress);
    }
    return addressList;
  };

  const generateAddressOption = (option: any) => {
    return option
      ?.map((item: any) => {
        if (!item?.isVisible) {
          return null;
        }
        switch (item?.type) {
          case AddressOptionType.DETECT_MY_LOCATION:
            return {
              ...item,
              onPress: handleDetectMyLocation,
            };
          case AddressOptionType.PINCODE:
            return {
              ...item,
              onPress: handlePincode,
            };
          default:
            return item;
        }
      })
      ?.filter((item: any) => item != null);
  };
  const generateAddAddressBox = (data: any) => ({
    text: data?.text,
    icon: data?.icon,
    onPress: async () => {
      close?.(onClose);

      navigateTo(
        navBeanObj({
          ...config?.addAddressButton?.cta,
        }),
        navigation,
      );
      // await checkLocationPermission({
      //   onGranted: async () => {
      //     navigateTo(
      //       navBeanObj({
      //         ...config?.addAddressButton?.ctaAlt,
      //       }),
      //       navigation,
      //     );
      //   },
      //   onLimited: async () => {
      //     navigateTo(
      //       navBeanObj({
      //         ...config?.addAddressButton?.ctaAlt,
      //       }),
      //       navigation,
      //     );
      //   },
      //   onBlocked: () => {
      //     navigateTo(
      //       navBeanObj({
      //         ...config?.addAddressButton?.cta,
      //       }),
      //       navigation,
      //     );
      //     console.warn('Permission blocked, suggest opening settings');
      //   },
      //   onDenied: () => {
      //     navigateTo(
      //       navBeanObj({
      //         ...config?.addAddressButton?.cta,
      //       }),
      //       navigation,
      //     );
      //     console.warn('Permission denied by user');
      //   },
      //   onUnavailable: () => {
      //     navigateTo(
      //       navBeanObj({
      //         ...config?.addAddressButton?.cta,
      //       }),
      //       navigation,
      //     );
      //     console.error('Location permission not available');
      //   },
      // })
    },
  });

  const handleDetectMyLocation = async () => {
    const stats = await fetchLocationFromReverseGeoCodeFromLatLong();
    checkAndSetPincode({
      pincode: stats?.address?.pin,
      state: stats?.address?.state,
      city: stats?.address?.city,
    });
    close?.(onClose);
  };
  const handlePincode = () => {
    close?.(onClose);
    openDeliverToBarPincode?.();
  };

  const handleAddressBoxPress = (item: JMAddressModel) => {
    return () => {
      try {
        close?.(onClose);
        const request = generateDefaultAddressRequestBody(item);
        setDefaultAddress.mutate(request);
      } catch (error) {
        JMLogger.log(error);
      }
    };
  };
  const handleLoginRedirection = () => {
    close?.(onClose);
    navigateTo(
      navBeanObj({
        ...config?.guestUserBtn?.cta,
      }),
      navigation,
    );
  };

  useEffect(() => {
    JMDatabaseManager.address.getDefaultAddress().then(value => {
      const address = JSON.parse(value || '');
      setPincode(address?.pin);
      if (address?.id != undefined) setDefaultAddressId(address?.id);
    });
  }, []);
  usePincodeChange(() => {
    JMDatabaseManager.address.getDefaultAddress().then(value => {
      const address = JSON.parse(value || '');
      setPincode(address?.pin);
      setDefaultAddressId(address?.id);
    });
  });

  return (
    <View
      style={[styles.addressBtmSheetContainer, {paddingBottom: insets.bottom}]}>
      <JMBtmSheetHeader
        title={config?.title ?? ''}
        onPress={() => {
          close?.(onClose);
        }}
      />
      <JioText
        text={config?.description?.title ?? ''}
        appearance={JioTypography.BODY_XS}
        maxLines={config?.description?.maxLine}
        color={config?.description?.color as JioColor}
        style={styles.margin}
      />
      {!JMSharedViewModel.Instance.loggedInStatus ? (
        config?.guestUserBtn?.isVisible ? (
          <JioButton
            title={config?.guestUserBtn?.title ?? ''}
            stretch={true}
            size={ButtonSize.LARGE}
            style={{margin: 24}}
            onClick={handleLoginRedirection}
          />
        ) : null
      ) : (
        <FlatList
          style={styles.loginAddressContainer}
          contentContainerStyle={styles.loginInternalContainer}
          scrollEnabled={address?.data && address?.data?.length > 1}
          data={getSortedAddressList(addressData.data ?? [], defaultAddressId)}
          renderItem={({item, index}) => {
            return (
              <AddressBox
                key={`address-${index}`}
                name={item?.name?.trim() ?? ''}
                address={getFormatterAddress(
                  item,
                  config?.addressBox?.hideAddress,
                )}
                pincode={item?.pin}
                location={item?.address_type?.trim() ?? ''}
                selected={
                  defaultAddressId == item?.id //? false : item?.is_default_address
                }
                onPress={handleAddressBoxPress(item)}
              />
            );
          }}
          keyExtractor={(item, index) => `address-${item?.id}-${index}`}
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={false}
          ListFooterComponent={
            !address.data ||
            address?.data?.length <= config?.showNoOfAddress ? (
              <AddAddressBox
                {...generateAddAddressBox(config?.addAddressButton)}
              />
            ) : null
          }
        />
      )}

      <AddressOption list={generateAddressOption(config?.options)} />
    </View>
  );
};

const AddAddressBox = (props: AddAddressBoxProps) => {
  const {onPress, icon, text} = props;
  return (
    <TouchableOpacity
      style={styles.addAddressBoxContainer}
      activeOpacity={0.65}
      onPress={onPress}>
      <View style={styles.addAddressPlus}>
        <JioIcon {...icon} />
      </View>
      <JioText {...text} style={{marginVertical: 4}} />
    </TouchableOpacity>
  );
};

const AddressBox = (props: AddressBoxProps) => {
  const {name, address, pincode, location, selected, onPress} = props;
  return (
    <TouchableOpacity
      style={[
        styles.addressBoxContainer,
        selected ? styles.selectedAddress : styles.unSelectedAddress,
      ]}
      activeOpacity={0.65}
      disabled={selected}
      onPress={onPress}>
      <JioText
        text={name}
        maxLines={2}
        appearance={JioTypography.BODY_XS_LINK}
        color={'primary_grey_100'}
        style={{marginBottom: 4, maxWidth: '75%'}}
      />
      <JioText
        text={address}
        appearance={JioTypography.BODY_XXS}
        color={'primary_grey_80'}
        maxLines={4}
      />
      <JioText
        text={pincode}
        appearance={JioTypography.BODY_XXS}
        color={'primary_grey_80'}
      />
      <View
        style={[
          styles.addressBadge,
          selected
            ? styles.selectedAddressBadge
            : styles.unSelectedAddressBadge,
        ]}>
        <JioText
          text={location}
          appearance={JioTypography.BODY_XXS_BOLD}
          color={selected ? 'primary_inverse' : 'black'}
          maxLines={1}
          style={{maxWidth: 100}}
        />
      </View>
    </TouchableOpacity>
  );
};

const AddressOption = (props: AddressOptionProps) => {
  const {list} = props;

  return (
    <View style={{rowGap: 8}}>
      {list?.map((link: any, index: number) => {
        return (
          <TouchableOpacity
            key={`address-option-${index}`}
            style={styles.addressOptionContainer}
            onPress={link?.onPress}
            activeOpacity={0.65}>
            <JioIcon {...link?.icon} />
            <JioText {...link?.text} />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default JMDeliverToBarBtmSheet;

const styles = StyleSheet.create({
  addressBtmSheetContainer: {
    flexDirection: 'column',
    backgroundColor: 'white',
    justifyContent: 'space-between',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  addressBoxContainer: {
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 8,
    borderRadius: 16,
    flex: 1,
    width: rw(154),
  },

  addressBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
    borderRadius: 4,
    marginTop: 'auto',
  },

  unSelectedAddress: {
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.1,
        shadowRadius: 16,
      },
      android: {
        elevation: 5,
      },
    }),
    backgroundColor: '#ffffff',
    marginBottom: 1,
    marginHorizontal: 1,
  },
  selectedAddress: {
    borderWidth: 1,
    borderBlockColor: '#0C5273',
    backgroundColor: '#E5F1F7',
  },

  unSelectedAddressBadge: {
    backgroundColor: '#E0E0E0',
  },
  selectedAddressBadge: {
    backgroundColor: '#0C5273',
  },

  addressOptionContainer: {
    flexDirection: 'row',
    columnGap: 12,
    alignItems: 'center',
    paddingVertical: 8,
    marginHorizontal: 24,
  },
  addAddressBoxContainer: {
    paddingVertical: 36,
    paddingHorizontal: 16,
    flex: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.1,
        shadowRadius: 16,
      },
      android: {
        elevation: 5,
      },
    }),
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    marginVertical: 'auto',
    marginBottom: 1,
    marginHorizontal: 1,
  },
  addAddressPlus: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 25,
    borderColor: '#E0E0E0',
  },
  loginAddressContainer: {
    width: '100%',
  },
  loginInternalContainer: {
    flexDirection: 'row',
    columnGap: 8,
    paddingHorizontal: 24,
    paddingVertical: 24,
  },
  margin: {marginHorizontal: 24},
});
