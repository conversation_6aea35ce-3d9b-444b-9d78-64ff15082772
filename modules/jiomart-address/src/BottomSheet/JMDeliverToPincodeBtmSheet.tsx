import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JioText} from '@jio/rn_components';
import {
  ButtonState,
  JioTypography,
  type JioColor,
} from '@jio/rn_components/src/index.types';
import {useEffect, useRef, useState} from 'react';
import {
  Keyboard,
  NativeSyntheticEvent,
  StyleSheet,
  TextInputFocusEventData,
  View,
} from 'react-native';
import {TextInput} from 'react-native-gesture-handler';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import {capitalizeFirstLetter} from '../../../jiomart-common/src/utils/JMStringUtility';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import JMBtmSheetHeader from '../../../jiomart-general/src/ui/JMBtmSheetHeader';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useAddressOperation from '../hooks/useAddressOperation';

interface JMDeliverToPincodeBtmSheetProps extends BottomSheetChildren {
  hideCloseButton: boolean;
  onClose?: () => void;
}

const JMDeliverToPincodeBtmSheet = (props: JMDeliverToPincodeBtmSheetProps) => {
  const {close, onClose} = props;
  const insets = useSafeAreaInsets();
  // const config = CONFIG?.bottomSheet?.deliverToBarPincode;
  const config: any = useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.bottomSheet?.deliverToBarPincode;
  const {getPincodeStatus, checkAndSetPincode} = useAddressOperation();

  const [pincode, setPincode] = useState('');
  const [isValidPincodeMessage, setIsValidPincodeMessage] = useState('');
  const [isValidPincode, setIsValidPincode] = useState<boolean | null>(null);
  let isButtonClicked = false;

  const pincodeInputRef = useRef<TextInput>(null);

  const iconValidation = isValidPincode
    ? config?.successIcon
    : config?.errorIcon;

  const enterPincode = async (text: string) => {
    setPincode(text);
    if (text.length < 6) {
      setIsValidPincodeMessage('');
      setIsValidPincode(null);
    }
    if (text.length === 6) {
      getPincodeStatus.mutate(text, {
        onSuccess: response => {
          if (response?.success || response.status === 'success') {
            console.log('🚀 ~ return ~ response:', response?.result);
            const city = capitalizeFirstLetter(
              response?.data
                ? response?.data?.[0]?.parents
                    ?.find((it: any) => it?.sub_type === 'city')
                    ?.name?.replace(/_/g, ' ')
                : response?.result?.city?.replace(/_/g, ' '),
            );
            const state = capitalizeFirstLetter(
              response?.data
                ? response?.data?.[0]?.parents
                    ?.find((it: any) => it?.sub_type === 'state')
                    ?.name?.replace(/_/g, ' ')
                : response?.result?.state_name?.replace(/_/g, ' '),
            );

            setIsValidPincodeMessage(`${city}, ${state}`);
            setIsValidPincode(true);
          } else {
            setIsValidPincodeMessage(config?.message?.invalidPincode);
            setIsValidPincode(false);
          }
        },
        onError: error => {
          setIsValidPincodeMessage(
            error?.response?.reason?.reason_eng ??
              config?.message?.pincodeChangeFailed,
          );
          setIsValidPincode(false);
        },
      });
    }
  };

  const resetPincodeInputField = () => {
    close?.(onClose);
  };

  const applyPincode = () => {
    try {
      isButtonClicked = true;
      console.log(
        '🚀 ~ applyPincode ~ pincodeInputRef.current?.isFocused:',
        pincodeInputRef.current?.isFocused(),
      );
      checkAndSetPincode({
        pincode,
        city: isValidPincodeMessage?.split(',')?.[0],
        state: isValidPincodeMessage?.split(',')?.[1],
      });
      close?.(() => {
        onClose?.();
      }); // Close bottom sheet
    } catch (error) {}
  };

  useEffect(() => {
    setTimeout(() => {
      pincodeInputRef.current?.focus();
    }, 300);
  }, []);

  const onBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    console.log('🚀 ~ onBlur ~ isButtonClicked:', isButtonClicked);
    if (isButtonClicked) {
      setTimeout(() => {
        Keyboard.dismiss();
        checkAndSetPincode({
          pincode,
          city: isValidPincodeMessage?.split(',')?.[0],
          state: isValidPincodeMessage?.split(',')?.[1],
        });
        close?.(onClose); // Close bottom sheet
      }, 100);
    }
  };

  return (
    <View style={{paddingBottom: insets.bottom}}>
      <JMBtmSheetHeader
        title={config?.title ?? ''}
        onPress={resetPincodeInputField}
        hideClose={true}
      />
      <JioText
        text={config?.description?.title ?? ''}
        style={styles.margin}
        color={config?.description?.color as JioColor}
        appearance={JioTypography.BODY_XS}
      />

      <View style={styles.container}>
        <View style={{justifyContent: 'center'}}>
          <JioText
            {...config?.textInput?.label}
            appearance={JioTypography.BODY_XS}
          />
          <View style={styles.textInputContainer}>
            <JioIcon {...config?.textInput?.icon} />
            <TextInput
              ref={pincodeInputRef}
              textContentType="postalCode"
              placeholder={config?.textInput?.placeholder}
              style={styles.textInput}
              keyboardType={'number-pad'}
              keyboardAppearance={'default'}
              editable
              enablesReturnKeyAutomatically
              value={pincode}
              // onBlur={onBlur}
              onChangeText={enterPincode}
              autoComplete={'postal-code'}
              inputMode={'numeric'}
              maxLength={6}
              contextMenuHidden={true}
              selectionColor="#000000A6"
              returnKeyType="done"
              onSubmitEditing={({nativeEvent}) => {
                console.log('Return pressed:', nativeEvent.text);
                if (isValidPincode) {
                  applyPincode();
                }
              }}
            />
            <JioButton
              title={config?.button?.apply?.text ?? ''}
              state={isValidPincode ? ButtonState.NORMAL : ButtonState.DISABLED}
              onClick={applyPincode}
            />
          </View>
          <View
            style={[
              styles.border,
              isValidPincode !== null
                ? isValidPincode
                  ? styles.valid
                  : styles.invalid
                : null,
            ]}
          />

          {pincode && isValidPincode !== null ? (
            <View style={{minHeight: rh(24), flexDirection: 'row'}}>
              <JioIcon
                {...iconValidation}
                style={[styles.marginTop, styles.marginRight]}
              />
              <JioText
                text={isValidPincodeMessage}
                maxLines={2}
                appearance={JioTypography.BODY_XXS}
                style={styles.marginTop}
                color={
                  isValidPincode !== null
                    ? isValidPincode
                      ? 'feedback_success_80'
                      : 'feedback_error_80'
                    : 'primary_inverse'
                }
              />
            </View>
          ) : null}
        </View>
      </View>
    </View>
  );
};

export default JMDeliverToPincodeBtmSheet;

const styles = StyleSheet.create({
  keyboardAvoidingContainer: {
    flexDirection: 'column',
    backgroundColor: 'white',
    justifyContent: 'space-between',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  flex: {flex: 1},
  margin: {marginHorizontal: 24},
  container: {
    flexDirection: 'row',
    margin: 24,
  },
  textInputContainer: {
    flexDirection: 'row',
    columnGap: 8,
    marginBottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    // height: rh(40),
    fontSize: 16,
    // lineHeight: 24,
    letterSpacing: -0.05,
    fontFamily: 'JioType',
    fontStyle: 'normal',
    fontWeight: '500',
  },
  textInput: {
    width: rw(225),
    textDecorationColor: '#141414',
    color: 'black',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: -0.05,
    fontFamily: 'JioType',
    fontStyle: 'normal',
    fontWeight: '500',
  },
  border: {height: 2, backgroundColor: '#000000A6', width: rw(242)},
  valid: {backgroundColor: '#25AB21'},
  invalid: {backgroundColor: '#F50031'},
  marginTop: {marginTop: 4},
  marginRight: {marginRight: 4},
});
