import { useEffect } from 'react';
import useAddressOperation from '../hooks/useAddressOperation';
import type { UseJMAddressFormScreenProps } from '../types/JMAddressFormScreenType';
import { AppScreens } from '../../../jiomart-common/src/JMAppScreenEntry';
import { navBeanObj } from '../../../jiomart-common/src/JMNavGraphUtil';
import useFormHandler from '../../../jiomart-general/src/hooks/useFormHandler';
import { useConfigFile } from '../../../jiomart-general/src/hooks/useJMConfig';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import { navigateTo } from '../../../jiomart-general/src/navigation/JMNavGraph';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import { JMAddressModel } from '../../../jiomart-common/src/uiModals/JMAddressModel';

export const enum AddressFormField {
  ID = 'id',
  HOUSE_NO = 'flat_or_house_no',
  FLOOR_NO = 'floor_no',
  TOWER_NO = 'tower_no',
  BUILDING_APARTMENT_NAME = 'area',
  ADDRESS = 'address',
  LANDMARK_AREA = 'landmark',
  PINCODE = 'pincode',
  CITY = 'city',
  STATE = 'state',
  RECEIVER_NAME = 'name',
  RECEIVER_NUMBER = 'phone',
  ADDRESS_TYPE = 'address_type',
}

export const enum AddressType {
  HOME = 'Home',
  WORK = 'Work',
  OTHER = 'Other',
}

const useJMAddressFormScreenController = (
  props: UseJMAddressFormScreenProps,
) => {
  const {route, navigation} = props;
  const config = useConfigFile(JMConfigFileName.JMAddressConfigurationFileNAme)
    ?.screen?.addressForm;
  const address = route.params?.params?.address;
  const regionCamera = {
    ...config?.map?.initialCamera,
    center: {
      latitude: address?.lat,
      longitude: address?.lon,
    },
  };

  const {userData} = useUserProfile();

  const receiverName = address?.id
    ? address?.name
    : userData?.first_name + ' ' + userData?.last_name;
  const receiverNumber = address?.id
    ? address?.phone
    : userData?.phone_numbers?.find(num => num.primary && num.verified)?.phone;

  const {
    saveAddress,
    updateAddress,
    generateSaveAddressRequestBody,
    generateUpdateAddressRequestBody,
  } = useAddressOperation();

  const {
    formData,
    formError,
    handleInputRef,
    handleFormError,
    handleFormData,
    onChangeText,
    validateValue,
    validateForm,
  } = useFormHandler(
    {
      [AddressFormField.ID]: address?.id,
      [AddressFormField.RECEIVER_NAME]: receiverName,
      [AddressFormField.RECEIVER_NUMBER]: receiverNumber ?? '',
      [AddressFormField.ADDRESS_TYPE]: address?.address_type,
      [AddressFormField.ADDRESS]: address?.address,
      [AddressFormField.HOUSE_NO]: address?.flat_or_house_no,
      [AddressFormField.FLOOR_NO]: address?.floor_no,
      [AddressFormField.TOWER_NO]: address?.tower_no,
      [AddressFormField.BUILDING_APARTMENT_NAME]: address?.area,
      [AddressFormField.LANDMARK_AREA]: address?.landmark,
      [AddressFormField.CITY]: address?.city,
      [AddressFormField.STATE]: address?.state,
      [AddressFormField.PINCODE]: address?.pin,
    },
    config?.form,
  );

  const handleSaveAs = (val: string) => {
    return () => {
      handleFormData(AddressFormField.ADDRESS_TYPE)(val);
    };
  };

  const handleRedirection = () => {
    const routes = navigation.getState().routes;

    const screenAIndex = routes.findIndex(
      route => route.name === AppScreens.ADDRESS_LIST_SCREEN,
    );

    if (screenAIndex != -1) {
      navigateTo(
        navBeanObj({
          ...config?.submitButton?.cta,
        }),
        navigation,
      );
    } else {
      navigation.popToTop();
    }
  };

  const handleAddressSubmit = () => {
    try {
      if (!validateForm()) {
        return;
      }

      const payload: JMAddressModel = {
        id: formData[AddressFormField.ID],
        city: formData[AddressFormField.CITY],
        state: formData[AddressFormField.STATE],
        pin: formData[AddressFormField.PINCODE],
        name: formData[AddressFormField.RECEIVER_NAME],
        phone: formData[AddressFormField.RECEIVER_NUMBER],
        address_type: formData[AddressFormField.ADDRESS_TYPE],
        address: formData[AddressFormField.ADDRESS],
        flat_or_house_no: formData[AddressFormField.HOUSE_NO],
        floor_no: formData[AddressFormField.FLOOR_NO],
        tower_no: formData[AddressFormField.TOWER_NO],
        area: formData[AddressFormField.BUILDING_APARTMENT_NAME],
        landmark: formData[AddressFormField.LANDMARK_AREA],
        lat: regionCamera?.center?.latitude,
        lon: regionCamera?.center?.longitude,
        input_mode: 'map',
        is_default_address: true,
      };

      if (formData[AddressFormField.ID]) {
        const request = generateUpdateAddressRequestBody(payload);
        updateAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success) {
              handleRedirection();
            }
          },
        });
      } else {
        const request = generateSaveAddressRequestBody(payload);
        saveAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success) {
              handleRedirection();
            }
          },
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    navigation.setParams({
      ...route.params,
      navTitle: formData[AddressFormField.ID]
        ? config?.header?.editNavTitle
        : route.params.navTitle,
    });
  }, []);

  return {
    ...props,
    address,
    regionCamera,
    config,
    formData,
    formError,
    handleInputRef,
    handleFormError,
    onChangeText,
    validateValue,
    handleAddressSubmit,
    handleSaveAs,
    navigationBean: route.params,
  };
};

export default useJMAddressFormScreenController;
