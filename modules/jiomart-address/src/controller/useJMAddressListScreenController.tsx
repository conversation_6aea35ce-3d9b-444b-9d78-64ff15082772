import { useRef, useState } from 'react';
import { Platform, type FlatList } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useConfigFile } from '../../../jiomart-general/src/hooks/useJMConfig';
import { navigateTo } from '../../../jiomart-general/src/navigation/JMNavGraph';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useAddressList from '../hooks/useAddressList';
import useAddressOperation from '../hooks/useAddressOperation';
import type { UseJMAddressListScreenProps } from '../types/JMAddressListScreenType';
import { navBeanObj, NavigationBean } from '../../../jiomart-common/src/JMNavGraphUtil';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';

const enum DropDownType {
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  MARK_AS_DEFAULT = 'MARK_AS_DEFAULT',
}

const useJMAddressListScreenController = (
  props: UseJMAddressListScreenProps,
) => {
  const { navigation, route } = props;
  const { userData } = useUserProfile();
  const config = useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.screen?.addressList;
  const addressData = useAddressList(true);
  const insets = useSafeAreaInsets();
  const {
    setDefaultAddress,
    removeAddress,
    generateRemoveAddressRequestBody,
    generateDefaultAddressRequestBody,
  } = useAddressOperation();

  const reorderedAddresses = [];
  const defaultAddressId = userData?.preferred_shipping_address;

  for (const addr of addressData.data ?? []) {
    if (addr.id === defaultAddressId) {
      reorderedAddresses.unshift(addr); // insert at the beginning
    } else {
      reorderedAddresses.push(addr);
    }
  }

  const address =
    reorderedAddresses?.slice(0, config?.shouldShowNoOfAddress) ?? [];

  const showAlertAddressBook = address?.length >= config?.shouldShowNoOfAddress;
  const card = true
    ? config?.cards?.addressListCard
    : config?.cards?.orderReviewListCard;

  const [menuInfo, setMenuInfo] = useState<{
    y: number;
    index: number;
    default: boolean;
  } | null>(null);
  const [deleteBtmSheet, setDeleteBtmSheet] = useState<number | null>(null);
  const iconRefs = useRef<{ [key: string]: any }>({});
  const flatListRef = useRef<FlatList<any>>(null);

  const onIconPress = (
    id: string,
    index: number,
    is_default_address: boolean = false,
  ) => {
    const ref = iconRefs.current[`${id}_${index}`];
    if (ref?.measureInWindow) {
      ref.measureInWindow((x: any, y: any) => {
        const updateY = Platform.OS === 'android' ? y - 20 : y - 90;
        setMenuInfo({ y: updateY, index, default: is_default_address });
      });
    }
  };

  const handleEmptyAddress = () => {
    navigateTo(
      navBeanObj({
        ...config?.negativeCases?.emptyAddress?.cta,
      }),
      navigation,
    );
  };
  const handleAddAddres = () => {
    navigateTo(
      navBeanObj({
        ...config?.addAddressButton?.cta,
      }),
      navigation,
    );
  };
  const handleOpenMap = () => {
    // const ctaAlt = {
    //   "navTitle": "Set Delivery Location",
    //   "HeaderType": 1,
    //   "source": "",
    //   "destination": "JMAddressMapScreen",
    //   "actionType": "T001",
    //   "actionUrl": "",
    //   "bundle": ""
    // }
    navigateTo(
      navBeanObj({
        // ...ctaAlt
        ...config?.addAddressButton?.cta,
      }),
      navigation,
    );
  };
  const handleEdit = (cta: NavigationBean) => {
    return () => {
      console.log('🚀 ~ handleEdit ~ cta:', cta);
      if (menuInfo) {
        navigateTo(
          navBeanObj({
            ...cta,
            params: {
              address: address[menuInfo?.index],
              coords: {
                latitude: address[menuInfo?.index]?.lat,
                longitude: address[menuInfo?.index]?.lon,
              },
            },
          }),
          navigation,
        );
        setMenuInfo(null);
      }
    };
  };
  const handleDelete = () => {
    if (menuInfo) {
      setDeleteBtmSheet(menuInfo?.index + 1);
      setMenuInfo(null);
    }
  };
  const handleMarkAsDefault = () => {
    setMenuInfo(null);
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
    if (menuInfo) {
      const request = generateDefaultAddressRequestBody(
        address[menuInfo?.index],
      );
      setDefaultAddress.mutate(request);
    }
  };
  const handleDeleteAddress = () => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
    if (deleteBtmSheet) {
      const request = generateRemoveAddressRequestBody(
        address[deleteBtmSheet - 1],
      );
      removeAddress.mutate(request, {
        onSuccess: res => {
          addressData.refetch()
        },
      });
    }
  };

  const generateMenuList = (config: any) => {
    const sw = menuInfo?.default ? 'default' : 'normal';
    return config?.[sw]
      ?.map((item: any) => {
        if (!item?.isVisible) {
          return;
        }
        switch (item?.type) {
          case DropDownType.EDIT:
            return {
              ...item,
              onPress: handleEdit(item?.cta),
            };
          case DropDownType.DELETE:
            return {
              ...item,
              onPress: handleDelete,
            };
          case DropDownType.MARK_AS_DEFAULT:
            return {
              ...item,
              onPress: handleMarkAsDefault,
            };
          default:
            return;
        }
      })
      ?.filter((item: any) => item != null);
  };

  return {
    config,
    showAlertAddressBook,
    card,
    address,
    handleEmptyAddress,
    handleAddAddres,
    handleOpenMap,
    menuInfo,
    setMenuInfo,
    generateMenuList,
    onIconPress,
    iconRefs,
    insets,
    flatListRef,
    deleteBtmSheet,
    setDeleteBtmSheet,
    handleDeleteAddress,
    ...props,
    navigationBean: route.params,
  };
};

export default useJMAddressListScreenController;
