import {navBeanObj} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getReverseGeoCodeFromLatLongNB} from '../../../jiomart-general/src/bridge/JMRNBridge';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {useEffect, useRef, useState} from 'react';
import {Platform} from 'react-native';
import type {GeoPosition} from 'react-native-geolocation-service';
import type MapView from 'react-native-maps';
import type {Region} from 'react-native-maps';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import useAddressSearchSuggestion from '../hooks/useAddressSearchSuggestion';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type {UseJMAddressMapScreenProps} from '../types/JMAddressMapScreenType';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';

const useJMAddressMapScreenController = (props: UseJMAddressMapScreenProps) => {
  const defaultLocation = {
    latitude: 18.9348965,
    longitude: 72.8161317,
  };
  const {route, navigation} = props;
  let getCurrentLocation = false;
  const config = useConfigFile(JMConfigFileName.JMAddressConfigurationFileNAme)
    ?.screen?.addressMap;
  let location = route.params?.params?.coords;
  if (!location) {
    location = {
      latitude: config?.defaultLocation?.latitude ?? defaultLocation.latitude,
      longitude:
        config?.defaultLocation?.longitude ?? defaultLocation.longitude,
    };
    getCurrentLocation = true;
  } else {
    getCurrentLocation = false;
  }

  useEffect(() => {
    if (getCurrentLocation) {
      animateCamera();
      getCurrentLocation = false;
    } else {
      setIsLocationLoading(false);
    }
  }, [getCurrentLocation]);

  const address = route.params?.params?.address;
  const regionCamera = {
    ...config?.map?.initialCamera,
    center: {
      latitude: location.latitude,
      longitude: location.longitude,
    },
  };

  const insets = useSafeAreaInsets();
  const {isFetchingLocation, fetchCurrentLocation} = useCurrentLocation({
    alertBlocked: config?.alert?.blockedLocation,
  });

  const {search, suggestion, handleChangeText, handleSearchSuggestion} =
    useAddressSearchSuggestion({
      shouldShowNoOfSearchSuggestion: config?.shouldShowNoOfSearchSuggestion,
      predictionFilter: config?.googlePredictionFilter,
    });

  const [currentAddress, setCurrentAddress] = useState<JMAddressModel | null>(
    address,
  );

  const [isLocationLoading, setIsLocationLoading] = useState(true);
  const [isMoving, setIsMoving] = useState(false);
  const [suggestionClick, setSuggestionClick] = useState(false);

  const savedAddress = useRef<JMAddressModel>(address);
  const isInitialRegionChange = useRef(true);
  const mapViewRef = useRef<MapView | null>(null);

  const handleSearchSuggestionClick = (placeId: string) => {
    return async () => {
      setSuggestionClick(true);
      const cameraFocus = await mapViewRef.current?.getCamera();
      const response = await handleSearchSuggestion(placeId);
      if (response?.place?.coordinate && cameraFocus) {
        cameraFocus.center.latitude = response?.place?.coordinate?.latitude;
        cameraFocus.center.longitude = response?.place?.coordinate?.longitude;
        mapViewRef.current?.animateCamera(cameraFocus);
      }
    };
  };
  const animateCamera = async () => {
    try {
      const position = (await fetchCurrentLocation()) as GeoPosition;
      const cameraFocus = await mapViewRef.current?.getCamera();
      if (position?.coords && cameraFocus) {
        cameraFocus.center.latitude = position?.coords?.latitude;
        cameraFocus.center.longitude = position?.coords?.longitude;
        mapViewRef.current?.animateCamera(cameraFocus);
      }
    } catch (error) {
      console.log('🚀 ~ animateCamera ~ error:', error);
    }
  };
  const onRegionChange = () => {
    console.log('🚀 ~ onRegionChange ~ onRegionChange:');
    if (isInitialRegionChange.current) {
      console.log(
        '🚀 ~ onRegionChange ~ skipping initial region change on Android',
      );
      return;
    }
    setIsMoving(true);
  };
  const onRegionChangeComplete = async (region: Region) => {
    try {
      console.log('🚀 ~ onRegionChangeComplete ~ onRegionChangeComplete:');
      if (isInitialRegionChange.current) {
        console.log(
          '🚀 ~ onRegionChangeComplete ~ skipping initial region change on Android',
        );
        isInitialRegionChange.current = false;
        return;
      }
      console.log('🚀 ~ onRegionChangeComplete ~ processing region change');
      setIsLocationLoading(false);
      const lat = region?.latitude ?? 0;
      const long = region?.longitude ?? 0;
      if (lat && long) {
        let latestAddress = await getReverseGeoCodeFromLatLongNB(lat, long);
        latestAddress = JSON.parse(latestAddress ?? '');
        if (latestAddress?.address) {
          setCurrentAddress(prev => {
            return {
              ...savedAddress.current,
              ...prev,
              ...latestAddress,
            };
          });
        } else {
          setCurrentAddress(null);
        }
      }
    } catch (error) {
      console.log('🚀 ~ onRegionChangeComplete ~ error:', error);
      setCurrentAddress(null);
    } finally {
      setIsMoving(false);
    }
  };
  const handleConfirmLocation = () => {
    navigateTo(
      navBeanObj({
        ...config?.addressBox?.address?.button?.cta,
        params: {
          address: currentAddress,
        },
      }),
      navigation,
    );
  };

  const onMapReady = async () => {
    console.log('🚀 ~ onMapReady ~ Map is ready');
    if (Platform.OS === 'android') {
      setTimeout(() => {
        if (mapViewRef.current) {
          mapViewRef.current.getCamera().then(camera => {
            mapViewRef.current?.animateCamera(camera);
          });
        }
      }, 300);
    }
  };

  console.log('navigation bean', route.params);

  return {
    ...props,
    config,
    mapViewRef,
    regionCamera,
    isFetchingLocation,
    location,
    animateCamera,
    search,
    handleChangeText,
    handleSearchSuggestionClick,
    suggestion,
    currentAddress,
    insets,
    isMoving,
    isLocationLoading,
    onRegionChange,
    onRegionChangeComplete,
    onMapReady, // Add the onMapReady handler
    suggestionClick,
    setSuggestionClick,
    handleConfirmLocation,
    navigationBean: route.params,
  };
};

export default useJMAddressMapScreenController;
