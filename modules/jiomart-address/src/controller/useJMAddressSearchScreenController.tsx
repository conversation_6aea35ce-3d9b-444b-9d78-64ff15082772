import { useState } from 'react';
import type { GeoCoordinates } from 'react-native-geolocation-service';
import { navBeanObj } from '../../../jiomart-common/src/JMNavGraphUtil';
import { useConfigFile } from '../../../jiomart-general/src/hooks/useJMConfig';
import { navigateTo } from '../../../jiomart-general/src/navigation/JMNavGraph';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import type { JMAddressModel } from '../../../jiomart-common/src/uiModals/JMAddressModel';
import useAddressSearchSuggestion from '../hooks/useAddressSearchSuggestion';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type { UseJMAddressSearchScreenProps } from '../types/JMAddressSearchScreenType';

const useJMAddressSearchScreenController = (
  props: UseJMAddressSearchScreenProps,
) => {
  const { navigation, route } = props;
  const config = useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.screen?.addressSearch;


  const addressListConfig = useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.data?.screen?.addressList;

  const { isFetchingLocation, fetchLocationFromReverseGeoCodeFromLatLong } =
    useCurrentLocation({
      alertBlocked: config?.alert?.blockedLocation,
    });
  const { search, suggestion, handleChangeText, handleSearchSuggestion } =
    useAddressSearchSuggestion({
      shouldShowNoOfSearchSuggestion: config?.shouldShowNoOfSearchSuggestion,
      predictionFilter: config?.googlePredictionFilter,
    });
  const [suggestionClick, setSuggestionClick] = useState(false);

  const handleSearchSuggestionClick = (placeId: string) => {
    return async () => {
      setSuggestionClick(true);
      const response = await handleSearchSuggestion(placeId);
      if (response) {
        handleChangeText('');
        handleMapRedirection(
          response?.place?.coordinate as GeoCoordinates,
          JSON.parse(response?.address ?? ''),
        );
      }
    };
  };

  const handleCurrentLocation = async () => {
    const { address, coords }: any =
      await fetchLocationFromReverseGeoCodeFromLatLong();
    handleMapRedirection(coords, address);
  };

  const handleMapRedirection = (
    geoCoords: GeoCoordinates,
    address?: JMAddressModel,
  ) => {
    navigateTo(
      navBeanObj({
        ...config?.searchSuggestion?.cta,
        params: { coords: geoCoords, address },
      }),
      navigation,
    );
  };
  return {
    ...props,
    search,
    suggestion,
    handleChangeText,
    handleSearchSuggestionClick,
    handleCurrentLocation,
    isFetchingLocation,
    config,
    addressListConfig,
    suggestionClick,
    setSuggestionClick,
    navigationBean: route.params,
  };
};

export default useJMAddressSearchScreenController;
