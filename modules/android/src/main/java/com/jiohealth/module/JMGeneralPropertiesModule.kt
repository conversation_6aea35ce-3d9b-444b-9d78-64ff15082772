package com.jiohealth.module
 
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.WindowManager
import com.facebook.react.module.annotations.ReactModule
import android.provider.Settings
import com.facebook.react.bridge.*
 
 
@ReactModule(name = "JMGeneralPropertiesModule")
class JMGeneralPropertiesModule(var context: ReactApplicationContext) : ReactContextBaseJavaModule(context) {
    override fun getName(): String {
        return "JMGeneralPropertiesModule"
    }
 
    override fun canOverrideExistingModule(): Boolean {
        return true
    }
 
    @ReactMethod
    fun setResizeMode(flag : Boolean) {
        val activity = currentActivity
        Handler(Looper.getMainLooper()).post {
            if(flag){
                activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
            else{
                activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            }
        }
    }

    @ReactMethod
    fun getDeviceInfo(promise: Promise) {
        try {
            val deviceInfo = Arguments.createMap()
            deviceInfo.putString("device_model", Build.MODEL ?: "")
            deviceInfo.putString("device_name", Build.DEVICE ?: "")
            deviceInfo.putString("device_os", "Android")
            deviceInfo.putString("os_version", Build.VERSION.RELEASE ?: "")
            deviceInfo.putInt("device_type", 2)
            deviceInfo.putString("unique_id", Settings.Secure.getString(
                reactApplicationContext.contentResolver,
                Settings.Secure.ANDROID_ID
            ))
 
            val packageInfo = reactApplicationContext.packageManager.getPackageInfo(
                reactApplicationContext.packageName, 0
            )
            deviceInfo.putString("app_version", packageInfo.versionName)
 
            promise.resolve(deviceInfo)
        } catch (e: Exception) {
            promise.reject("DEVICE_INFO_ERROR", "Failed to get device info", e)
        }
    }
}
 