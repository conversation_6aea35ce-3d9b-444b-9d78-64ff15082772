buildscript {
  // Buildscript is evaluated before everything else so we can't use getExtOrDefault
  def kotlin_version = rootProject.ext.has("kotlinVersion") ? rootProject.ext.get("kotlinVersion") : project.properties["JioBlackRock_kotlinVersion"]
  ext {
    compose_compiler = '1.3.1'         //compiler
    compose_version = '1.6.1'   //compose dependencies
    compose_material3 = '1.0.0-beta02' //material3 release
  }
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    classpath "com.android.tools.build:gradle:8.5.1"
    // noinspection DifferentKotlinGradleVersion
    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
  }
}
def isNewArchitectureEnabled() {
  return rootProject.hasProperty("newArchEnabled") && rootProject.getProperty("newArchEnabled") == "true"
}
if (isNewArchitectureEnabled()) {
  apply plugin: "com.facebook.react"
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties["JioBlackRock_" + name]).toInteger()
}
def getExtOrDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties["JioBlackRock_" + name]
}
apply plugin: "com.android.library"
apply plugin: "kotlin-android"
apply plugin: "kotlin-kapt"
apply plugin: 'kotlin-parcelize'

android {
  compileSdkVersion getExtOrIntegerDefault("compileSdkVersion")
  defaultConfig {
    minSdkVersion getExtOrIntegerDefault("minSdkVersion")
    targetSdkVersion getExtOrIntegerDefault("targetSdkVersion")
  }
  buildTypes {
    release {
      minifyEnabled false
    }
  }
  lintOptions {
    disable "GradleCompatible"
  }
  buildFeatures
          {
            dataBinding = true
            compose =  true
            viewBinding = true
          }
  composeOptions {
     kotlinCompilerExtensionVersion = "1.5.15"
  }
}

repositories {
  mavenCentral()
  google()
}
def kotlin_version = getExtOrDefault("kotlinVersion")
dependencies {
  // For < 0.71, this will be from the local maven repo
  // For > 0.71, this will be replaced by `com.facebook.react:react-android:$version` by react gradle plugin
  //noinspection GradleDynamicVersion
  implementation 'androidx.core:core-ktx:1.12.0'
  implementation 'androidx.appcompat:appcompat:1.6.1'
  implementation 'com.google.android.material:material:1.6.1'
  testImplementation 'junit:junit:4.13.2'
  androidTestImplementation 'androidx.test.ext:junit:1.1.5'
  androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
  implementation "com.facebook.react:react-native:+"
  //implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

  implementation "androidx.activity:activity-ktx:1.6.1"
  implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.5.1"
  implementation "io.coil-kt:coil-compose:2.2.2" // For image loading
  implementation 'androidx.activity:activity-compose:1.6.1'

  implementation "androidx.compose.runtime:runtime-livedata:1.0.0"
  implementation "androidx.datastore:datastore-preferences:1.0.0"

  implementation 'com.caverock:androidsvg-aar:1.4'



// Import the Compose BOM
  implementation platform('androidx.compose:compose-bom:2023.10.01')
  implementation("androidx.compose.ui:ui")
  implementation("androidx.compose.runtime:runtime")
  // Import Material Design 3 library
  implementation 'androidx.compose.material3:material3:1.1.1'
  // Import other Compose libraries without version numbers
  // ..
  implementation 'androidx.compose.foundation:foundation'
  androidTestImplementation(platform("androidx.compose:compose-bom:2023.10.00"))
  androidTestImplementation("androidx.compose.ui:ui-test-junit4")
  debugImplementation("androidx.compose.ui:ui-tooling")
  debugImplementation("androidx.compose.ui:ui-test-manifest")
  implementation("com.google.accompanist:accompanist-pager:0.20.2")
  implementation("com.google.accompanist:accompanist-pager-indicators:0.20.2")
  implementation("androidx.navigation:navigation-compose:2.7.7")
  //JDS
  // def jdsIconVersion = "0.12.9"
  // def jdsVersion = "1.0.18"
  // implementation("com.jio.ds:compose:1.0.18")
  // implementation("com.jio.ds:jdsToken:$jdsVersion")
  // implementation("com.jio.ds:jdsEngine:$jdsVersion")
  // implementation("com.jio.ds:coreToken:$jdsVersion")
  // implementation("com.jio.ds:comps:$jdsVersion")
  // implementation("com.jio.ds:core-icons:$jdsIconVersion")
  // implementation("com.jio.ds:extended-icons:$jdsIconVersion")
  // implementation("com.jio.ds:product-logo-icons:$jdsIconVersion")
  implementation "org.jetbrains.kotlinx:kotlinx-collections-immutable:0.3.6"
  implementation 'com.google.code.gson:gson:2.8.9'
  def camerax_version = "1.3.3"
  //CameraX
  implementation("androidx.camera:camera-core:${camerax_version}")
  implementation("androidx.camera:camera-camera2:${camerax_version}")
  implementation("androidx.camera:camera-lifecycle:${camerax_version}")
  implementation("androidx.camera:camera-video:${camerax_version}")
  implementation("androidx.camera:camera-view:${camerax_version}")
  implementation("androidx.camera:camera-extensions:${camerax_version}")
  implementation("androidx.lifecycle:lifecycle-extensions:2.2.0")

  implementation("com.google.accompanist:accompanist-permissions:0.31.0-alpha")
  implementation "com.google.android.gms:play-services-auth-api-phone:18.1.0"

  /* def voyagerVersion = "1.0.0"
 // Navigator
   implementation("cafe.adriel.voyager:voyager-navigator:$voyagerVersion")
 // Screen Model
   implementation("cafe.adriel.voyager:voyager-screenmodel:$voyagerVersion")*/

  implementation ("com.google.android.gms:play-services-auth:20.7.0") because "Provides Hint picker and SmsConsent"
  implementation ("androidx.biometric:biometric:1.1.0") because "helps with biometric authencatication"
}
 