import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useState } from 'react';
import {
  AppScreens,
  type ScreenProps,
} from '../../../jiomart-common/src/JMAppScreenEntry';
import JMBridgeEmitterState from '../../../jiomart-general/src/bridge/JMBridgeEmitter';
import ScreenSlot, {
    DeeplinkHandler, createEventFinalBean,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import useGlobalBottomSheetController from '../../../jiomart-main/src/features/StartUp/controllers/useGlobalBottomSheetController';
import GlobalBottomSheet from '../../../jiomart-main/src/features/StartUp/GlobalBottomSheet';
import { getBaseURL } from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import useWebViewController from '../../../jiomart-webmanager/src/useWebViewController';
import WebViewUtility from '../../../jiomart-webmanager/src/WebViewUtility';

export type JProps = ScreenProps<typeof AppScreens.HOME_SCREEN>;

const JMHomeScreen = (props: JProps) => {
  const {
    navigation,
    navigationBean,
    webState,
    webViewRef,
    handleJSEvents,
    onUrlOverride,
    onError,
    handleBackPress,
    handleNavigationStateChange,
    eventTriggerBean,
    screenSpecificQcMessage
  } = useWebViewController(props);
  const { checkAndOpenNextSheet } = useGlobalBottomSheetController();

  const [isBSNonCancellable, setBSNonCancellable] = useState(false);


  const { } = JMBridgeEmitterState();

  const webViewMainUi = () => {
    console.log('webState.content', webState.content);
    return (
      <WebViewUtility
        source={{ uri: webState.content || getBaseURL() }}
        onMessage={handleJSEvents}
        onRef={ref => {
          webViewRef.current = ref;
        }}
        onError={onError}
        javaScriptEnabled={true}
        onNavigationStateChange={handleNavigationStateChange}
        onShouldStartLoadWithRequest={onUrlOverride}
      />
    );
  };

  const isHomeUrl = (url: string) => {
    const base = getBaseURL();

    const normalizedUrl = url.split('?')[0].replace(/\/+$/, '');
    const normalizedBase = base.replace(/\/+$/, '');

    return normalizedUrl === normalizedBase;
  };

  useFocusEffect(
    useCallback(() => {
      if (isHomeUrl(webState.content ?? '')) {
        checkAndOpenNextSheet();
      }
    }, []),
  );

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={createEventFinalBean(bean, eventTriggerBean)}
          navigation={navigation}
          onCustomBackPress={handleBackPress}
          bottomsheetDismissable={{ disabledBackDropClick: isBSNonCancellable }}
          bottomSheetContent={({ openDeliverToBarPincodeBtmSheet }) => (
            <GlobalBottomSheet
              openPincodeBottomSheet={(isNonCancellable) => {
                setBSNonCancellable(isNonCancellable)
                openDeliverToBarPincodeBtmSheet();
              }}
            />
          )}
          children={_ => {
            return webViewMainUi();
          }}
          deliverToBarData={{qcMessage : screenSpecificQcMessage}}
        />
      )}
    />
  );
};

export default JMHomeScreen;
