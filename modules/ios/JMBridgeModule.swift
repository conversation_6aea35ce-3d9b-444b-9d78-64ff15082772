import Foundation
import React

 
@objc(JMBridgeModule)
class JMBridgeModule: NSObject {
 
    var resolver: RCTPromiseResolveBlock?
     var rejecter: RCTPromiseRejectBlock?
    override init() {
        super.init()
    }
    
    
 
   @objc
  func getDeviceInfo(_ resolve: @escaping RCTPromiseResolveBlock,
                     rejecter reject: @escaping RCTPromiseRejectBlock) {
    do {
      let device = UIDevice.current
      let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
 
      let result: [String: Any] = [
        "device_model": device.model,
        "device_name": device.name,
        "device_os": "iOS",
        "os_version": device.systemVersion,
        "device_type": 3,
        "unique_id": device.identifierForVendor?.uuidString ?? "",
        "app_version": appVersion
      ]
 
      resolve(result)
    } catch {
      reject("DEVICE_INFO_ERROR", "Failed to get device info", error)
    }
  }
}
 
 