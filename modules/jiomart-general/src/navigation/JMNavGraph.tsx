import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {OneRetailSharedViewModel} from '../../../../node_modules/@ucp/one-retail/src/utils/bridge/OneRetailSharedViewModel';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {
  ActionType,
  navBeanObj,
  navGraph,
  NavigationBean,
  NavigationType,
  UserJourneyRequiredState,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {getOneRetailsConfig} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {updatedNavBeanData} from '../deeplink/JMDeeplinkUtility';
import {logAnalyticsEventWithGAModel} from '../ga_utils/JMAnalyticsUtility';
// import { OneRetailSharedViewModel } from '../../../../one-retail/src/utils/bridge/OneRetailSharedViewModel';

export function redirectToLoginScreen(
  navigation: NativeStackNavigationProp<any>,
) {
  const bean = navBeanObj({
    actionType: ActionType.OPEN_NATIVE,
    destination: AppScreens.OUTSIDE_LOGIN_SCREEN,
    actionUrl: '',
  });
  const state = navigation.getState();
  const navObj = state.routes.find(item => item.name === bean.destination);
  if (navObj !== undefined) {
    navigation.dispatch({
      type: 'NAVIGATE',
      payload: {
        name: bean.destination,
        merge: true,
        params: undefined,
      },
    });
  } else {
    navigation.reset({
      index: 0,
      routes: [{ name: AppScreens.OUTSIDE_LOGIN_SCREEN }],
    });
  }
}

export function popUpToRequiredDestinationAndNavigate(
  navigation: NativeStackNavigationProp<any>,
  navigationBean: NavigationBean,
  keepSameParams: boolean = true,
) {
  if (!JMSharedViewModel.Instance.networkAvailable) return;
  const state = navigation.getState();
  const navObj = state.routes.find(
    item => item.name === navigationBean.destination,
  );
  if (navObj !== undefined) {
    navigation.dispatch({
      type: 'NAVIGATE',
      payload: {
        name: navigationBean.destination,
        merge: keepSameParams,
        params: keepSameParams === true ? undefined : navigationBean,
      },
    });
  } else if (navigationBean) {
    openNativeNavigation(navigationBean, navigation);
  }
}

export function navigateTo(
  navigationBean: NavigationBean,
  navigation: NativeStackNavigationProp<any>,
) {

  if (
    !JMSharedViewModel.Instance.networkAvailable ||
    navigationBean === null ||
    navigationBean === undefined
  )
    return;

  if (navigationBean.destination === AppScreens.ONE_RETAIL_SCREEN) {
    setOneRetailsParams();
  }

  if (navigateToUserRequiredStateJourney(navigationBean, navigation)) return;
  logAnalyticsEventWithGAModel(navigationBean.gaModel);

  switch (navigationBean.actionType) {
    case ActionType.OPEN_NATIVE:
      openNativeNavigation(navigationBean, navigation);
      break;
    case ActionType.OPEN_WEB_URL:
    case ActionType.OPEN_WEB_HTML:
    case ActionType.OPEN_WEB_URL_WITH_TOKEN:
      openNativeNavigation(navigationBean, navigation);
      break;

    case ActionType.OPEN_EXTERNAL:
      break;

    case ActionType.OPEN_DEEPLINK:
      updatedNavBeanData('', navigationBean).then(bean => {
        if (bean) {
          navigateTo(bean, navigation);
        }
      });
      break;

    default:
      break;
  }
}

export function openLogin(navigation: NativeStackNavigationProp<any>) {
  navigateTo(
    navBeanObj({
      actionType: ActionType.OPEN_NATIVE,
      destination: AppScreens.ONE_RETAIL_SCREEN,
      navigationType: NavigationType.PUSH,
    }),
    navigation,
  );
}

function setOneRetailsParams() {
  const oneRetailConfig = getOneRetailsConfig();
  console.log('setOneRetailsParams --- ', oneRetailConfig);
  OneRetailSharedViewModel.Instance.setOneRetailParams({
    apiDomain: oneRetailConfig.apiDomain,
    hasActiveSession: JMSharedViewModel.Instance.getLoggedInStatus(),
    clientId: oneRetailConfig.clientId,
    returenUiUrl: oneRetailConfig.returenUiUrl,
  });
}

export function navigateToUserRequiredStateJourney(
  navigationBean: NavigationBean,
  navigation: NativeStackNavigationProp<any>,
  popScreen: boolean = false,
): boolean {
  JMLogger.log(
    'navigateToUserRequiredStateJourney  userAuthenticationRequired ' +
    navigationBean.userJourneyRequiredState,
  );
  switch (navigationBean.userJourneyRequiredState) {
    case UserJourneyRequiredState.LOGIN_REQUIRED: {
      const loggedIn = JMSharedViewModel.Instance.getLoggedInStatus();
      JMLogger.log('navigateToUserRequiredStateJourney loggedIn ' + loggedIn);
      if (!loggedIn) {
        navigateToUserRequiredStateJourneyHelper(
          UserJourneyRequiredState.LOGIN_REQUIRED,
          navigationBean,
          navigation,
          popScreen,
        );
        return true;
      }
      return false;
    }

    default: {
      return false;
    }
  }
}

function navigateToUserRequiredStateJourneyHelper(
  userJourneyRequiredState: UserJourneyRequiredState,
  navigationBean: NavigationBean,
  navigation: NativeStackNavigationProp<any>,
  popScreen: boolean = false,
) {
  switch (userJourneyRequiredState) {
    case UserJourneyRequiredState.LOGIN_REQUIRED: {
      const outsideLoginBean = navBeanObj({
        actionType: ActionType.OPEN_NATIVE,
        destination: AppScreens.ONE_RETAIL_SCREEN,
        actionUrl: '',
        navigationType: NavigationType.NAVIGATE,
        userJourneyRequiredState: UserJourneyRequiredState.LOGIN_NOT_REQUIRED,
        headerType: 1,
      });
      JMSharedViewModel.Instance.setNavigationBeanData(navigationBean);
      if (popScreen && navigation.canGoBack()) {
        navigation.goBack();
      }
      navigateTo(outsideLoginBean, navigation);
      break;
    }
    default: {
    }
  }
}

function openNativeNavigation(
  navigationBean: NavigationBean,
  navigation: NativeStackNavigationProp<any>,
) {
  const mainDestination = navGraph.get(navigationBean.destination ?? '') || '';
  if (mainDestination.length != 0) {
    if (mainDestination === navigationBean.destination) {
      switch (navigationBean.navigationType) {
        case NavigationType.REPLACE: {
          navigation.replace(mainDestination, navigationBean);
          break;
        }
        case NavigationType.PUSH: {
          navigation.push(mainDestination, navigationBean);
          break;
        }
        case NavigationType.RESET: {
          navigation.reset({
            index: 0,
            routes: [{ name: mainDestination, params: navigationBean }],
          });
          break;
        }
        default: {
          navigation.navigate(mainDestination, navigationBean);
        }
      }
    } else {
      switch (navigationBean.navigationType) {
        case NavigationType.REPLACE: {
          navigation.replace(mainDestination, {
            screen: navigationBean.destination,
            params: navigationBean,
          });
          break;
        }
        case NavigationType.PUSH: {
          navigation.push(mainDestination, {
            screen: navigationBean.destination,
            params: navigationBean,
          });
          break;
        }
        case NavigationType.RESET: {
          navigation.reset({
            index: 0,
            routes: [
              {
                name: mainDestination,
                state: {
                  routes: [
                    { name: navigationBean.destination, params: navigationBean },
                  ],
                },
              },
            ],
          });
          break;
        }
        default: {
          navigation.navigate(mainDestination, {
            screen: navigationBean.destination,
            params: navigationBean,
          });
        }
      }
    }
  }
}
