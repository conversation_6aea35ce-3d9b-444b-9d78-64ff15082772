import {useRef, useState} from 'react';
import type {TextInput} from 'react-native';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';

type ValidationConfig = {
  [key: string]: {
    min?: number;
    max?: number;
    required?: boolean;
    regex?: string;
    isReadOnly?: boolean;
    isRegexClean?: boolean;
    error?: {
      min?: string;
      max?: string;
      required?: string;
      default?: string;
    };
  };
};

const useFormHandler = (
  initialData: any,
  validationConfig: ValidationConfig,
) => {
  const [formData, setFormData] = useState(initialData);
  const [formError, setFormError] = useState(
    Object.keys(initialData).reduce((acc, key) => ({...acc, [key]: ''}), {}),
  );
  const inputRef = useRef<{[key: string]: TextInput | null}>(
    Object.keys(initialData).reduce((acc, key) => ({...acc, [key]: null}), {}),
  );

  const handleFormError = (field: string, message: string) => {
    return () => {
      setFormError(prev => ({
        ...prev,
        [field]: message,
      }));
    };
  };

  const handleInputRef = (field: string) => {
    return (ref: TextInput) => {
      if (ref && field) {
        inputRef.current[field] = ref;
      }
    };
  };

  const clearFieldError = (field: string) => {
    setFormError(prevErrors => ({
      ...prevErrors,
      [field]: '',
    }));
  };
  const handleFormData = (field: string) => {
    return (text: string) => {
      setFormData((prev: any) => ({
        ...prev,
        [field]: text,
      }));
      clearFieldError(field);
    };
  };
  const onChangeText = (field: any) => {
    return (value: string) => {
      let text = value;
      const fieldConfig = validationConfig?.[field];
      if (fieldConfig?.isRegexClean && fieldConfig?.regex) {
        const regex = new RegExp(fieldConfig?.regex, 'g');
        text = text?.replace?.(regex, '') ?? '';
      }
      handleFormData(field)(text);
    };
  };

  const validateValue = (value: string, config: any): string => {
    if (config?.required && (!value || value?.trim() === '')) {
      return config?.error?.required || 'This field is required.';
    }

    if (!isNullOrUndefinedOrEmpty(value) && value?.length < config?.min) {
      return (
        config?.error?.min || `Minimum ${config?.min} characters required.`
      );
    }

    if (!isNullOrUndefinedOrEmpty(value) && value?.length > config?.max) {
      return config?.error?.max || `Maximum ${config?.max} characters allowed.`;
    }

    if (
      !isNullOrUndefinedOrEmpty(value) &&
      !config?.isRegexClean &&
      config?.regex
    ) {
      const pattern = new RegExp(config?.regex);
      if (!pattern.test(value)) {
        return config?.error?.default || 'Invalid format.';
      }
    }

    return '';
  };

  const validateForm = (): boolean => {
    let isValid = true;
    let firstErrorField: string | null = null;

    Object.keys(validationConfig).forEach(field => {
      const error = validateValue(formData[field], validationConfig[field]);
      if (error) {
        isValid = false;
        handleFormError(field, error)();
        if (!firstErrorField && !validationConfig[field]?.isReadOnly) {
          firstErrorField = field;
        }
      }
    });

    if (!isValid && firstErrorField && inputRef.current[firstErrorField]) {
      inputRef.current[firstErrorField]?.focus();
    }

    return isValid;
  };

  return {
    formData,
    formError,
    handleFormData,
    clearFieldError,
    handleInputRef,
    validateValue,
    onChangeText,
    validateForm,
    handleFormError,
  };
};

export default useFormHandler;
