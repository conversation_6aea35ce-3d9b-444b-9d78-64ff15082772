import {JMRNAddressModule, JMGeneralPropertiesModule} from '.';

export async function getReverseGeoCodeFromLatLongNB(
  latitude: number,
  longitude: number,
) {
  return await JMRNAddressModule.getReverseGeoCodeFromLatLong(
    latitude,
    longitude,
  );
}


export async function getDeviceInfo() {
  return await JMGeneralPropertiesModule.getDeviceInfo();
}

export async function setKeyboardResizeMode(flag : boolean) {
  return await JMGeneralPropertiesModule.setResizeMode(flag);
}
