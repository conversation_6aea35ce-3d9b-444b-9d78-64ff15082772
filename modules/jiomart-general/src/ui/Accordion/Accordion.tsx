import {LayoutAnimation, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {styles} from './styles/AccordionStyleSheet';
import useAccordion from './hook/useAccordion';
import {
  AccordionPanelProps,
  AccordionProps,
} from './types/AccordionInterface.d';

const Accordion = (props: AccordionProps) => {
  const {
    allowMultiple = false,
    children,
    onChange,
    style,
    ItemSeparatorComponent,
  } = props;

  const {Children, openAccordionPanel, handleAccordionEvent} = useAccordion(
    children,
    allowMultiple,
  );
  return (
    <View style={[styles.accordion, style]}>
      {Children.map((child: any, index) => {
        return (
          <>
            <AccordionPanel
              key={`acc-panel-${index}`}
              {...child?.props}
              open={openAccordionPanel[index]}
              onPress={() => {
                LayoutAnimation.configureNext(
                  LayoutAnimation.Presets.easeInEaseOut,
                );
                if (onChange) {
                  onChange(index);
                }
                handleAccordionEvent(index);
              }}
            />
            {index !== Children.length - 1 && ItemSeparatorComponent}
          </>
        );
      })}
    </View>
  );
};

const AccordionPanel = (props: AccordionPanelProps) => {
  const {accordionHeader, children, open, onPress, disable, style} = props;
  return (
    <View
      style={[styles.accordionPanel, style, disable ? {opacity: 0.65} : null]}>
      <TouchableOpacity
        onPress={onPress}
        style={styles.accordionHeader}
        disabled={disable}>
        {accordionHeader}
      </TouchableOpacity>
      {open ? children : null}
    </View>
  );
};

export default Accordion;
export {AccordionPanel};
