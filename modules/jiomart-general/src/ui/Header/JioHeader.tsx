import {
  Animated,
  TextInput,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {JioAvatar, JioIcon, JioText, useColor} from '@jio/rn_components';
import useJioHeaderController from './controllers/useJioHeaderController';
import type {
  JioHeaderBadgeProps,
  JioHeaderIconList,
  JioHeaderIconListViewProps,
  JioHeaderNotifyProps,
  JioHeaderSearchBarProps,
  JMHeaderNavProps,
} from './types/JioHeader';
import {styles} from './styles/JioHeaderStyle';
import {
  IconColor,
  JioAvatarKind,
  JioAvatarSize,
  JioColor,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import useJioHeaderSearchBar from './hooks/useJioHeaderSearchBar';

import {IconKey} from '@jio/rn_components/src/utils/IconUtility';
import {useGlobalState} from '../../context/JMGlobalStateProvider';
import {useCartCount} from '../../../../jiomart-cart/src/hooks/useCart';
import FastImage from 'react-native-fast-image';
import type {NavigationBean} from '../../../../jiomart-common/src/JMNavGraphUtil';
import {NavigationBeanType} from './JioMartHeader';
import { JMSharedViewModel } from '../../../../jiomart-common/src/JMSharedViewModel';

const JioHeader = (props: JMHeaderNavProps) => {
  const {
    data,
    navTitle,
    subTitle,
    style,
    onPress,
    headerTopComponent,
    headerBottomComponent,
    searchTextHandler,
    onSubmitHandler,
    customFunctionality,
  } = useJioHeaderController(props);
  const {backgroundColor, headerIconsList, searchData} = data;
  const leftIconList = headerIconsList?.leftIconList;
  const rightIconList = headerIconsList?.rightIconList;
  const headerBackgroundColor = useColor(
    (backgroundColor as JioColor) ?? 'primary_50',
  );
  const [searchValue, setSearchValue] = useState('');

  if (!Object.keys(data)?.length) {
    return null;
  }

  return (
    <>
      {React.isValidElement(headerTopComponent) &&
        React.cloneElement(headerTopComponent)}

      <View
        style={[
          styles.container,
          {backgroundColor: headerBackgroundColor},
          style?.headerStyle,
        ]}>
        {leftIconList && leftIconList?.length > 0 && (
          <JioHeaderIconListView
            iconList={leftIconList as JioHeaderIconList[]}
            onPress={onPress}
            customFunctionality={customFunctionality}
          />
        )}

        {navTitle && !searchData?.enable && (
          <JioText
            text={navTitle}
            appearance={JioTypography.BODY_M_BOLD}
            maxLines={1}
            color="primary_inverse"
            style={styles.headerTitle}
          />
        )}

        {searchData?.enable && (
          <JioHeaderSearchBar
            {...searchData}
            onSubmitHandler={onSubmitHandler}
            textInput={{
              onChangeText: text => {
                setSearchValue(text);
                searchTextHandler?.(text);
              },
              value: searchValue,
            }}
            onPress={onPress}
          />
        )}

        {subTitle && (
          <JioText
            text={subTitle}
            appearance={JioTypography.BODY_XXS}
            color="primary_30"
            style={styles.marginLeftAuto}
          />
        )}

        {rightIconList && rightIconList?.length > 0 && (
          <JioHeaderIconListView
            iconList={rightIconList as JioHeaderIconList[]}
            style={[styles.marginLeftAuto]}
            onPress={onPress}
            customFunctionality={customFunctionality}
          />
        )}
      </View>

      {React.isValidElement(headerBottomComponent) &&
        React.cloneElement(headerBottomComponent)}
    </>
  );
};

const JioHeaderSearchBar = React.memo((props: JioHeaderSearchBarProps) => {
  const {
    style,
    backgroundColor,
    inputRef,
    textInput,
    isEditable,
    value,
    JioHeaderSearchBarIconListView,
    onSearchBarPress,
    onSearchText,
    onFocus,
    onBlur,
    closeSearchButton,
    onSubmitHandler,
    enableAnimation,
    animationTexts,
    fadeAnim,
    translateYAnim,
    currentIndex,
    animationEnabled,
  } = useJioHeaderSearchBar(props);

  return (
    <View
      style={[
        styles.searchBarContainer,
        {
          backgroundColor: backgroundColor,
        },
        style,
      ]}>
      <JioHeaderSearchBarIconListView />
      <TouchableOpacity
        style={styles.flexOne}
        hitSlop={7}
        onPress={onSearchBarPress}
        disabled={isEditable}>
        <View
          style={[
            styles.flexOne,
            !isEditable && value !== '' ? styles.flexWrap : null,
          ]}
          pointerEvents={isEditable ? 'auto' : 'none'}>
          <TextInput
            ref={inputRef}
            style={[styles.searchBarTextInput, textInput?.style]}
            pointerEvents={isEditable ? 'auto' : 'none'}
            underlineColorAndroid={
              textInput?.underlineColorAndroid ?? 'transparent'
            }
            placeholderTextColor={textInput?.placeholderTextColor ?? 'white'}
            enablesReturnKeyAutomatically={
              textInput?.enablesReturnKeyAutomatically ?? true
            }
            selectionColor={textInput?.selectionColor ?? 'rgba(0, 120, 173, 1)'}
            onChangeText={onSearchText}
            onFocus={onFocus}
            onBlur={onBlur}
            onSubmitEditing={() => {
              onSubmitHandler?.(value);
            }}
            {...textInput}
            placeholder={
              animationEnabled ? '' : JMSharedViewModel?.Instance?.isQcJourneySelected ? props.qcPlaceholder ?? "Search in Quick" : props.placeholder ?? 'Search JioMart'
            }
            value={value}
            numberOfLines={1}
          />
          {animationEnabled && animationTexts && animationTexts?.length > 0 && (
            <View style={styles.animationTextWrapper}>
              <Animated.Text
                style={[
                  styles.animationText,
                  {
                    opacity: fadeAnim,
                    transform: [{translateY: translateYAnim}],
                  },
                ]}>
                {animationTexts?.[currentIndex]}
              </Animated.Text>
            </View>
          )}
        </View>
      </TouchableOpacity>

      {value && isEditable ? (
        <TouchableOpacity
          onPress={closeSearchButton}
          activeOpacity={0.65}
          hitSlop={6}>
          <JioIcon ic="IcCloseRemove" color={IconColor.INVERSE} />
        </TouchableOpacity>
      ) : null}
    </View>
  );
});

const JioHeaderIconListView = React.memo(
  (props: JioHeaderIconListViewProps) => {
    const {iconList = [], style, onPress, customFunctionality} = props;
    const {userInitials, notificationCount} = useGlobalState();
    const count = useCartCount();

    const modifiedIconList = iconList.map(list => {
      switch (list?.cta?.type) {
        case NavigationBeanType.CART:
          return {
            ...list,
            icon: {
              ...list?.icon,
              isNotify: false,
              badge: count,
            },
          };
        case NavigationBeanType.AVATAR:
          return {
            ...list,
            avatar: {
              ...list?.avatar,
              kind: userInitials ? JioAvatarKind.INITIALS : JioAvatarKind.ICON,
              name: userInitials,
              isNotify: JMSharedViewModel.Instance.loggedInStatus &&
                notificationCount > 0,
            },
          };
        default:
          return list;
      }
    });

    const IconList = modifiedIconList?.map((list, index) => {
      const item = list?.icon || list?.avatar || list?.image;
      const isIcon = !!list?.icon;
      const isAvatar = !!list?.avatar;
      const isImage = !!list?.image;

      const disabled = !!(
        customFunctionality?.[`${list?.cta?.type}`]?.disabled || item?.disabled
      );
      const badge =
        customFunctionality?.[`${list?.cta?.type}`]?.badge || item?.badge;
      const isNotify =
        customFunctionality?.[`${list?.cta?.type}`]?.isNotify || item?.isNotify;
      const Style =
        customFunctionality?.[`${list?.cta?.type}`]?.style || item?.style;

      return (
        <TouchableHighlight
          key={`h_icon-${index}`}
          style={[
            isIcon ? styles.padding12 : {borderRadius: 100},
            disabled ? {opacity: 0.65} : null,
            Style,
          ]}
          underlayColor={'#0C5273A6'}
          onPress={() => {
            if (
              !customFunctionality?.[`${list?.cta?.type}`]?.disableDefaultCall
            ) {
              onPress?.(list?.cta as NavigationBean);
            }
            customFunctionality?.[`${list?.cta?.type}`]?.onPress?.(
              list?.cta as NavigationBean,
            );
          }}
          disabled={disabled}
          activeOpacity={1}>
          <>
            {isIcon ? (
              <JioIcon ic={list?.icon?.ic as IconKey} {...list?.icon} />
            ) : null}
            {isAvatar ? (
              <JioAvatar
                kind={list?.avatar?.kind ?? JioAvatarKind.ICON}
                name={list?.avatar?.name ?? ''}
                size={list?.avatar?.size ?? JioAvatarSize.SMALL}
                {...list?.avatar}
              />
            ) : null}
            {isImage ? (
              <FastImage
                {...list?.image}
                style={[
                  {
                    width: list?.image?.width,
                    height: list?.image?.height,
                  },
                  styles.image,
                ]}
              />
            ) : null}
            {badge ? <JioHeaderBadge count={item?.badge} /> : null}
            {isNotify ? (
              <JioHeaderNotify
                style={[
                  styles.notifyIcon,
                  isAvatar ? {top: 0, right: 0} : null,
                ]}
              />
            ) : null}
          </>
        </TouchableHighlight>
      );
    });

    return <View style={[style, styles.iconListContainer]}>{IconList}</View>;
  },
);

const JioHeaderBadge = React.memo((props: JioHeaderBadgeProps) => {
  const {count, style} = props;

  const secondary50 = useColor('secondary_50');

  return count ? (
    <View
      style={[
        styles.badge,
        {
          backgroundColor: secondary50,
        },
        style,
      ]}>
      <JioText
        text={`${count}`}
        appearance={JioTypography.BODY_XXS}
        color={'primary_inverse'}
      />
    </View>
  ) : null;
});

const JioHeaderNotify = React.memo((props: JioHeaderNotifyProps) => {
  const {style} = props;
  const secondary50 = useColor('secondary_50');

  return (
    <View style={[styles.notify, {backgroundColor: secondary50}, style]} />
  );
});

export default React.memo(JioHeader);
