import {StyleSheet, TouchableOpacity, View, type StyleProp, type ViewStyle} from 'react-native';
import React from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
  type JioColor,
  type JioIconProps,
} from '@jio/rn_components/src/index.types';
import FastImage from 'react-native-fast-image';
import AddCtaView from './AddCta/AddCtaView';
import WishlistToggleView from './Wishlist/WishlistToggleView';
import {SvgUri} from 'react-native-svg';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import {formatPrice, getSvgUrlData} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import QCTag from './QCTag';
import JMTag, { type JMTagProps } from './JMTag';
import type { AddCtaViewProps } from './AddCta/types/AddCtaModel';


interface DeliveryConfig {
  hyperLocal?: {
    showHyperLocal: boolean;
    hyperLocalText: string;
  };
  scheduleDelivery?: {
    showScheduleDelivery: boolean;
    scheduleDeliveryText: string;
  };
}

export interface ProductGridCardProps {
  style?: StyleProp<ViewStyle>;
  image?: string;
  brand?: string;
  productCode: number;
  sellerId: string;
  discount?: string;
  title?: string;
  sellingPrice?: number;
  stridePrice?: number;
  isQuickDeliveryLable?: isQuickDeliveryLableProps;
  onPress?: () => void;
  onWishlistPress?: (state: boolean) => void;
  onCartPress?: (value: 'Add' | 'Remove') => void;
  slug?: string;
  verticalCode?: string;
  sellable?: boolean;
  itemSize?: string;
  storeId?: string | number;
  articleAssignment?: string;
  isMultiVariants?: any;
  multiVariantsOnPress?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  gridConfig?: any;
  showAddToCart?: boolean;
  showWishlist?: boolean;
  showOutOfStock?: boolean;
  showOffer?: boolean;
  offerText?: string;
  onOfferPress?: () => void;
  tag?: JMTagProps;

  addCta?: {
    disableDefaultFun?: boolean;
    onPress?: () => void;
    button?: AddCtaViewProps['button'];
  };

  imageUrl?: string;
  disableWishlist?: boolean;
  disableAddToCart?: boolean;
  shouldShowVeg?: boolean;
  vegIcon?: JioIconProps;
  shouldShowSmartBazzar?: boolean;
  smartBazzarImage?: string;
  disableTag?: boolean;
  uid?: any;
  slug?: string;
  title?: string;
  titleColor?: string;
  titleMaxLine?: number;
  effectivePrice?: number;
  effectivePriceColor?: JioColor;
  markedPrice?: number;
  markedPriceColor?: JioColor;
  discount?: string;
  discountBackgroundColor?: string;
  discountColor?: JioColor;
  onMultiVariantPress?: () => void;
  shouldShowMultiVariant?: boolean;
  size?: string;
  totalMultiVariant?: string;
  totalMultiVariantColor?: JioColor;
  multiVariantColor?: JioColor;
  multiVariantIcon?: JioIconProps;
  addToCartTitle?: string;
  verticalCode?: string;
  outOfStockText?: string;
  outOfStockColor?: JioColor;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
}

export interface isQuickDeliveryLableProps {
  hyperLocal: boolean;
  IcScheduleDelivery: boolean;
  displayText: string;
}


const LgProductGridCard = (props: ProductGridCardProps) => {
  const {
    title,
    titleColor = 'primary_grey_80',
    titleMaxLine = 2,
    sellingPrice,
    effectivePriceColor = 'primary_grey_100',
    stridePrice,
    markedPriceColor = 'primary_grey_60',
    discountColor = 'secondary_50',
    discount,
    image,
    productCode,
    itemSize,
    verticalCode,
    outOfStockText = 'Out of Stock',
    outOfStockColor = 'feedback_error_50',
    tag,
    style,
    sellable,
    slug,
    onPress,
    showAddToCart = false,
    showWishlist = false,
    showOffer = true,
    showOutOfStock = true,
    offerText,
    onOfferPress,
    addCta,
  } = props;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      activeOpacity={0.65}
      onLongPress={() => {}}
      onPress={onPress}>
      <View>
        <FastImage
          source={{uri: image}}
          style={styles.image}
          resizeMode="contain"
        />
        {!showWishlist && (
          <WishlistToggleView uid={productCode} style={styles.wishlist} />
        )}
      </View>
      <View style={styles.containWrapper}>
        <View style={styles.flexOne}>
          <JioText
            text={title ?? ''}
            appearance={JioTypography.BODY_XXS}
            maxLines={titleMaxLine}
            color={titleColor as JioColor}
            style={styles.title}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <View>
              <JioText
                text={`₹${formatPrice(sellingPrice)}`}
                appearance={JioTypography.BODY_XS_BOLD}
                color={effectivePriceColor}
                style={sellingPrice ? {opacity: 1} : {opacity: 0}}
              />
              <JioText
                text={`₹${formatPrice(stridePrice)}`}
                appearance={JioTypography.BODY_XS}
                color={markedPriceColor}
                style={[
                  {
                    textDecorationLine: 'line-through',
                  },
                  sellingPrice != stridePrice && stridePrice
                    ? {opacity: 1}
                    : {opacity: 0},
                ]}
              />
            </View>
            {discount ? (
              <JioText
                text={`${discount.split(' ')[0]} \nOff`}
                appearance={JioTypography.BODY_XS_BOLD}
                color={discountColor}
                style={[
                  {
                    maxWidth: rw(60),
                    marginRight: rw(12),
                  },
                  discount ? {opacity: 1} : {opacity: 0},
                ]}
              />
            ) : null}
          </View>
          {sellable
            ? !showAddToCart && (
                <AddCtaView
                  uid={productCode}
                  size={`${itemSize}`}
                  slug={`${slug}`}
                  meta={{
                    vertical_code: `${verticalCode}`,
                  }}
                  runPriceApi={!addCta?.disableDefaultFun}
                  disableDefaultAddToCartFunc={addCta?.disableDefaultFun}
                  button={addCta?.button}
                  onPress={addCta?.onPress}
                  style={styles.cart}
                />
              )
            : showOutOfStock && (
                <JioText
                  text={outOfStockText}
                  appearance={JioTypography.BODY_XXS}
                  style={styles.outOfStock}
                  color={outOfStockColor}
                />
              )}
        </View>
        {tag?.hyperLocal ? (
          <QCTag
            icon={<SvgUri uri={getSvgUrlData('IcQcTagsLogoMini')} />}
            image={tag?.image ?? ''}
            color={tag?.backgroundColor as string}
            textColor={tag?.textColor as JioColor}
            primaryText={tag?.primaryText}
            secondaryText={tag?.secondaryText}
            iconHeight={tag?.iconHeight}
            iconWidth={tag?.iconWidth}
            style={[styles.hyperLocal, , {width: rw(120)}]}
          />
        ) : tag?.IcScheduleDelivery ? (
          <JMTag
            color={tag?.backgroundColor as string}
            textColor={tag?.textColor as JioColor}
            text={tag?.displayText}
            style={[styles.hyperLocal, {width: rw(120)}]}
            minLines={2}
          />
        ) : null}
        {showOffer ? (
          <TouchableOpacity
            style={styles.offer}
            hitSlop={3}
            activeOpacity={0.65}
            onPress={onOfferPress}>
            <JioIcon
              ic={'IcOfferCoupon'}
              color={IconColor.SPARKLE}
              size={IconSize.SMALL}
            />
            <JioText
              text={offerText}
              appearance={JioTypography.BODY_XXS}
              color={'primary_60'}
            />
            <JioIcon
              ic={'IcChevronDown'}
              color={IconColor.PRIMARY60}
              size={IconSize.SMALL}
              style={styles.marginTopAuto}
            />
          </TouchableOpacity>
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    flexGrow: 1,
  },
  hyperLocal: {margin: 3, marginTop: 'auto', paddingVertical: 4, flex: 1},
  title: {height: rw(36)},
  image: {
    width: rw(90),
    height: rw(90),
    alignSelf: 'center',
    marginTop: 21,
    marginBottom: 15,
  },
  containWrapper: {
    rowGap: 7,
    padding: 4,
    flex: 1,
  },
  flexOne: {flex: 1},
  cart: {
    marginTop: 2,
    marginBottom: 4,
  },
  outOfStock: {
    alignSelf: 'flex-end',
    paddingVertical: rh(9),
    marginTop: 2,
    marginBottom: 4,
  },
  marginTopAuto: {marginTop: 'auto'},
  offer: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 4,
    marginTop: 'auto',
  },
  wishlist: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
});

export default LgProductGridCard;
