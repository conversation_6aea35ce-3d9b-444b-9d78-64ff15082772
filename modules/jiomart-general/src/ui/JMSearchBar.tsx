import {
  StyleSheet,
  TouchableOpacity,
  View,
  type StyleProp,
  type TextInputProps,
  type ViewStyle,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {JioIcon, useColor} from '@jio/rn_components';
import {TextInput} from 'react-native-gesture-handler';
import {IconColor, type JioColor} from '@jio/rn_components/src/index.types';

export interface JMSearchBarProps {
  textInput?: TextInputProps;
  style?: StyleProp<ViewStyle>;
  searchBarBackgroundColor?: JioColor;
}

const JMSearchBar = (props: JMSearchBarProps) => {
  const {style, textInput, searchBarBackgroundColor} = props;

  const [value, setValue] = useState('');
  const [isFocused, setIsFocused] = useState(textInput?.autoFocus ?? false);
  const inputRef = useRef<TextInput | null>(null);
  const clearingInput = useRef(false);
  const backgroundColor = useColor(
    searchBarBackgroundColor ?? 'primary_grey_20',
  );

  const closeSearchButton = useCallback(() => {
    clearingInput.current = true;
    setValue('');
    inputRef.current?.clear();
    inputRef.current?.blur();
    textInput?.onChangeText?.('');
  }, [textInput]);

  const onSearchText = useCallback(
    (text: any) => {
      if (clearingInput.current) {
        clearingInput.current = false;
        return;
      }
      setValue(text);
      textInput?.onChangeText?.(text);
    },
    [textInput],
  );

  const onFocus = useCallback(
    (e: any) => {
      setIsFocused(true);
      textInput?.onFocus?.(e);
    },
    [textInput],
  );

  const onBlur = useCallback(
    (e: any) => {
      setIsFocused(false);
      textInput?.onBlur?.(e);
    },
    [textInput],
  );

  useEffect(() => {
    if (textInput?.value !== undefined) {
      setValue(textInput?.value as string);
    }
  }, [textInput?.value]);

  return (
    <View
      style={[
        styles.searchBarContainer,
        {
          backgroundColor: backgroundColor,
        },
        style,
      ]}>
      {!value && !isFocused ? (
        <TouchableOpacity
          onPress={() => {
            inputRef.current?.focus();
          }}
          activeOpacity={0.65}
          hitSlop={6}>
          <JioIcon ic="IcSearch" color={IconColor.GREY100} />
        </TouchableOpacity>
      ) : null}
      <TextInput
        ref={inputRef}
        style={[styles.searchBarTextInput, textInput?.style]}
        placeholderTextColor={textInput?.placeholderTextColor ?? '#000000A6'}
        enablesReturnKeyAutomatically={
          textInput?.enablesReturnKeyAutomatically ?? true
        }
        returnKeyType={textInput?.returnKeyType ?? 'done'}
        selectionColor={textInput?.selectionColor ?? 'black'}
        onChangeText={onSearchText}
        {...textInput}
        onFocus={onFocus}
        onBlur={onBlur}
        placeholder={textInput?.placeholder}
        value={value}
        numberOfLines={1}
        hitSlop={10}
      />
      {value ? (
        <TouchableOpacity
          onPress={closeSearchButton}
          activeOpacity={0.65}
          hitSlop={6}>
          <JioIcon ic="IcCloseRemove" color={IconColor.GREY100} />
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

export default JMSearchBar;

const styles = StyleSheet.create({
  searchBarTextInput: {
    color: '#141414',
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  searchBarContainer: {
    paddingHorizontal: 12,
    borderRadius: 24,
    justifyContent: 'center',
    flexDirection: 'row',
    height: 48,
    alignItems: 'center',
    columnGap: 12,
    zIndex: 100,
  },
});
