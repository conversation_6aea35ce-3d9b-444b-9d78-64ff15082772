import {Platform, StyleSheet} from 'react-native';
import { getScreenDim, rh, rw } from '../../../../../jiomart-common/src/JMResponsive';

const styles = StyleSheet.create({
  cardElevated: {
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
    backgroundColor: '#FFFFFF',
  },
  container: {
    paddingHorizontal: rw(16),
    paddingTop: rh(8),
    paddingBottom: rh(12),
    marginHorizontal: rw(24),
    borderRadius: 16,
    marginTop: rh(16),
    borderWidth: Platform.OS == 'ios' ? 0.2 : 0,
    borderColor: 'rgba(0, 0, 0, 0.16)',
  },
  box: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  icon: {alignSelf: 'center', padding: rw(8)},
  headerTitle: {marginBottom: rh(4)},
});

const multiSearch = StyleSheet.create({
  touchableWithoutFeedback: {flex: 1},
  close: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  subTitle: {
    marginBottom: 24,
  },
  searchTextInput: {
    flexDirection: 'row',
    width: rw(312),
    justifyContent: 'space-between',
  },
  textInput: {
    borderBottomWidth: 2,
    borderColor: '#0C5273',
    margin: 0,
    padding: 0,
    minHeight: rh(32),
    maxHeight: rh(getScreenDim.height / 6),
    fontSize: 16,
    fontWeight: '500',
    color: '#141414',
    textAlignVertical: 'center',
    textAlign: 'justify',
    flex: 1,
    paddingBottom: 8,
  },
  textInputSubTitle: {marginTop: 4},
  container: {
    flexDirection: 'column',
    backgroundColor: 'white',
    justifyContent: 'space-between',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  top_container: {
    marginHorizontal: rh(24),
    marginTop: 16,
    marginBottom: rh(32),
  },
  bottom_container: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 16,
  },
  header_container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  header_text: {
    fontSize: 16,
    fontWeight: '900',
    fontFamily: 'JioType-Medium',
    color: '#141414',
  },
  btn: {
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 1000,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearBtn: {
    borderColor: '#E0E0E0',
    paddingHorizontal: 53.5,
  },
  searchBtn: {
    backgroundColor: '#0078AD',
    borderColor: '#0078AD',
    paddingHorizontal: 35,
  },
  text: {
    fontSize: 16,
    fontWeight: '700',
    fontFamily: 'JioType-Medium',
  },
  clear_text: {
    color: '#0C5273',
  },
  search_text: {
    color: '#FFFFFF',
  },
});

export default styles;
export {multiSearch};
