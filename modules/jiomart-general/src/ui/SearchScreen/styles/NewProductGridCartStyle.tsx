import {StyleSheet} from 'react-native';
import { rw } from '../../../../../jiomart-common/src/JMResponsive';

const styles = StyleSheet.create({
  wishlist: {position: 'absolute', top: 4, right: 4},
  container: {
    alignSelf: 'flex-start',
    rowGap: 4,
    backgroundColor: '#ffffff',
    borderRadius: 8, // iOS
    shadowColor: '#000', // Shadow color
    shadowOffset: {width: 0, height: 4}, // Offset for shadow
    shadowOpacity: 0.08, // Opacity of shadow (14% in hex is approximately 0.08)
    shadowRadius: 8, // Blur radius of the shadow
    // Android
    elevation: 5, // Elevation level (higher values create stronger shadows)
    flexGrow: 1,
  },
  image: {
    width: rw(80),
    height: rw(80),
    marginTop: 8,
    marginHorizontal: 20,
    alignSelf: 'center',
  },
  wishlist: {position: 'absolute', top: 4, right: 4},
  title: {marginLeft: 8, marginRight: 4, width: rw(120)},
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 8,
    marginRight: 4,
  },
  stridePrice: {textDecorationLine: 'line-through'},
  discount: {width: rw(40)},
  cart: {
    marginLeft: 'auto',
    // marginTop: 'auto',
    marginBottom: 4,
    marginRight: 4,
  },
  hyperLocal: {margin: 4, marginTop: 'auto', paddingVertical: 4, flex: 1},
  activeOpacity: {
    opacity: 1,
  },
  deActiveOpacity: {
    opacity: 0,
  },
});

export default styles;
