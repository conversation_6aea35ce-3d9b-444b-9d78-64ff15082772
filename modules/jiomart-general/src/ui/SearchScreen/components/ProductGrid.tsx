import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, FlatList, StyleSheet} from 'react-native';
import TextChip from '../../TextChip';
import NewProductGridCard from './NewProductGridCard';
import {
  isHyperLocal,
  isScheduleDelivery,
} from '../controller/useSearchScreenController';
import useRedirection from '../../../hooks/useRedirection';
import {JMSharedViewModel} from '../../../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../../../jiomart-common/src/SourceType';
import {rw, rh} from '../../../../../jiomart-common/src/JMResponsive';

interface SearchHorizontalListProps {
  data: any;
  title: string;
  onPress: (brand: string) => void;
  maxItemsVisible?: number;
  borderRadius?: any;
  showShadowTextChip?: boolean;
}
const SCREEN_WIDTH = Dimensions.get('window').width;

const ProductGrid = (props: {template: SearchHorizontalListProps}) => {
  const {title, maxItemsVisible, data} = props?.template;
  const {redirectToPdp} = useRedirection();

  if (!data || !data?.length) {
    return null;
  }

  return (
    <>
      <JioText
        text={title}
        appearance={JioTypography.HEADING_XXS}
        color={'primary_grey_100'}
        style={styles.recommendedTextHeading}
      />
      <FlatList
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        scrollEnabled={data?.length > 2}
        data={data.slice(0, maxItemsVisible)}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({item}) => {
          return (
            <NewProductGridCard
              productCode={
                JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
                  ? item?.item_code
                  : item?.uid
              }
              sellerId={item?.seller_id}
              title={item?.name}
              image={item?.medias?.[0]?.url}
              sellingPrice={item?.price?.effective?.min}
              stridePrice={item?.price?.marked?.min}
              discount={item?.discount}
              verticalCode={item?.attributes['vertical-code']}
              isQuickDeliveryLable={{
                hyperLocal: isHyperLocal(
                  item?.seller_id,
                  item?.attributes['vertical-code'],
                ),
                IcScheduleDelivery: isScheduleDelivery(
                  item?.seller_id,
                  item?.attributes['vertical-code'],
                ),
                displayText: 'Scheduled Delivery By',
              }}
              slug={item?.slug}
              onPress={() => {
                redirectToPdp({
                  item: {
                    params: {
                      slug: item?.slug,
                    },
                  },
                  verticalCode: ['GROCERIES'],
                  vertical: item?.attributes?.['vertical-code'],
                  url: item?.url,
                });
              }}
              onCartPress={() => {}}
              onWishlistPress={() => {}}
              sellable={item?.sellable}
              itemSize={item?.sizes?.[0]}
              storeId={item?.store_id}
              articleAssignment={item?.article_assignment}
              minQty={item?.minQty}
              maxQty={item?.maxQty}
            />
          );
        }}
        contentContainerStyle={styles.container}
      />
    </>
  );
};

export default React.memo(ProductGrid);

const styles = StyleSheet.create({
  container: {
    columnGap: 8,
    paddingHorizontal: 24,
    paddingVertical: 8,
  },
  flex: {
    flex: 1,
  },
  recommendedTextHeading: {
    marginHorizontal: rw(24),
    marginTop: rh(20),
    marginBottom: rh(12),
  },
});
