import {View} from 'react-native';
import JioMartShimmer from '../../../JioMartShimmer';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import { getChipHeight, getChipWidth, getScreenWidth } from '../../../../../../jiomart-common/src/utils/JMCommonFunctions';

export const RecommendedProductsShimmer = () => {
  const arr = [1, 2, 3, 4];
  return (
    <View style={{marginHorizontal: 24}}>
      <JioMartShimmer
        width={getChipWidth() * 191}
        height={getChipHeight() * 24}
        kind={ShimmerKind.RECTANGLE}
        style={{borderRadius: 16, marginVertical: 12 * getChipHeight()}}
      />
      <View style={{flexDirection: 'row', width: getScreenWidth()}}>
        {arr.map((item, index) => {
          return (
            <View key={index}>
              <JioMartShimmer
                width={getChipWidth() * 132}
                height={getChipHeight() * 132}
                kind={ShimmerKind.RECTANGLE}
                style={{borderRadius: 16}}
              />
              <JioMartShimmer
                width={getChipWidth() * 132}
                height={getChipHeight() * 22}
                kind={ShimmerKind.RECTANGLE}
                style={{
                  marginRight: 20 * getChipWidth(),
                  marginTop: 4 * getChipHeight(),
                  borderRadius: 16,
                }}
              />
              <JioMartShimmer
                width={getChipWidth() * 60}
                height={getChipHeight() * 18}
                kind={ShimmerKind.RECTANGLE}
                style={{
                  marginRight: 20 * getChipWidth(),
                  marginTop: 4 * getChipHeight(),
                  borderRadius: 16,
                }}
              />
              <JioMartShimmer
                width={getChipWidth() * 120}
                height={getChipHeight() * 18}
                kind={ShimmerKind.RECTANGLE}
                style={{
                  marginRight: 20 * getChipWidth(),
                  marginTop: 4 * getChipHeight(),
                  borderRadius: 16,
                }}
              />
              <JioMartShimmer
                width={getChipWidth() * 132}
                height={getChipHeight() * 26}
                kind={ShimmerKind.RECTANGLE}
                style={{
                  marginRight: 20 * getChipWidth(),
                  marginTop: 8 * getChipHeight(),
                  borderRadius: 1000,
                }}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};
