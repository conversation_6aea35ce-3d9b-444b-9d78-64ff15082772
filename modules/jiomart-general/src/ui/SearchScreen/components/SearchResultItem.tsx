import React from 'react';
import {StyleSheet, View} from 'react-native';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconKind,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import { getChipHeight, getChipWidth } from '../../../../../jiomart-common/src/utils/JMCommonFunctions';

interface SearchResultItemProps {
  searchResultItem?: string;
  onPress?: () => void;
}
const SearchResultItem = ({
  searchResultItem,
  onPress,
}: SearchResultItemProps) => {
  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <>
        <View style={styles.itemContainer}>
          <JioIcon
            ic={'IcSearch'}
            size={IconSize.LARGE}
            color={IconColor.GREY60}
            kind={IconKind.BACKGROUND}
          />
          <JioText
            text={searchResultItem}
            maxLines={2}
            appearance={JioTypography.BODY_XS}
            color={'primary_grey_100'}
            style={{
              paddingLeft: 16,
              maxWidth: '90%',
            }}
          />
        </View>
        {/* <View style={styles.divider} /> */}
      </>
    </TouchableWithoutFeedback>
  );
};
export default SearchResultItem;

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 24 * getChipWidth(),
    paddingTop: 9 * getChipHeight(),
    paddingBottom: 8 * getChipHeight(),
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 24,
  },
});
