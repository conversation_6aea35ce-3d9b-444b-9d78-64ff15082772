import {
  StyleSheet,
  View,
  ViewStyle,
  type StyleProp,
  type TextStyle,
} from 'react-native';
import React, {FC} from 'react';
import {JioText} from '@jio/rn_components';
import {
  JioTypography,
  type ColorToken,
} from '@jio/rn_components/src/index.types';

interface QCTagProps {
  icon: React.JSX.Element;
  color: string;
  textColor: string;
  primaryText: string;
  secondaryText: string;
  style?: StyleProp<ViewStyle>;
  textstyle?: StyleProp<TextStyle>;
  maxLine?: number;
  minLines?: number;
}

const QCTag: FC<QCTagProps> = ({
  color,
  textColor = 'sparkle_70',
  primaryText,
  secondaryText,
  style,
  icon,
  textstyle,
}) => {
  return (
    <View style={[styles.tagContainer, style, {backgroundColor: color}]}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        {React.isValidElement(icon) && <>{React.cloneElement(icon)}</>}
        <JioText
          appearance={JioTypography.BODY_XXS_BOLD}
          color={textColor as keyof ColorToken}
          text={primaryText}
          style={[textstyle, {marginLeft: 1}]}
        />
      </View>
      <JioText
        appearance={JioTypography.BODY_XXS_BOLD}
        color={textColor as keyof ColorToken}
        text={secondaryText}
        style={textstyle}
      />
    </View>
  );
};

export default QCTag;

const styles = StyleSheet.create({
  tagContainer: {
    backgroundColor: '#E5F7EE',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    columnGap: 4,
    alignItems: 'center',
    flex: 1,
  },
});
