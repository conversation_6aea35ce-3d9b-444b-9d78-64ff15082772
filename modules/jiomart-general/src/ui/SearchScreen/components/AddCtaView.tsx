import React from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {useColor} from '@jio/rn_components/src/theme/color/useColor';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {
  ActivityIndicator,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {AddCtaViewProps} from '../types/AddCtaModel';
import useAddCtaViewController from '../controller/useAddCtaViewController';
// import handleHapticFeedback from '../../../utilities/JMHapticFeedback';

const AddCtaView = (props: AddCtaViewProps) => {
  const buttonBorderColor = useColor('primary_grey_40');
  const activityIndicatorColor = useColor('primary_60');
  const {
    isProductInCart,
    loader,
    hideAddButton,
    initiateAddToCartHandle,
    updateCartProductCount,
  } = useAddCtaViewController(props);

  return (
    <View
      style={[
        styles.container,
        props.style,
        props?.stretchButton ? styles.stretch : {},
      ]}>
      {!isProductInCart?.quantity && !hideAddButton ? (
        <TouchableOpacity
          style={[
            styles.buttonWrapper,
            props?.stretchButton ? styles.buttonStretch : {},
            props?.disableDefaultAddToCartFunc ? props.button?.style : {},
            {borderColor: buttonBorderColor},
          ]}
          onPress={() => {
            // if (Platform.OS == 'android') handleHapticFeedback('impactMedium');
            // else handleHapticFeedback('impactLight');
            if (props.disableDefaultAddToCartFunc) {
              props.onPress?.();
              return;
            }
            initiateAddToCartHandle();
          }}
          activeOpacity={0.9}>
          <>
            <JioText
              text={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.text?.text
                  : 'Add'
              }
              appearance={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.text?.appearance
                  : JioTypography.BUTTON
              }
              color={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.text?.color
                  : 'primary_60'
              }
              // {...props.button?.text}
            />
            <JioIcon
              ic={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.icon?.ic
                  : 'IcAdd'
              }
              size={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.icon?.size
                  : IconSize.SMALL
              }
              color={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.icon?.color
                  : IconColor.PRIMARY60
              }
              // {...props?.button?.icon}
            />
          </>
        </TouchableOpacity>
      ) : (
        <View
          style={[
            styles.buttonIconWrapper,
            props?.stretchButton ? styles.flexEnd : {},
            loader ? {columnGap: 7.5} : {},
          ]}>
          <TouchableOpacity
            style={[styles.buttonIcon, {borderColor: buttonBorderColor}]}
            onPress={() => {
              //   if (Platform.OS == 'android')
              //     handleHapticFeedback('impactMedium');
              //   else handleHapticFeedback('impactLight');
              if (!loader) {
                updateCartProductCount(false);
              }
            }}
            activeOpacity={0.9}>
            <JioIcon
              ic={'IcMinus'}
              size={IconSize.SMALL}
              color={IconColor.PRIMARY60}
            />
          </TouchableOpacity>
          {loader ? (
            <ActivityIndicator
              color={activityIndicatorColor}
              style={{marginHorizontal: -2}}
            />
          ) : (
            <JioText
              text={`${isProductInCart?.quantity ?? ''}`}
              appearance={JioTypography.BODY_XS}
              color={'primary_grey_80'}
            />
          )}
          <TouchableOpacity
            style={[
              styles.buttonIcon,
              {borderColor: buttonBorderColor},
              isProductInCart &&
              (isProductInCart?.moq?.maximum ??
                isProductInCart?.moq?.minimum) === isProductInCart?.quantity
                ? {opacity: 0.5}
                : null,
            ]}
            onPress={() => {
              if (
                (isProductInCart?.moq?.maximum ??
                  isProductInCart?.moq?.minimum) === isProductInCart?.quantity
              ) {
                return;
              }
              if (!loader) {
                // if (Platform.OS == 'android')
                //   handleHapticFeedback('impactMedium');
                // else handleHapticFeedback('impactLight');
                updateCartProductCount(true);
              }
            }}
            activeOpacity={0.9}>
            <JioIcon
              ic={'IcAdd'}
              size={IconSize.SMALL}
              color={IconColor.PRIMARY60}
            />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default React.memo(AddCtaView);

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-end',
    marginTop: 'auto',
  },
  buttonWrapper: {
    paddingVertical: 4,
    paddingHorizontal: 16,
    borderWidth: 1,
    alignSelf: 'flex-start',
    borderRadius: 100,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 8,
  },
  buttonIconWrapper: {
    flexDirection: 'row',
    columnGap: 12,
    alignItems: 'center',
  },
  buttonIcon: {
    padding: 8,
    borderWidth: 1,
    alignSelf: 'flex-start',
    borderRadius: 100,
  },
  stretch: {alignSelf: 'stretch'},
  buttonStretch: {alignSelf: 'stretch', justifyContent: 'space-between'},
  flexEnd: {alignSelf: 'flex-end'},
});
