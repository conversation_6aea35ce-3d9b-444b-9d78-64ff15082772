import React from 'react';
import {View} from 'react-native';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import {RecentSearchProps} from '../types/JMSearchScrennType';
import styles from '../styles/RecentSearchStyle';
import TextChip from '../../TextChip';
import useRecentSearchController from '../controller/useRecentSearchController';

const RecentSearch = (props: {template: RecentSearchProps; onClick: any}) => {
  const {title, visibleSearchKeywordCount, actionContentItems} =
    props?.template;

  const buttonTitle = actionContentItems?.[0]?.showElement
    ? actionContentItems?.[0]?.title
    : '';

  const {
    loading,
    recentSearchData,
    chipBorderColor,
    setRecentSearchData,
    clearRecentSearchData,
  } = useRecentSearchController();

  if (loading) {
    return <View />;
  }

  return recentSearchData && recentSearchData.length > 0 ? (
    <>
      <View style={styles.container}>
        <JioText
          text={title}
          appearance={JioTypography.HEADING_XXS}
          color={'primary_grey_100'}
        />
        {buttonTitle ? (
          <TouchableWithoutFeedback
            onPress={() => {
              clearRecentSearchData(setRecentSearchData);
            }}>
            <JioText
              text={buttonTitle}
              appearance={JioTypography.BODY_XS_LINK}
              color={'primary_60'}
            />
          </TouchableWithoutFeedback>
        ) : null}
      </View>
      <View style={styles.chipContainer}>
        {recentSearchData
          .slice(0, visibleSearchKeywordCount)
          .map((item, index) => {
            return (
              <View key={`r-c-${index}`} style={styles.recenSearchContainer}>
                <TextChip
                  onPress={() => {
                    props?.onClick(item);
                  }}
                  text={item}
                  containerStyle={[
                    styles.containerStyle,
                    {
                      borderColor: chipBorderColor,
                    },
                  ]}
                />
              </View>
            );
          })}
      </View>
    </>
  ) : null;
};

export default RecentSearch;
