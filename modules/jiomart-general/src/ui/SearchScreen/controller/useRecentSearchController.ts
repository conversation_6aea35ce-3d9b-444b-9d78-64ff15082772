import {useColor} from '@jio/rn_components/src/theme/color/useColor';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useFocusEffect} from '@react-navigation/native';
import {useCallback, useState} from 'react';
import {
  getPrefString,
  removeStringPref,
} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';

const useRecentSearchController = () => {
  const [recentSearchData, setRecentSearchData] = useState(['']);
  const [loading, setLoading] = useState(true);
  const chipBorderColor = useColor('primary_grey_40');

  useFocusEffect(
    useCallback(() => {
      getRecentSearchData();
    }, []),
  );

  const getRecentSearchData = async () => {
    const data = await getPrefString(AsyncStorageKeys.RECENT_SEARCH);
    var jsonData = [];
    if (data != null) {
      jsonData = JSON.parse(data);
      setRecentSearchData(jsonData);
    } else {
      setRecentSearchData([]);
    }
    setLoading(false);
  };

  const clearRecentSearchData = async (setRecentSearchData: any) => {
    await removeStringPref(AsyncStorageKeys.RECENT_SEARCH);
    setRecentSearchData([]);
  };

  return {
    recentSearchData,
    loading,
    chipBorderColor,
    clearRecentSearchData,
    setRecentSearchData,
  };
};

export default useRecentSearchController;
