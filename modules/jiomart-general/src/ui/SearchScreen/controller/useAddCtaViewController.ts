import {useCallback, useEffect, useState} from 'react';
import {AddCtaViewProps} from '../types/AddCtaModel';
// import useCartViewModel from '../ViewModel/useCartViewModel';
// import {useJMToast} from '../../../../utilities/hooks/JMToastProvider';
import {NativeModules, Platform} from 'react-native';
// import {ToastMessage} from '../../../../config/Message';
import {CartDetailResponseItem} from '../../../../modules/CartResponse';
// import {ToastMessageType} from '../../../../utilities/Toast';
// import {ToastSetting} from '../../../../config/toastSetting';
// import useCartDataHandler from '../DataHandler/useCartDataHandler';
import { ApiConstant } from '../../../../../jiomart-networkmanager/src/JMNetworkController/APIConstant';
const {AuthModule, JMRNNavigatorModule} = NativeModules;

// let BUY_NOW_ACTION_WEB_URL = `${ApiConstant.hostURL}/cart/checkout?cart_id=CART_ID&buy_now=true`;
let orderReviewDeeplinkDictionary = {
  TransitionStyle: 0,
  NavigationTitle: 'Order Review',
  ActionTag: '100',
  ActionWebURL: `${ApiConstant.hostURL}/cart/checkout?cart_id=CART_ID&buy_now=true`,
  ViewIdentifier: '300',
  ActionType: 'T001',
  DeliverToText: 'Deliver to',
  DeeplinkIdentifier: 'order_review',
  callActionLink: 'jiomart_order_review',
  PageType: 'external',
  HeaderType: 2,
  IsDeliverToViewHidden: true,
};

const useAddCtaViewController = (props: AddCtaViewProps) => {
  const {
    runPriceApi,
    uid,
    size,
    slug,
    seller_id,
    store_id,
    article_assignment,
    product_group_tag,
    article_id,
    isExchangeApplied,
    exchangeData,
    installation,
    warranty,
    screen,
    appsFlyer,
    meta,
  } = props;
//   const {initiateAddToCart, updateAddToCart, buyNow, fetchCart, setHideButton} =
//     useCartViewModel();
//   const cartData = useCartDataHandler();
//   const {showToast} = useJMToast();
  const [isProductInCart, setIsProductInCart] =
    useState<CartDetailResponseItem | null>(null);
  const [buyNowClicked, setBuyNowClicked] = useState(false);

  const extractIsProductInCartFromCart = useCallback(
    (extractProduct: any) => {
      const product = extractProduct?.filter(
        (cartItem: any) => cartItem?.product?.uid === uid,
      );

      if (product && product?.length > 0) {
        setIsProductInCart(product[0]);
      } else {
        setIsProductInCart(null);
      }
    },
    [uid],
  );

  const initiateAddToCartHandle = useCallback(() => {
    const body = {
      runPriceApi,
      item_id: uid,
      size,
      productQty: 1,
      slug,
      seller_id,
      store_id,
      article_assignment,
      product_group_tag,
      article_id,
      isExchangeApplied,
      exchangeData,
      cart_id: cartData?.payload?.cart?.id ?? cartData?.payload?.id,
      installation,
      warranty,
      meta,
    };
    // initiateAddToCart(body);
    sendAppsFlyerEventForCart({...appsFlyer, quantity: 1}, true);
  }, [
    appsFlyer,
    article_assignment,
    article_id,
    cartData?.payload?.cart?.id,
    cartData?.payload?.id,
    exchangeData,
    initiateAddToCart,
    installation,
    isExchangeApplied,
    meta,
    product_group_tag,
    runPriceApi,
    seller_id,
    size,
    slug,
    store_id,
    uid,
    warranty,
  ]);

  const buyNowHandle = useCallback(async () => {
    try {
      const isLoginData = await AuthModule.isLoggedIn();
      if (!isLoginData) {
        await AuthModule.openLoginPage(false);
        return;
      }
      const body = {
        item_id: uid,
        size,
        productQty: 1,
        slug,
        seller_id,
        store_id,
        article_assignment,
        product_group_tag,
        article_id,
      };
      buyNow(body);
    } catch (error) {
      // console.log('🚀 ~ buyNowHandle ~ error:', error);
    }
  }, [
    article_assignment,
    article_id,
    buyNow,
    product_group_tag,
    seller_id,
    size,
    slug,
    store_id,
    uid,
  ]);

  const updateCartProductCount = useCallback(
    async (isAdding: boolean) => {
      let operation = 'update_item';
      if (!isAdding && isProductInCart?.quantity === 1) {
        operation = 'remove_item';
      }

      const body = {
        size,
        slug,
        productQty: !isAdding
          ? (isProductInCart?.quantity ? isProductInCart?.quantity : 0) - 1
          : (isProductInCart?.quantity ? isProductInCart?.quantity : 0) + 1,
        item_id: uid,
        identifiers: isProductInCart?.identifiers,
        article_id: isProductInCart?.article.uid,
        parent_item_identifiers: isProductInCart?.parent_item_identifiers,
        operation,
        cartData: cartData?.payload,
        meta,
      };
      updateAddToCart(body);
      sendAppsFlyerEventForCart(
        {
          ...appsFlyer,
          quantity: !isAdding
            ? (isProductInCart?.quantity ? isProductInCart?.quantity : 0) - 1
            : (isProductInCart?.quantity ? isProductInCart?.quantity : 0) + 1,
        },
        isAdding,
      );
    },
    [
      appsFlyer,
      cartData?.payload,
      isProductInCart?.article.uid,
      isProductInCart?.identifiers,
      isProductInCart?.parent_item_identifiers,
      isProductInCart?.quantity,
      meta,
      size,
      slug,
      uid,
      updateAddToCart,
    ],
  );
  const redirectOrderReviewPage = useCallback(
    async (cart_id: string) => {
      try {
        const defaultAddress: string =
          await JMRNNavigatorModule.fetchDefaultAddressId();
        let actionWebURL = '';
        if (defaultAddress) {
          actionWebURL = `${ApiConstant.hostURL}/cart/checkout?cart_id=${cart_id}&buy_now=true&address_id=${defaultAddress}&billing_address_id=${defaultAddress}`;
        } else {
          actionWebURL = `${ApiConstant.hostURL}/cart/checkout?cart_id=${cart_id}&buy_now=true`;
        }
        const modifiedOrderReviewDeeplinkDictionary = {
          ...orderReviewDeeplinkDictionary,
          ActionWebURL: actionWebURL,
        };
        await JMRNNavigatorModule.checkAndPushControllerAsPerDictionaryValue(
          Platform.OS === 'ios'
            ? modifiedOrderReviewDeeplinkDictionary
            : JSON.stringify(modifiedOrderReviewDeeplinkDictionary),
        );
      } catch (error) {
        // showToast({
        //   text: ToastMessage.SOMETHING_WENT_WRONG,
        //   type: ToastMessageType.ERROR,
        //   duration: 5000,
        //   ...ToastSetting[screen],
        // });
      }
    },
    [screen/*, showToast*/],
  );

  useEffect(() => {
    if (
      cartData?.payload?.cart?.success !== false &&
      cartData?.payload?.cart?.buy_now &&
      buyNowClicked
    ) {
      redirectOrderReviewPage(cartData?.payload?.cart?.id);
      setBuyNowClicked(false);
      fetchCart();
    }

    extractIsProductInCartFromCart(
      cartData?.payload?.items ?? cartData?.payload?.cart?.items,
    );
  }, [uid, cartData]);

  useEffect(() => {
    if (!isProductInCart) {
      setHideButton(uid, false);
    }
  }, [isProductInCart]);

  return {
    isProductInCart,
    loader: cartData?.loading?.[uid],
    hideAddButton: cartData?.hideAddToCartButton?.[uid],
    initiateAddToCartHandle,
    updateCartProductCount,
    buyNowHandle,
    setBuyNowClicked,
  };
};

const sendAppsFlyerEventForCart = (appsFlyer, isAdding) => {
//   var appsFlyerParam = {};

//   if (isAdding) {
//     appsFlyerParam = {
//       [AppsFlyerEventParameterName.PRICE]: appsFlyer?.price?.toString(),
//       [AppsFlyerEventParameterName.CONTENT_ID]: appsFlyer?.content_id,
//       [AppsFlyerEventParameterName.CONTENT_TYPE]: appsFlyer?.content_type,
//       [AppsFlyerEventParameterName.CURRENCY]: appsFlyer?.currency,
//       [AppsFlyerEventParameterName.QUANTITY]: appsFlyer?.quantity,
//       [AppsFlyerEventParameterName.L1_CATEGORY]: appsFlyer?.l1_category,
//       [AppsFlyerEventParameterName.L2_CATEGORY]: appsFlyer?.l2_category,
//       [AppsFlyerEventParameterName.L3_CATEGORY]: appsFlyer?.l3_category,
//       [AppsFlyerEventParameterName.BRAND_NAME]: appsFlyer?.brand_name,
//       [AppsFlyerEventParameterName.SKU_NAME]: appsFlyer?.sku_name,
//     };
//   } else {
//     appsFlyerParam = {
//       [AppsFlyerEventParameterName.CONTENT_ID]: appsFlyer?.content_id,
//       [AppsFlyerEventParameterName.CONTENT_TYPE]: appsFlyer?.content_type,

//       [AppsFlyerEventParameterName.L1_CATEGORY]: appsFlyer?.l1_category,
//       [AppsFlyerEventParameterName.L2_CATEGORY]: appsFlyer?.l2_category,
//       [AppsFlyerEventParameterName.L3_CATEGORY]: appsFlyer?.l3_category,
//       [AppsFlyerEventParameterName.BRAND_NAME]: appsFlyer?.brand_name,
//       [AppsFlyerEventParameterName.SKU_NAME]: appsFlyer?.sku_name,
//     };
//   }

//   sendAppsFlyerEventLogs(
//     isAdding
//       ? AppsFlyerEventType.ADD_TO_CART
//       : AppsFlyerEventType.REMOVE_FROM_CART,
//     appsFlyerParam,
//   );
};

export default useAddCtaViewController;
