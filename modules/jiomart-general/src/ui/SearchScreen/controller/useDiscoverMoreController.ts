import {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {useColor} from '@jio/rn_components';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SearchService from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMSearchAPINetworkController';
import {AutoCompleteResponse} from '../types/JMSearchAutoCompleteResponse';
import {
  addStringPref,
  getPrefString,
  removeStringPref,
} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';

const useDiscoverMoreController = (config: any) => {
  console.log('useDiscoverMoreController config:', config);
  const [discoverMoreData, setDiscoverMoreData] = useState<
    AutoCompleteResponse | null | undefined
  >();
  const [loading, setLoading] = useState(true);
  const ChipBorderColor = useColor('primary_grey_40');

  useEffect(() => {
    getDiscoverMoreData();
  }, []);

  const filteredItemList = discoverMoreData?.filter?.(
    item => item?.display && item.display.trim() !== '',
  );

  const storeDiscoverMoreData = async (data: AutoCompleteResponse) => {
    await addStringPref(AsyncStorageKeys.DISCOVER_MORE, JSON.stringify(data));
  };

  const getDiscoverMoreData = async () => {
    try {
      const data = await getPrefString(AsyncStorageKeys.DISCOVER_MORE);
      var jsonData = [];
      if (data != null) {
        jsonData = JSON.parse(data);
        setDiscoverMoreData(jsonData);
        setLoading(false);
      } else {
        fetchDiscoverMoreData();
      }
    } catch {
      fetchDiscoverMoreData();
    }
  };

  const fetchDiscoverMoreData = async () => {
    const configApiId = config?.algoliaSearchConfiguration?.uiPathID;
    const configApiHash = config?.algoliaSearchConfiguration?.cardViewID;
    const algoliaIndex = config?.algoliaSearchConfiguration?.algoliaIndexName;
    const HitsPerPageForQuery =
      config?.algoliaSearchConfiguration?.hitsPerPageForDiscoverMoreQuery;
    const hitsPerPage = HitsPerPageForQuery;

    const algoliaConfig = {
      algoliaConfig: null,
      query: '',
      algoliaIndex: algoliaIndex,
      hitsPerPage: hitsPerPage,
      configApiId: configApiId,
      configApiHash: configApiHash,
    };

    await SearchService.fetchDiscoverMoreData(algoliaConfig)
      .then(data => {
        storeDiscoverMoreData(data as AutoCompleteResponse);
        setDiscoverMoreData(data as AutoCompleteResponse);
        setLoading(false);
      })
      .catch(error => setLoading(false));
  };

  return {
    discoverMoreData,
    filteredItemList,
    ChipBorderColor,
    loading,
  };
};

export default useDiscoverMoreController;
