import {WishlistToggleProps} from '../types/WishlistToggleType';
import {Platform} from 'react-native';
import {useWishlistItems} from '../../../../../jiomart-wishlist/src/hooks/useWishlist';
import useWishlistOperation from '../../../../../jiomart-wishlist/src/hooks/useWishlistOperation';
import handleHapticFeedback from '../../../../../jiomart-common/src/utils/JMHapticFeedback';

const useWishlistToggleViewController = (props: WishlistToggleProps) => {
  const {request, style, onPressWishlist} = props;
  const wishlistToggle = useWishlistItems({uid: request.uid});
  const {addToWishlist, removeFromWishlist, generateAddToWishlistRequest, generateRemoveToWishlistRequest} = useWishlistOperation();
  const actionWishlist = async () => {
    if (Platform.OS === 'android') {
      handleHapticFeedback('impactMedium');
    } else {
      handleHapticFeedback('impactLight');
    }
    // if (1 === 1) {
    //   console.log('Open Login');
    //   return;
    // }
    if (wishlistToggle) {
      removeFromWishlist.mutate(generateRemoveToWishlistRequest(request));
      onPressWishlist?.('remove');
      return;
    }
    addToWishlist.mutate(generateAddToWishlistRequest(request));
    onPressWishlist?.('add');
  };

  // useEffect(() => {
  //   // if (wishlistData?.wishlistDataMap?.includes?.(Number(uid))) {
  //   //   setWishlistIconToggle(true);
  //   // } else {
  //   //   setWishlistIconToggle(false);
  //   // }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [uid]);

  return {style, actionWishlist, wishlistToggle};
};

export default useWishlistToggleViewController;
