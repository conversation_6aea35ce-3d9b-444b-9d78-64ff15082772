import {StyleProp, ViewStyle} from 'react-native';
import type {
  JioIconProps,
  JioTextProps,
} from '@jio/rn_components/src/index.types';
import {AppsFlyerInterface} from './AppsFlyerInterface';
import {NavigationBean} from '../../../../../jiomart-common/src/JMNavGraphUtil';

export interface AddCtaViewProps {
  inverse?: boolean;
  style?: StyleProp<ViewStyle>;
  title?: string;
  stretchButton?: boolean;
  runPriceApi?: boolean;
  uid: number | null;
  size: string;
  slug: string;
  seller_id?: number | null;
  store_id?: number | null;
  article_assignment?: any;
  article_id?: any;
  product_group_tag?: any;
  disabled?: boolean;
  isExchangeApplied?: boolean;
  exchangeData?: any;
  installation?: any;
  warranty?: any;
  meta?: {vertical_code: string};
  screen?: NavigationBean;
  appsFlyer?: AppsFlyerInterface;
  disableDefaultAddToCartFunc?: boolean;
  onPress?: () => void;
  button?: {
    text?: JioTextProps;
    icon?: JioIconProps;
    style?: StyleProp<ViewStyle>;
  };
}
