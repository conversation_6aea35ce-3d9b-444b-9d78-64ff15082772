import {
  ScrollView,
  Keyboard,
  FlatList,
  StyleSheet,
  NativeModules,
  View,
} from 'react-native';
import React, {useState, useCallback, useRef} from 'react';
import ScreenSlot, {DeeplinkHandler} from '../JMScreenSlot';
import {
  AppScreens,
  type ScreenProps,
} from '../../../../jiomart-common/src/JMAppScreenEntry';
import useJMSearchScreenController from './controller/useJMSearchScreenController';
import {SearchScreenContentProps} from './types/JMSearchScrennType';
import SearchResultItem from './components/SearchResultItem';
import BottomSheet from '../BottomSheet/BottomSheet';
import MultiSearchBottomSheet from '../BottomSheet/MultiSearchBottomSheet';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';
import useRedirection from '../../hooks/useRedirection';
import test from 'node:test';
import {url} from 'inspector';
import {renderTemplate} from '../JMTemplateController';
import { JMTemplateViewType } from '../../../../jiomart-common/src/JMTemplateViewType';

export type JProps = ScreenProps<typeof AppScreens.SEARCH_SCREEN>;
const {KeyboardHandling, JMRNUserModule} = NativeModules;

const SearchScreenContent = React.memo(
  ({
    sortedComponents,
    config,
    textInputRef,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    handleRecentSearchResult,
    openMultiSearchBtmSheet,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    onBrandItemClick,
    onCategoryItemClick,
    onSearchResultItemClick,
    sortedSspComponents,
  }: SearchScreenContentProps) => {
    const renderItem = ({item, index}: any) => {
      return (
        <SearchResultItem
          key={`s_i_${index}`}
          onPress={() => {
            onSearchResultItemClick(item, config?.searchSuggestion_List?.cta);
          }}
          searchResultItem={item}
        />
      );
    };

    const getActionCta = (JMTemplateData: any) => {
      let onClick = null;
      if (JMTemplateData?.viewType === JMTemplateViewType.SHOPPING_LIST_BLOCK) {
        onClick = () => {
          setTimeout(() => {
            textInputRef?.current?.focus();
          }, 600);
          KeyboardHandling?.nativeKeyboardEnabled(false);
          openMultiSearchBtmSheet();
        };
      } else if (
        JMTemplateData?.viewType === JMTemplateViewType.RECENT_SEARCHES
      ) {
        onClick = (text: string) => {
          handleRecentSearchResult(text, JMTemplateData?.cta);
        };
      } else if (
        JMTemplateData?.viewType === JMTemplateViewType.DISCOVER_MORE
      ) {
        onClick = (text: string) => {
          handleDiscoverMoreSearchResult(text, JMTemplateData?.cta);
        };
      } else if (
        JMTemplateData?.viewType === JMTemplateViewType.POPULAR_CATEGORY
      ) {
        onClick = (slug, page) => {
          handleTrendingCategoryResult(slug, page, JMTemplateData?.cta);
        };
      } else if (JMTemplateData?.type === 'categories') {
        onClick = (l3CategoryName: string) => {
          onCategoryItemClick(l3CategoryName, JMTemplateData?.cta);
        };
      } else if (JMTemplateData?.type === 'brands') {
        onClick = (brand: string) => {
          onBrandItemClick(brand, JMTemplateData?.cta);
        };
      }
      return onClick;
    };

    return (
      <View style={styles.container}>
        <FlatList
          data={isSearchActive ? sortedSspComponents : sortedComponents}
          ListHeaderComponent={
            isSearchActive &&
            searchResultData?.items &&
            searchResultData?.items?.length > 0 ? (
              <>
                {config?.searchSuggestion_List?.is_visible &&
                  searchResultFilteredData
                    ?.slice(
                      0,
                      config?.searchSuggestion_List?.maxItemsVisible ?? 4,
                    )
                    .map((item, index) => renderItem({item, index}))}
              </>
            ) : null
          }
          renderItem={({item, index}) =>
            renderTemplate(
              {
                ...item,
                viewType: item.viewType,
                algoliaSearchConfiguration: config?.algoliaSearchConfiguration,
                isVisibleForVersion: config?.isVisibleForVersion,
                versionNumber: config?.versionNumber,
                jiomart_header: config?.jiomart_header,
                data:
                  item.type === 'categories'
                    ? categoriesData.current
                    : item.type === 'brands'
                    ? brandsData.current
                    : searchResulExtData?.items,
                onClick: getActionCta(item),
                showShadowTextChip: true,
              },
              index,
            )
          }
        />
      </View>
    );
  },
);

const JMSearchScreen = (props: JProps) => {
  const {route, navigation} = props;
  const navigationBean = route.params;
  const [searchKeyword, setSearchValue] = useState('');
  const textInputRef = useRef(null);
  const {
    config,
    sortedComponents,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    handleAutoComplete,
    onCategoryItemClick,
    onBrandItemClick,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    multiSearchBottomSheet,
    onSearchResultItemClick,
    onChangeMultiSearch,
    sortedSspComponents,
  } = useJMSearchScreenController();

  const searchScreenMainUI = useCallback(() => {
    return (
      <SearchScreenContent
        sortedComponents={sortedComponents}
        config={config}
        textInputRef={textInputRef}
        remoteConfig={config}
        handleRecentSearchResult={handleRecentSearchResult}
        handleDiscoverMoreSearchResult={handleDiscoverMoreSearchResult}
        handleTrendingCategoryResult={handleTrendingCategoryResult}
        openMultiSearchBtmSheet={openMultiSearchBtmSheet}
        closeMultiSearchBtmSheet={closeMultiSearchBtmSheet}
        isSearchActive={isSearchActive}
        searchResultData={searchResultData ?? []}
        searchResultFilteredData={searchResultFilteredData}
        categoriesData={categoriesData}
        brandsData={brandsData}
        searchResulExtData={searchResulExtData}
        onCategoryItemClick={onCategoryItemClick}
        onBrandItemClick={onBrandItemClick}
        onSearchResultItemClick={onSearchResultItemClick}
        sortedSspComponents={sortedSspComponents}
      />
    );
  }, [
    sortedComponents,
    config,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    onCategoryItemClick,
    onBrandItemClick,
    onSearchResultItemClick,
  ]);

  const bottomSheetContent = () => {
    return (
      <>
        {config?.btm_sheet?.shopping_list_block?.is_visible ? (
          <BottomSheet
            isStretchEnabled
            maxHeightPercent={50}
            enableKeyboarAvoidingView
            onBackDropClick={closeMultiSearchBtmSheet}
            onDrag={closeMultiSearchBtmSheet}
            visible={multiSearchBottomSheet}>
            <MultiSearchBottomSheet
              textInputRef={textInputRef}
              closeMultiSearchBtmSheet={closeMultiSearchBtmSheet}
              onChangeMultiSearch={(text: string) =>
                onChangeMultiSearch(
                  text,
                  config?.btm_sheet?.shopping_list_block?.cta,
                )
              }
              config={config?.btm_sheet?.shopping_list_block}
            />
          </BottomSheet>
        ) : null}
      </>
    );
  };

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          bottomSheetContent={bottomSheetContent}
          searchTextHandler={handleAutoComplete}
          onSubmitHandler={text => {
            handleDiscoverMoreSearchResult(
              text,
              config?.searchSuggestion_List?.cta,
            );
          }}
          children={_ => {
            return searchScreenMainUI();
          }}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flex: 1,
  },
});

export default JMSearchScreen;
