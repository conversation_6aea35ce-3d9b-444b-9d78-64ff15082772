import {SafeAreaView, StyleSheet, View} from 'react-native';
import React from 'react';
import {JioButton, JioText} from '@jio/rn_components';
import {
  ButtonSize,
  JioTypography,
  type JioButtonProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import CustomMediaRendered, {Kind} from './CustomMediaRendered';

export interface NegativeScreenUIProps {
  image?: {
    uri: string;
    size?: number;
  };
  title?: JioTextProps;
  subTitle?: JioTextProps;
  isButtonVisible?: boolean;
  shouldShowContentInCenter?: boolean;
  button?: JioButtonProps;
  onPress?: () => void;
  offset?: number;
}

const NegativeScreenUI = (props: NegativeScreenUIProps) => {
  const {
    image,
    title,
    subTitle,
    isButtonVisible,
    shouldShowContentInCenter = true,
    offset,
    button,
    onPress,
  } = props;
  return (
    <SafeAreaView style={styles.flex}>
      <View style={styles.container}>
        <View
          style={[shouldShowContentInCenter ? styles.center : styles.start]}>
          <View style={styles.content}>
            {image?.uri ? (
              <CustomMediaRendered
                kind={Kind.IMAGE}
                mediaUrl={image?.uri}
                customStyles={styles.image}
                width={rw(image?.size ?? 164)}
                height={rw(image?.size ?? 164)}
                viewBox={`0 0 ${rw(image?.size ?? 120)} ${rw(
                  image?.size ?? 120,
                )}`}
              />
            ) : null}

            {title ? (
              <JioText
                appearance={JioTypography.HEADING_XXS}
                color={'primary_grey_100'}
                maxLines={1}
                textAlign="center"
                {...title}
                style={styles.marginBottom12}
              />
            ) : null}
            {subTitle ? (
              <JioText
                appearance={JioTypography.BODY_XXS}
                color={'primary_grey_80'}
                textAlign="center"
                {...subTitle}
              />
            ) : null}
          </View>
        </View>
        {isButtonVisible ? (
          <View style={[styles.bottom, {marginBottom: offset}]}>
            <JioButton
              title=""
              size={ButtonSize.LARGE}
              stretch
              onClick={onPress}
              {...button}
            />
          </View>
        ) : null}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {backgroundColor: '#ffffff', flex: 1},
  content: {
    marginHorizontal: rw(24),
    alignItems: 'center',
  },
  start: {marginTop: rh(34)},
  center: {justifyContent: 'center', flex: 1},
  image: {
    width: rw(164),
    height: rw(164),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  bottom: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    marginTop: 'auto',
  },
  marginBottom12: {
    marginBottom: 12,
  },
  flex: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default NegativeScreenUI;
