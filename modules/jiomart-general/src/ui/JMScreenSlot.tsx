import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {ActivityIndicator, BackHandler, Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import JMDeliverToBarBtmSheet from '../../../jiomart-address/src/BottomSheet/JMDeliverToBarBtmSheet';
import JMDeliverToPincodeBtmSheet from '../../../jiomart-address/src/BottomSheet/JMDeliverToPincodeBtmSheet';
import useDeliverToBarBtmSheet from '../../../jiomart-address/src/hooks/useDeliverToBarBtmSheet';
import {AuthState} from '../../../jiomart-common/src/JMAuthType';
import networkService from '../../../jiomart-common/src/JMNetworkConnectionUtility';
import useNetworkController from '../../../jiomart-common/src/JMNetworkController';
import {
  DeeplinkProps,
  GenericToast,
  ScreenSlotProps,
  genericToastTypeData,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {capitalizeFirstLetter} from '../../../jiomart-common/src/utils/JMStringUtility';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useGlobalState} from '../context/JMGlobalStateProvider';
import {
  getInitialNavBean,
  handleDeeplinkIntent,
  updatedNavBeanData,
} from '../deeplink/JMDeeplinkUtility';
import {useConfigFile} from '../hooks/useJMConfig';
import CustomBottomNavBar from './BottomNavBar/CustomBottomNavBar';
import BottomSheet from './BottomSheet/BottomSheet';
import {CustomStatusBar} from './CustomStatusBar';
import DeliverToBar from './DeliverToBar/DeliverToBar';
import JioMartHeader, {NavigationBeanType} from './Header/JioMartHeader';
import QCSlidingTab from './Qc/QCSlidingTab';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {navigateTo} from '../navigation/JMNavGraph';
import {
  navBeanObj,
  NavigationType,
  type NavigationBean,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import usePincodeChange from '../../../jiomart-address/src/hooks/usePincodeChange';
import {EventEmitterKeys} from '../../../jiomart-common/src/JMConstants';
import {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import JMToast from './JMToast';

export const userAuthenticationErrorState = () => {
  return (
    <View>
      <Text>Authentication failed</Text>
    </View>
  );
};
export const userAuthenticationLoadingState = () => {
  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <ActivityIndicator size="large" color="#0000ff" />
    </View>
  );
};

export const createEventFinalBean = (
  bean: NavigationBean,
  eventTriggerBean: NavigationBean,
): NavigationBean => {
  const baseBean = {...bean};
  return {
    ...baseBean,
    ...(eventTriggerBean?.headerType
      ? {headerType: eventTriggerBean?.headerType}
      : {}),
    ...(eventTriggerBean?.shouldShowDeliverToBar
      ? {shouldShowDeliverToBar: eventTriggerBean?.shouldShowDeliverToBar}
      : {}),
    ...(eventTriggerBean?.navTitle
      ? {navTitle: eventTriggerBean?.navTitle}
      : {}),
    ...(eventTriggerBean?.shouldShowBottomNavBar
      ? {shouldShowBottomNavBar: eventTriggerBean?.shouldShowBottomNavBar}
      : {}),
  };
};

export function DeeplinkHandler(props: DeeplinkProps): JSX.Element {
  let [navigationBeanData, setNavigationBeanData] = useState(
    getInitialNavBean(props.navigationBean),
  );

  useEffect(() => {
    // will check user is loggedin or not and set the value accordingly
    updatedNavBeanData(
      JMSharedViewModel.Instance.deeplinkUrl,
      navigationBeanData,
    ).then(bean => {
      if (bean) {
        setNavigationBeanData(bean);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <>{props?.children ? props.children(navigationBeanData) : null}</>;
}

const usePinCodeChangeController = () => {
  const {setEvent, setAddress} = useGlobalState();
  const handleAddress = async (initialAddress?: JMAddressModel) => {
    let address: any = await JMDatabaseManager.address.getDefaultAddress();
    JMLogger.log('handleAddress ', 'handleAddress ' + JSON.stringify(address));
    address = !address ? initialAddress : JSON.parse(address ?? '');
    setAddress(address);
  };

  usePincodeChange(async event => {
    console.log('usePincodeChange--', event);
    if (event?.type === 'pincode') {
      console.log('🚀 ~ event1:', event);
      setEvent({
        [EventEmitterKeys.WEB_VIEW_EVENT_EMITT]: {
          pincodeModifiedOnNative: `'${JSON.stringify({
            pin: event?.pin,
          })}'`,
        },
      });
    } else {
      console.log('🚀 ~ event?.addressId:', event?.addressId);
      setEvent({
        [EventEmitterKeys.WEB_VIEW_EVENT_EMITT]: {
          addressModifiedOnNative: `'${JSON.stringify({
            addressId: event?.addressId,
          })}'`,
        },
      });
    }
    await handleAddress();
  });
};

const ScreenSlot = (props: ScreenSlotProps) => {
  const {statusBarColor, deliverToBarData} = props;
  const {bottomsheetDismissable} = props;
  usePinCodeChangeController();
  const header: any = useConfigFile(
    JMConfigFileName.JMHeaderConfigurationFileName,
  );
  let headerConfig = header?.[props.navigationBean?.headerType ?? '2'];
  const {
    userAuthenticationStatus,
    toastTypeData,
    address,
    setToastTypeData,
    deeplinkData,
    setDeeplinkData,
  } = useGlobalState();
  const {
    showDeliverToBarBtmSheet,
    showDeliverToBarPincodeBtmSheet,
    openDeliverToBarBtmSheet,
    openDeliverToBarPincodeBtmSheet,
    closeDeliverToBarBtmSheet,
    closeDeliverToBarPincodeBtmSheet,
  } = useDeliverToBarBtmSheet();
  let deliverToBarText: string = '';
  if (address?.name && address?.name?.length > 10) {
    deliverToBarText = capitalizeFirstLetter(
      address?.name?.slice(0, 10) + '...',
    );
  } else {
    deliverToBarText = capitalizeFirstLetter(address?.name ?? '');
  }

  if (address?.name) {
    deliverToBarText +=
      ' ' +
      capitalizeFirstLetter(address?.city ?? '') +
      ' ' +
      (address?.pin ?? '');
  } else {
    deliverToBarText +=
      capitalizeFirstLetter(address?.city ?? '') + ' ' + (address?.pin ?? '');
  }

  const navigation = props.navigation;
  const insets = useSafeAreaInsets();
  const {isNetworkConnected} = useNetworkController();

  useEffect(() => {
    if (
      !isNullOrUndefinedOrEmpty(JMSharedViewModel.Instance.externalDeeplinkData)
    ) {
      const timer = setTimeout(() => {
        if (JMSharedViewModel.Instance.externalDeeplinkData) {
          const copyDeeplinkData = {
            ...JMSharedViewModel.Instance.externalDeeplinkData,
          };
          JMSharedViewModel.Instance.setExternalDeeplink(undefined);
          setDeeplinkData(copyDeeplinkData);
        }
      }, 500);
      return () => clearTimeout(timer);
    }
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      // This will run only when ScreenB is in focus
      const handleDeeplink = async () => {
        if (deeplinkData && !isNullOrUndefinedOrEmpty(deeplinkData)) {
          const navBeanData = await handleDeeplinkIntent(deeplinkData);
          if (navBeanData) {
            console.log(
              'ScreenSlot deeplinkData navBeanData ' +
                JSON.stringify(navBeanData),
            );
            setDeeplinkData(undefined);
            console.log('ScreenSlot deeplinkData navBeanData navigateTo');
            navigateTo(navBeanData, props.navigation);
          } else {
            console.log('ScreenSlot deeplinkData navBeanData Not found');
            setDeeplinkData(undefined);
          }
        }
      };
      console.log('ScreenSlot deeplinkData ' + deeplinkData);
      handleDeeplink();

      return () => {
        // Clean up or perform any action when ScreenB is unfocused
        // setDeeplinkData(undefined);
        console.log('ScreenSlot deeplinkData cleanup ' + deeplinkData);
      };
    }, [deeplinkData]),
  );

  const onBackPress = () => {
    if (
      JMSharedViewModel.Instance.previousPageURL !== '' &&
      props.onCustomBackPress
    ) {
      props.onCustomBackPress?.();
    } else if (navigation.canGoBack()) {
      if (props.onCustomBackPress) {
        props.onCustomBackPress?.();
      } else {
        navigation.goBack();
      }
    } else {
      networkService.stopMonitoring();
      BackHandler.exitApp();
    }
    props.onBackPressCallback?.();
    return true;
  };

  const handleHeaderCtaIconPress = (cta: NavigationBean) => {
    switch (cta?.type) {
      case NavigationBeanType.BACK:
        onBackPress();
        break;
      default:
        navigateTo(
          navBeanObj({
            ...cta,
            navigationType:
              NavigationType[
                (
                  cta?.navigationType ?? NavigationType.PUSH
                ).toUpperCase() as keyof typeof NavigationType
              ],
            actionUrl: `${getBaseURL() + '/' + cta?.actionUrl}`,
            headerType: cta?.headerType ?? 1,
            userJourneyRequiredState: cta?.userJourneyRequiredState ?? 0,
          }),
          navigation,
        );
        break;
    }
  };

  const searchTextHandler = (text: string) => {
    props.searchTextHandler?.(text);
    // store in redux if value !=
  };

  const onSubmitHandler = (text: string) => {
    props.onSubmitHandler?.(text);
  };

  useEffect(() => {
    if (isNetworkConnected === false) {
      setToastTypeData(genericToastTypeData(GenericToast.NO_INTERNET));
    } else if (toastTypeData?.genericToastType === GenericToast.NO_INTERNET) {
      setToastTypeData(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNetworkConnected]);

  useFocusEffect(
    useCallback(() => {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );
      return () => backHandler.remove();
    }, [props.onCustomBackPress, props.onCustomBackPress]),
  );

  useEffect(() => {
    const unsubscription = navigation.addListener('focus', () => {
      closeDeliverToBarBtmSheet();
      closeDeliverToBarPincodeBtmSheet();
    });
    return () => {
      closeDeliverToBarBtmSheet();
      closeDeliverToBarPincodeBtmSheet();
      unsubscription();
    };
  }, []);

  function getLocalUserAuthStatus(
    userAuthenticationStatus: AuthState,
  ): AuthState {
    if (
      (props.navigationBean?.userAuthenticationRequired === 1 &&
        userAuthenticationStatus === AuthState.SESSION_CREATED) ||
      props.navigationBean?.userAuthenticationRequired === 0
    ) {
      return AuthState.AUTHENTICATED;
    }
    return userAuthenticationStatus;
  }

  return (
    <View
      style={{
        flex: 1,
      }}>
      <CustomStatusBar
        color={
          statusBarColor ?? props.navigationBean?.params?.customStatusBarColor
        }
      />
      {headerConfig && (
        <JioMartHeader
          data={headerConfig}
          navTitle={props?.navigationBean?.navTitle ?? ''}
          headerTopComponent={
            headerConfig?.isQCEnabled ? <QCSlidingTab /> : null
          }
          headerBottomComponent={
            props?.navigationBean?.shouldShowDeliverToBar ? (
              <DeliverToBar
                text={deliverToBarText}
                onPress={openDeliverToBarBtmSheet}
                deliverToBarData={deliverToBarData}
              />
            ) : null
          }
          onPress={handleHeaderCtaIconPress}
          searchTextHandler={searchTextHandler}
          onSubmitHandler={onSubmitHandler}
          {...props?.header}
        />
      )}
      {props?.children
        ? props.children(
            getLocalUserAuthStatus(userAuthenticationStatus),
            openDeliverToBarBtmSheet,
          )
        : null}
      {toastTypeData !== undefined && (
        <JMToast
          message={toastTypeData?.message}
          isVisible={toastTypeData.isVisible}
          semanticState={toastTypeData?.semanticState}
          duration={toastTypeData?.duration}
          showClose={toastTypeData?.showClose}
          type={toastTypeData?.type}
          offset={insets.top}
          showButton={toastTypeData?.showButton}
          buttonText={toastTypeData?.buttonText}
          onClick={() => {
            if (toastTypeData?.cta) {
              navigateTo(toastTypeData?.cta, navigation);
            } else {
              toastTypeData?.onClick?.();
            }
            setToastTypeData(undefined);
          }}
          onDismiss={() => {
            setToastTypeData(undefined);
          }}
        />
      )}

      {props?.navigationBean?.shouldShowBottomNavBar ? (
        <CustomBottomNavBar />
      ) : null}

      <BottomSheet
        visible={showDeliverToBarBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={closeDeliverToBarBtmSheet}
        onDrag={closeDeliverToBarBtmSheet}>
        <JMDeliverToBarBtmSheet
          onClose={closeDeliverToBarBtmSheet}
          openDeliverToBarPincode={openDeliverToBarPincodeBtmSheet}
        />
      </BottomSheet>
      <BottomSheet
        visible={showDeliverToBarPincodeBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={closeDeliverToBarPincodeBtmSheet}
        onDrag={closeDeliverToBarPincodeBtmSheet}
        {...bottomsheetDismissable}>
        <JMDeliverToPincodeBtmSheet
          hideCloseButton={false}
          onClose={closeDeliverToBarPincodeBtmSheet}
        />
      </BottomSheet>

      {props?.bottomSheetContent?.({
        openDeliverToBarBtmSheet,
        openDeliverToBarPincodeBtmSheet,
      })}
    </View>
  );
};

export default ScreenSlot;
