import React from 'react';
import {TouchableOpacity} from 'react-native';
import {WishlistToggleProps} from '../SearchScreen/types/WishlistToggleType';
import useWishlistToggleViewController from '../SearchScreen/controller/useWishlistToggleViewController';
import MicroScale from '../Animation/Micro/MicroScale';
import CustomMediaRendered, {Kind} from '../CustomMediaRendered';
import {getSvgUrlData} from '../../../../jiomart-common/src/utils/JMCommonFunctions';

const WishlistToggleView = (props: WishlistToggleProps) => {
  const {actionWishlist, style, wishlistToggle} =
    useWishlistToggleViewController(props);
  return (
    <TouchableOpacity onPress={actionWishlist} style={style} hitSlop={8}>
      <MicroScale scale={1.5}>
        <CustomMediaRendered
          kind={Kind.SVG}
          mediaUrl={getSvgUrlData('CardHeart')}
          width={24}
          height={24}
          fill={wishlistToggle ? '#FC6770' : '#fff'}
          strokeWidth={wishlistToggle ? '0' : '1'}
        />
      </MicroScale>
    </TouchableOpacity>
  );
};

export default WishlistToggleView;
