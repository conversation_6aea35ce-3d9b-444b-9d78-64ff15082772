import {StyleSheet, Text} from 'react-native';
import React from 'react';
import {useTheme} from '@jio/rn_components';
import {useTypography} from '@jio/rn_components/src/typography';
import {JioTypography} from '@jio/rn_components/src/index.types';

interface JmLinkMixTextProps {
  regularString: string;
  clickableString: string;
  redirectionUrl: string;
  onPressHandler?: () => void;
}

const JmLinkMixText = ({
  regularString,
  clickableString,
  redirectionUrl,
  onPressHandler = () => {},
}: JmLinkMixTextProps) => {
  const theme = useTheme();
  const clickableTerms = clickableString.split(',');
  const redirectionUrlTerms = redirectionUrl.split(',');
  const termDataMap = clickableTerms.reduce((map, term, index) => {
    map[term] = redirectionUrlTerms[index];
    return map;
  }, {});

  // Regular Text Styling
  const textStyle = useTypography(JioTypography.BODY_S);
  const textColor = theme.primary_grey_80;

  // Clickable Text Styling
  const textStyleBold = useTypography(JioTypography.BODY_S_LINK);
  const textColorBold = theme.primary_60;

  const findNextTerm = (text: string, terms: string[]) => {
    let nextTerm = null;
    let index = -1;

    terms.forEach(term => {
      const termIndex = text.indexOf(term);
      if (termIndex !== -1 && (index === -1 || termIndex < index)) {
        nextTerm = term;
        index = termIndex;
      }
    });

    return {nextTerm, index};
  };

  const renderTextWithClickableTerms = (
    text: string,
    terms: string[],
    termMap: {[key: string]: string},
    onPressHandler: () => void,
  ) => {
    const elements = [];
    let remainingText = text;

    while (remainingText) {
      const {nextTerm, index} = findNextTerm(remainingText, terms);

      if (index === -1) {
        elements.push(
          <Text style={[textStyle, {color: textColor}]} key={remainingText}>
            {remainingText}
          </Text>,
        );
        break;
      }

      if (index > 0) {
        const beforeTerm = remainingText.substring(0, index);
        elements.push(
          <Text style={[textStyle, {color: textColor}]} key={beforeTerm}>
            {beforeTerm}
          </Text>,
        );
      }

      elements.push(
        <Text
          style={[textStyleBold, {color: textColorBold}]}
          key={nextTerm}
          onPress={() => {
            onPressHandler();
          }}>
          {nextTerm}
        </Text>,
      );

      remainingText = remainingText.substring(index + nextTerm?.length);
    }

    return elements;
  };

  return (
    <>
      <Text>
        {renderTextWithClickableTerms(
          regularString,
          clickableTerms,
          termDataMap,
          onPressHandler,
        )}
      </Text>
    </>
  );
};

export default JmLinkMixText;

const styles = StyleSheet.create({});
