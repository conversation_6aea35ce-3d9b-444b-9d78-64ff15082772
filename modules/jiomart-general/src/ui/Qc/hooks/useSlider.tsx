import {useEffect, useState} from 'react';
import {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
} from 'react-native-reanimated';
import {useConfigFile} from '../../../hooks/useJMConfig';
import {UseSliderProps, UseSliderRef} from '../types/SliderTypes';
import usePincodeChange from '../../../../../jiomart-address/src/hooks/usePincodeChange';
import {
  EventEmitterKeys,
} from '../../../../../jiomart-common/src/JMConstants';
import {useGlobalState} from '../../../context/JMGlobalStateProvider';
import { JMConfigFileName } from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

export const useSlider = (
  props: UseSliderProps,
  ref?: React.Ref<UseSliderRef>,
) => {
  const {setEvent, qcDetails} = useGlobalState();
  const tabHeight = useSharedValue(0);
  const tabWidth = useSharedValue(0);

  const [fixedTabWidth, setFixedTabWidth] = useState(0);
  const [selectedOption, setSelectedOption] = useState("Quick")
  useEffect(() => {
    const option = qcDetails?.journey === quickCommerceConfig?.quickDeliveryKey ? "Quick" : "Scheduled";
    enableOptionSelectionAnim(option)
  }, [fixedTabWidth, qcDetails]);
  
  const commanConfigData = useConfigFile(
    JMConfigFileName.JMCommonContentFileName,
  );
  const quickCommerceConfig = commanConfigData?.quickCommerceConfig;

  const slideAnim = useSharedValue(0);
  const isVisible = useSharedValue(true);
  const prevScrollY = useSharedValue(0);
  const offsetAnim = 48;
  let animationFrameId: number | null = null;
  const SCROLL_THRESHOLD = 30;
  const {shouldEnableScrollAnimation} = props;

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(isVisible.value ? 0 : -offsetAnim, {
          duration: 500,
        }),
      },
    ],
    height: withTiming(isVisible.value ? offsetAnim : 0, {duration: 300}),
    opacity: withTiming(Number(isVisible.value), {duration: 500}),
  }));

  const tabSwitchAnimatedStyle = useAnimatedStyle(() => ({
    height: tabHeight.value,
    width: tabWidth.value,
    transform: [{translateX: slideAnim.value}],
  }));

  const enableOptionSelectionAnim = (option: string) => {
    setSelectedOption(option);
    slideAnim.value = withTiming(option === 'Quick' ? 0 : fixedTabWidth, {
      duration: 300,
    });
  }

  const handleOptionChange = (option: string) => {
    const selectedTab =
      option === 'Quick'
        ? quickCommerceConfig?.quickDeliveryKey
        : quickCommerceConfig?.scheduledDeliveryKey;
        enableOptionSelectionAnim(option)
    setEvent({
      [EventEmitterKeys.WEB_VIEW_EVENT_EMITT]: {
        selectedSADOOptionOnNative: JSON.stringify({
          selectedTab: selectedTab,
        }),
      },
    });
  };

  const onScroll = (value: number) => {
    if (!shouldEnableScrollAnimation || value < 0) return;
    if (Math.abs(value - prevScrollY.value) < SCROLL_THRESHOLD) return;

    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
    }

    animationFrameId = requestAnimationFrame(() => {
      isVisible.value = value <= prevScrollY.value;
      prevScrollY.value = value;
      animationFrameId = null;
    });
  };

  return {
    selectedOption,
    tabHeight,
    tabWidth,
    animatedStyle,
    tabSwitchAnimatedStyle,
    handleOptionChange,
    onScroll,
    shouldEnableScrollAnimation,
    quickCommerceConfig,
    setFixedTabWidth
  };
};
