import { useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useConfigFile } from '../../../hooks/useJMConfig';
import { useQuery, queryOptions } from '@tanstack/react-query';
import usePincodeChange from '../../../../../jiomart-address/src/hooks/usePincodeChange';
import { AsyncStorageKeys } from '../../../../../jiomart-common/src/JMConstants';
import { JMConfigFileName } from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import { JMQCNetworkController } from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMQCNetworkController';

const useQuickCommerce = (verticalCode: string | undefined = undefined) => {
  const qcController = new JMQCNetworkController();
  const quickCommerceConfig = useConfigFile(
    JMConfigFileName.JMCommonContentFileName
  );

  useEffect(() => {
    if (quickCommerceConfig?.quickDeliveryKey) {
      AsyncStorage.setItem(
        AsyncStorageKeys.QC_KEY,
        quickCommerceConfig.quickDeliveryKey
      );
    }
  }, [quickCommerceConfig?.quickDeliveryKey]);

  usePincodeChange(async (event) => {
    if (
      // !verticalCode &&
      quickCommerceConfig?.featureEnabled
    ) {
      console.log("Qc ki Api call ho rahi hai");
      await qcController.getQCDetails();
    }
  }, [quickCommerceConfig, verticalCode]);

  // Fetch QC details on mount or verticalCode/config change
  useEffect(() => {
    const shouldFetchQC =
      ((verticalCode &&
        quickCommerceConfig?.verticalCode?.includes?.(verticalCode)) ||
        !verticalCode) &&
      quickCommerceConfig?.featureEnabled;

    if (shouldFetchQC) {
      qcController.getQCDetails();
    }
  }, [quickCommerceConfig, verticalCode]);

  // Query for QC Details
  const qcDetails = useQuery({
    queryKey: ['qcDetails'],
    queryFn: qcController.getQCDetails,
    enabled: !!quickCommerceConfig?.featureEnabled
  });
  // Output
  return {
    qcDetails,
    isQcEnabled: quickCommerceConfig?.featureEnabled || false,
    journey: '', 
    message: '', 
  };
};

export default useQuickCommerce;
