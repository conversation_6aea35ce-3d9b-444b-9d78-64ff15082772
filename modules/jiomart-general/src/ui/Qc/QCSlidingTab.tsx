import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {View, TouchableOpacity, Text} from 'react-native';
import {styles} from '../Qc/styles/SliderStyles';
import {UseSliderProps, UseSliderRef} from '../Qc/types/SliderTypes';
import Animated from 'react-native-reanimated';
import {useSlider} from './hooks/useSlider';
import JMFastImage from './JMFastImage';

const QCSlidingTab = forwardRef(
  (props: UseSliderProps, ref?: React.Ref<UseSliderRef>) => {
    const {
      selectedOption,
      animatedStyle,
      tabSwitchAnimatedStyle,
      handleOptionChange,
      onScroll,
      tabWidth,
      tabHeight,
      quickCommerceConfig,
      setFixedTabWidth
    } = useSlider(props, ref);

    useImperativeHandle(ref, () => ({onScroll}));

    return quickCommerceConfig?.featureEnabled ? (
      <View style={{backgroundColor: '#0078AD'}}>
        <Animated.View
          onLayout={e => (tabHeight.value = e.nativeEvent.layout.height)}
          style={[styles.tabContainer, animatedStyle]}>
          <Animated.View
            style={[styles.slideIndicator, tabSwitchAnimatedStyle]}
          />
          <TouchableOpacity
            onLayout={e => {
              setFixedTabWidth(e.nativeEvent.layout.width);
              tabWidth.value = e.nativeEvent.layout.width
            }}
            activeOpacity={1}
            style={styles.tabOption}
            onPress={() =>
              selectedOption != 'Quick' ? handleOptionChange('Quick') : null
            }>
            {selectedOption === 'Quick' ? (
              <JMFastImage
                imageUrl={quickCommerceConfig?.quickActiveImageUrl}
              />
            ) : (
              <JMFastImage
                imageUrl={quickCommerceConfig?.quickInactiveImageUrl}
              />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.tabOption}
            onPress={() =>
              selectedOption != 'Scheduled'
                ? handleOptionChange('Scheduled')
                : null
            }
            activeOpacity={1}>
            {selectedOption === 'Scheduled' ? (
              <JMFastImage
                imageUrl={quickCommerceConfig?.scheduledActiveImageUrl}
              />
            ) : (
              <JMFastImage
                imageUrl={quickCommerceConfig?.scheduledInactiveImageUrl}
              />
            )}
          </TouchableOpacity>
        </Animated.View>
      </View>
    ) : null;
  },
);

export default QCSlidingTab;
