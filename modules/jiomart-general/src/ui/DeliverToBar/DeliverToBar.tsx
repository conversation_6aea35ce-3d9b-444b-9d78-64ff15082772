import {Image, TouchableOpacity, View} from 'react-native';
import React, {forwardRef} from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import Animated from 'react-native-reanimated';
import type {
  DeliverToBarProps,
  DeliverToBarRef,
} from './types/DeliverToBarType';
import useDeliverToBar from './hooks/useDeliverToBar';
import {styles} from './styles/DeliverToBarStyle';
import {useGlobalState} from '../../context/JMGlobalStateProvider';
import {rw, rh} from '../../../../jiomart-common/src/JMResponsive';

const DeliverToBar = forwardRef<DeliverToBarRef, DeliverToBarProps>(
  (props: DeliverToBarProps, ref: React.Ref<DeliverToBarRef>) => {
    const {text, onPress, deliverToBarStyle, primary20, qcDetails, quickCommerceConfig, deliverToBarData} = useDeliverToBar(
      props,
      ref,
    );
    return (
      <Animated.View style={[styles.negZindex, deliverToBarStyle]}>
        {deliverToBarData?.qcMessage ? (
          <TouchableOpacity
            style={[
              styles.container,
              {
                backgroundColor: primary20,
              },
            ]}
            activeOpacity={0.65}
            onPress={onPress}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <JioIcon
                ic="IcLocation"
                color={IconColor.GREY80}
                size={IconSize.MEDIUM}
                style={{marginRight: 4}}
              />
              <View>
                <View style={styles.wrapper}>
                  <JioText
                    text={text ?? ''}
                    color={'primary_grey_100'}
                    appearance={JioTypography.BODY_XXS}
                  />
                </View>
                <View style={{flexDirection: 'row'}}>
                  {qcDetails.journey === quickCommerceConfig?.quickDeliveryKey && (
                    <Image
                      style={{width: rw(50), height: rh(20), marginRight: 2}}
                      source={{
                        uri: quickCommerceConfig?.quickActiveImageUrl,
                      }}
                    />
                  )} 
                  <JioText
                    text={deliverToBarData?.qcMessage ?? ''}
                    color={'secondary_50'}
                    appearance={JioTypography.BODY_XS}
                  />
                </View>
              </View>
            </View>

            <JioIcon
              ic="IcChevronDown"
              color={IconColor.PRIMARY60}
            />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[
              styles.container,
              {
                backgroundColor: primary20,
              },
            ]}
            activeOpacity={0.65}
            onPress={onPress}>
            <View style={styles.wrapper}>
              <JioIcon
                ic="IcLocation"
                color={IconColor.GREY80}
                size={IconSize.SMALL}
              />
              <JioText
                text={text ?? ''}
                color={'primary_grey_100'}
                appearance={JioTypography.BODY_S}
              />
            </View>
            <JioIcon ic="IcChevronDown" color={IconColor.PRIMARY60} />
          </TouchableOpacity>
        )}
      </Animated.View>
    );
  },
);

export default React.memo(DeliverToBar);
