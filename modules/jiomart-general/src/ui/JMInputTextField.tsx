import {StyleSheet, TextInput, View, type TextInputProps} from 'react-native';
import React, {useEffect, useState} from 'react';
import {JioIcon, JioText, useColor} from '@jio/rn_components';
import Feedback, {
  FeedbackState,
  type FeedbackProps,
} from '@jio/rn_components/src/utils/Feedback';
import {
  IconColor,
  JioTypography,
  type JioColor,
  type JioIconProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {useTypography} from '@jio/rn_components/src/typography';

interface JMInputTextFieldProps {
  onRef?: (ref: TextInput | null) => void;
  label: JioTextProps;
  isReadOnly?: boolean;
  textInput: TextInputProps;
  feedback?: FeedbackProps;
  prefix?: {
    title?: JioTextProps;
    icon?: JioIconProps;
  };
  suffix?: {
    title?: JioTextProps;
    icon?: JioIconProps;
  };
}

const JMInputTextField = (props: JMInputTextFieldProps) => {
  const {onRef, textInput, label, feedback, prefix, suffix, isReadOnly} = props;
  const {value} = textInput;
  const textStyle = useTypography(JioTypography.BODY_S);
  const [focus, setFocus] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [prefixLength, setPrefixLength] = useState(0);
  const [suffixLength, setSuffixLength] = useState(0);

  const labelPosition = inputValue || focus;

  let conditionalBorderColor = 'primary_grey_80';

  if (focus) {
    conditionalBorderColor = 'primary_60';
  }

  if (feedback?.stateText) {
    switch (feedback?.state) {
      case FeedbackState.ERROR:
        conditionalBorderColor = 'feedback_error_50';
        break;
      case FeedbackState.SUCCESS:
        conditionalBorderColor = 'feedback_success_50';
        break;
      case FeedbackState.WARNING:
        conditionalBorderColor = 'feedback_warning_50';
        break;
    }
  }

  const borderColor = useColor(conditionalBorderColor as JioColor);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <View style={styles.container} pointerEvents={isReadOnly ? 'none' : 'auto'}>
      <TextInput
        ref={ref => {
          onRef?.(ref);
        }}
        style={[
          isReadOnly ? {opacity: 0.5} : null,
          {
            ...styles.textInput,
            borderBottomColor: borderColor,
            ...textStyle,
            paddingLeft: prefixLength,
            paddingRight: suffixLength,
          },
        ]}
        {...textInput}
        placeholder=""
        onFocus={e => {
          setFocus(true);
          textInput?.onFocus?.(e);
        }}
        onBlur={e => {
          setFocus(false);
          textInput?.onBlur?.(e);
        }}
        onChangeText={val => {
          setInputValue(val);
          textInput?.onChangeText?.(val);
        }}
        hitSlop={{
          top: 10,
          left: 10,
          right: 10,
          bottom: 10,
        }}
      />
      <View
        pointerEvents="none"
        style={styles.prefix}
        onLayout={e => {
          if (Math.floor(e.nativeEvent.layout.width)) {
            setPrefixLength(Math.floor(e.nativeEvent.layout.width + 8));
          }
        }}>
        <JioText
          color={'primary_grey_100'}
          {...prefix?.title}
          appearance={JioTypography.BODY_S}
        />
        {prefix?.icon?.ic ? (
          <JioIcon color={IconColor.GREY80} {...prefix?.icon} />
        ) : null}
      </View>
      <View
        pointerEvents="none"
        style={styles.suffix}
        onLayout={e => {
          if (Math.floor(e.nativeEvent.layout.width)) {
            setSuffixLength(Math.floor(e.nativeEvent.layout.width + 8));
          }
        }}>
        <JioText
          color={'primary_grey_100'}
          {...suffix?.title}
          appearance={JioTypography.BODY_S}
        />
        {suffix?.icon?.ic ? (
          <JioIcon color={IconColor.GREY80} {...suffix?.icon} />
        ) : null}
      </View>
      <View
        style={[
          labelPosition
            ? styles.labelShift
            : {...styles.labelUnshift, left: prefixLength},
        ]}
        pointerEvents="none">
        <JioText
          color={'primary_grey_80'}
          {...label}
          appearance={
            labelPosition ? JioTypography.BODY_XS : JioTypography.BODY_S
          }
        />
      </View>
      {feedback?.stateText ? (
        <View style={{marginTop: 8}}>
          <Feedback {...feedback} />
        </View>
      ) : null}
    </View>
  );
};

export default JMInputTextField;

const styles = StyleSheet.create({
  container: {
    marginTop: 12,
    flex: 1,
  },
  textInput: {
    borderBottomWidth: 2,
    height: 40,
    paddingBottom: 10,
  },
  labelShift: {
    position: 'absolute',
    top: -12,
  },
  labelUnshift: {
    position: 'absolute',
    top: 4,
  },
  prefix: {
    columnGap: 4,
    flexDirection: 'row',
    position: 'absolute',
    top: 4,
  },
  suffix: {
    columnGap: 4,
    flexDirection: 'row',
    position: 'absolute',
    top: 4,
    right: 0,
  },
});
