import React, {useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import StarIcon from './StarIcon';

export interface SelectStarRatingProps {
  starRating?: any;
  onStarPress?: (val: string) => void;
  isStatic?: boolean;
  size?: number;
  style?: StyleProp<ViewStyle>;
}

const SelectStarRating = (props: SelectStarRatingProps) => {
  const {starRating, onStarPress, size = 40, style, isStatic} = props;
  const [rating, setRating] = useState(starRating);
  const starNumber = [1, 2, 3, 4, 5];

  useEffect(() => {
    if (starRating) {
      setRating(starRating);
    }
  }, [starRating]);

  const handleStarClick = (starNumber: any) => {
    onStarPress?.(starNumber);
    if (!isStatic) {
      setRating(starNumber);
    }
  };

  const getOffset = (starNumber: any) => {
    return starNumber <= rating ? '100%' : '0%';
  };

  return (
    <View style={[styles.container, style]}>
      {starNumber.map(star => (
        <TouchableOpacity
          key={star}
          style={styles.starContainer}
          onLongPress={() => {}}
          onPress={() => handleStarClick(star)}
          activeOpacity={1}>
          <StarIcon size={size} offset={getOffset(star)} />
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    columnGap: 12,
  },
  starContainer: {
    // padding: 4,
  },
});

export default React.memo(SelectStarRating);
