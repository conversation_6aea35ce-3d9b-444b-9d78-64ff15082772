import React, {useState, ReactNode} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  type ViewStyle,
  type StyleProp,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  Extrapolate,
  runOnJS,
} from 'react-native-reanimated';
import {GestureDetector, Gesture} from 'react-native-gesture-handler';
import {JioText} from '@jio/rn_components';
import {
  JioTypography,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
const {width} = Dimensions.get('window');
interface TabProps {
  tabs: string[];
  tabStyle?: {
    container?: StyleProp<ViewStyle>;
    contentContainerStyle?: StyleProp<ViewStyle>;
    style?: StyleProp<ViewStyle>;
    tabItemStyle?: StyleProp<ViewStyle>;
    tabItemTouchStyle?: StyleProp<ViewStyle>;
    tabItemIndicatorStyle?: StyleProp<ViewStyle>;
    tabPanStyle?: StyleProp<ViewStyle>;
  };
  onChange?: (tab: string) => void;
  tabItem?: JioTextProps;
  disableGesture?: boolean;
  children: ReactNode;
}
const Tab = (props: TabProps) => {
  const {tabs, tabItem, tabStyle, children, disableGesture, onChange} = props;
  const ChildrenArray = React.Children.toArray(children);
  const [index, setIndex] = useState(0);
  const translateX = useSharedValue(0);
  // Enable gesture only if more than 1 tab exists
  const gesture = Gesture.Pan()
    .onUpdate(event => {
      translateX.value = -index * width + event.translationX;
    })
    .onEnd(event => {
      let nextIndex = index;
      if (event.translationX < -width * 0.3 && index < tabs.length - 1) {
        nextIndex = index + 1;
      } else if (event.translationX > width * 0.3 && index > 0) {
        nextIndex = index - 1;
      }
      translateX.value = withSpring(-nextIndex * width, {damping: 15});
      runOnJS(setIndex)(nextIndex);
      if (onChange) runOnJS(onChange)(tabs[nextIndex]);
    })
    .enabled(tabs?.length > 1 && !disableGesture);
  const handleTabPress = (i: number) => {
    setIndex(i);
    onChange?.(tabs[i]);
    translateX.value = withTiming(-i * width, {duration: 300});
  };
  return (
    <View style={[styles.container, tabStyle?.container]}>
      {/* Scrollable Tab Header */}
      <View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={[tabStyle?.contentContainerStyle]}
          style={[
            {
              backgroundColor: '#E0E0E0',
            },
            tabStyle?.style,
          ]}>
          {tabs?.map((tab, i) => (
            <View key={`tab-${i}`} style={[tabStyle?.tabItemStyle]}>
              <TouchableOpacity
                onPress={() => handleTabPress(i)}
                style={[styles.tab, tabStyle?.tabItemTouchStyle]}>
                <JioText
                  appearance={JioTypography.BODY_XS}
                  textAlign="center"
                  color={index === i ? 'primary_grey_100' : 'primary_grey_80'}
                  {...tabItem}
                  text={tab}
                />
              </TouchableOpacity>
              {index === i && (
                <View
                  style={[styles.indicator, tabStyle?.tabItemIndicatorStyle]}
                />
              )}
            </View>
          ))}
        </ScrollView>
      </View>
      {/* Tab Content */}

      <GestureDetector gesture={gesture}>
        <Animated.View style={styles.contentContainer}>
          {tabs?.map((tab, i) => {
            const Children: ReactNode = ChildrenArray[i];
            return (
              <TabPan
                key={`tab-pan-${i}`}
                translateX={translateX}
                selectedIndex={i}
                style={tabStyle?.tabPanStyle}>
                {React.isValidElement(Children) && React.cloneElement(Children)}
              </TabPan>
            );
          })}
        </Animated.View>
      </GestureDetector>
    </View>
  );
};
const TabPan = (props: any) => {
  const {translateX, selectedIndex: i, children, style} = props;
  const animatedStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      translateX.value,
      [-(i + 1) * width, -i * width, -(i - 1) * width],
      [0.9, 1, 0.9],
      Extrapolate.CLAMP,
    );
    return {
      transform: [{translateX: translateX.value}, {scale}],
    };
  });
  return (
    <Animated.View
      style={[
        {
          width: '100%',
        },
        style,
        animatedStyle,
      ]}>
      {children}
    </Animated.View>
  );
};
// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 14,
  },
  indicator: {
    height: 4,
    backgroundColor: '#0078AD',
    marginTop: 'auto',
    borderRadius: 100,
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
  },
});
export default Tab;
