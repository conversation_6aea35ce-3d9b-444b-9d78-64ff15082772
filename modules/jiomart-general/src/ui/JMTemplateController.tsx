import MultiSearch from './SearchScreen/components/MultiSearch';
import RecentSearch from './SearchScreen/components/RecentSearch';
import DiscoverMore from './SearchScreen/components/DiscoverMore';
import TopCategories from './SearchScreen/components/TopCategories';
import RecommendedProducts from './SearchScreen/components/RecommendedProducts';
import SearchHorizontalList from './SearchScreen/components/SearchHorizontalList';
import ProductGrid from './SearchScreen/components/ProductGrid';
import { NavigationBean } from '../../../jiomart-common/src/JMNavGraphUtil';
import { JMTemplateViewType } from '../../../jiomart-common/src/JMTemplateViewType';

export function renderTemplate(
  props: any,
  index: number,
  onClick?: (navBean: NavigationBean) => void,
): React.JSX.Element {
  switch (props.viewType) {
    case JMTemplateViewType.SHOPPING_LIST_BLOCK:
      return (
        <MultiSearch key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.RECENT_SEARCHES:
      return (
        <RecentSearch key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.DISCOVER_MORE:
      return (
        <DiscoverMore key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.POPULAR_CATEGORY:
      return (
        <TopCategories key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.RECOMMENDED_PRODUCT:
      return <RecommendedProducts key={index} template={props} />;
    case JMTemplateViewType.SEARCH_HORIZONTAL:
      return (
        <SearchHorizontalList
          key={index}
          template={props}
          onClick={props?.onClick}
        />
      );
    case JMTemplateViewType.PRODUCT_GRID:
      return <ProductGrid key={index} template={props} />;
    default: {
      return <></>;
    }
  }
}
