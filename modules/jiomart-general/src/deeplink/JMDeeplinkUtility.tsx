import { AppScreens } from '../../../jiomart-common/src/JMAppScreenEntry';
import {
  navGraph,
  NavigationBean,
  navBeanObj,
  ActionType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {
  DeeplinkData,
  HeaderType,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {
  getConfigDataFromAssetFile,
  getConfigFileData,
} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {jsonParse} from '../../../jiomart-common/src/uiModals/JMResponseParser';
import {
  convertHashMapToQueryParam,
  convertQueryParamToHashMap,
  getVersionSpecificFilterListData,
} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import {Decoder} from '../../../jiomart-networkmanager/src/api/utils/Decoder';
import _ from 'lodash';

function getKeyByValue(value: string): string {
  for (const key in AppScreens) {
    if (AppScreens[key as keyof typeof AppScreens] === value) {
      return key;
    }
  }
  return '';
}

export function getDestinationFromDeeplinkUrl(
  deeplinkUrl: string | null,
  defaultRoute: string,
  childNavGraph: boolean = true,
): string {
  let deeplink = JMSharedViewModel.Instance.deeplinkUrl;
  console.debug('getDestinationFromDeeplinkUrl' + deeplink);
  if (deeplink && deeplink.length > 0) {
    try {
      const url = deeplink;
      const parts = url?.split('://');
      const pathname =
        parts?.length > 1 ? parts?.[1]?.substring?.(parts?.[1]?.indexOf?.('/') + 1) : '';
      const destination = childNavGraph ? pathname : navGraph.get(pathname);
      console.debug('getDestinationFromDeeplinkUrl - ' + destination);
      return destination && !isNullOrUndefinedOrEmpty(destination)
        ? destination
        : defaultRoute;
    } catch (e) {
      console.error(e);
      return defaultRoute;
    }
  }
  return defaultRoute;
}

export function getInitialNavBean(
  navBean: NavigationBean | null,
  defaultBean: NavigationBean | null = null,
): NavigationBean {
  if (navBean && navBean !== null) {
    return navBean;
  } else if (defaultBean && defaultBean !== null) {
    return defaultBean;
  } else {
    return navBeanObj({
      actionType: ActionType.OPEN_DEEPLINK,
      destination: AppScreens.DASHBOARD,
      actionUrl: '',
      userAuthenticationRequired: 2,
      navTitle: '',
      headerVisibility: HeaderType.HIDDEN,
    });
  }
}
const splitAtFirstEquals = (str: string) => {
  const index = str.indexOf('=');

  if (index === -1) {
    return [null, null];
  }

  const part1 = str.slice(0, index);
  const part2 = str.slice(index + 1); // everything after the first `=`

  return [part1, part2];
};

const parseURI = (uri: string) => {
  // Extract the scheme (before "://")
  const [schemeData, queryString] = uri.split('?');
  console.log('schemeWithRest:', queryString);
  console.log('schemeData:', schemeData);

  const [scheme, hostAndPathname] = schemeData?.split('://');
  console.log('hostAndPathname:', hostAndPathname);
  // Split the host and pathname (separated by "/")
  const [host, ...pathnameParts] = hostAndPathname?.split('/');
  var pathname = pathnameParts.join('/'); // Join the pathname parts (in case there are multiple slashes)

  if(pathname?.startsWith?.("deeplink=")){
    pathname = pathname.replace("deeplink=", "");
  }
  if(pathname?.startsWith?.("deeplink/")){
    pathname = pathname.replace("deeplink/", "");
  }

  // Parse the query parameters into an object
  const queryParams: {[key: string]: string} = {};
  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = splitAtFirstEquals(param);
      if (key && value) {
        queryParams[key] = decodeURIComponent(value); // Decode the query parameter value
      }
    });
  }

  // Output the results
  console.log('Scheme:', scheme);
  console.log('Host:', host);
  console.log('Pathname:', pathname);
  console.log('Query Parameters:', queryParams);

  return {
    path: pathname,
    queryParams: queryParams,
  };
};

export async function handleDeeplinkIntent(
  deeplinkData: DeeplinkData,
): Promise<NavigationBean | null> {
  try {
    if (deeplinkData) {
      if (deeplinkData.mUri && !isNullOrUndefinedOrEmpty(deeplinkData.mUri)) {
        return getNavBeanFromDeeplinkURL(deeplinkData.mUri);
      } 
    }
  } catch (error) {
    return null;
  }
  return null;
}


export async function getNavBeanFromDeeplinkURL(
  deeplinkUrl: string,
): Promise<NavigationBean | null> {
  JMLogger.log(
    'getNavBeanFromDeeplink ',
    'getNavBeanFromDeeplink deeplinkUrl ' + deeplinkUrl,
  );
  const {path, queryParams} = parseURI(deeplinkUrl);
  // const deeplinkIdentifier = path.replace('/', '');
  const deeplinkIdentifier = path;
  JMLogger.log(
    'getNavBeanFromDeeplink ',
    'getNavBeanFromDeeplink deeplinkIdentifier ' +
      deeplinkIdentifier +
      ' queryParams ' +
      JSON.stringify(queryParams),
  );
  if (deeplinkIdentifier) {
    const bean = await getBeanFromDeeplinkFile(deeplinkIdentifier);
    JMLogger.log(
      'getNavBeanFromDeeplinkURL ',
      'getNavBeanFromDeeplinkURL bean ' + bean,
    );
    if (bean) {
      const queryParamsObj: {[key: string]: string} = {};
      const beanQueryParam = !isNullOrUndefinedOrEmpty(bean.bundle)
        ? convertQueryParamToHashMap(bean.bundle)
        : null;
      if (beanQueryParam) {
        Object.entries(beanQueryParam).forEach(([key, value]) => {
          console.log('beanQueryParam' + `${key}: ${value}`);
          queryParamsObj[key] = value;
        });
      }
      if (queryParams) {
        Object.entries(queryParams).forEach(([key, value]) => {
          console.log('queryParams' + `${key}: ${value}`);
          queryParamsObj[key] = value;
        });
      }
      bean.bundle = convertHashMapToQueryParam(queryParamsObj) ?? undefined;
      return bean;
    }
    else if(deeplinkUrl.startsWith('https://')){
       return {
          actionType : ActionType.OPEN_WEB_URL,
          actionUrl : deeplinkUrl,
          destination : AppScreens.COMMON_WEB_VIEW,
          headerVisibility : HeaderType.CUSTOM,
          headerType : 9
       }
    }
  }
  return null;
}

export async function updatedNavBeanData(
  deeplinkUrl: string | null,
  navBean: NavigationBean,
): Promise<NavigationBean | null> {
  if (deeplinkUrl) {
    JMSharedViewModel.Instance.setDeeplinkUrlData('');
    return getNavBeanFromDeeplinkURL(deeplinkUrl);
  } else if (
    navBean?.actionType === ActionType.OPEN_DEEPLINK &&
    navBean.actionUrl
  ) {
    return getNavBeanFromDeeplinkURL(navBean.actionUrl);
  }
  return null;
}

const getDeeplinkConfig = async (): Promise<DeeplinkConfigFile | null> => {
  try {
    const deeplinkConfigData = await new Promise<DeeplinkConfigFile>(
      (resolve, reject) => {
        getConfigFileData(
          JMConfigFileName.JMDeeplinkConfigurationFileName,
          (deeplinkConfig: any) => {
            const jsonString = JSON.stringify(deeplinkConfig);
            const parseData = jsonParse<DeeplinkConfigFile>(jsonString);
            JMLogger.log('getDeeplinkConfig ' + JSON.stringify(deeplinkConfig));
            parseData ? resolve(parseData) : null; // resolve the promise with the newConfig
          },
        );
      },
    );
    return deeplinkConfigData;
  } catch (error) {
    JMLogger.log('Error fetching deeplink config: ' + error);
    return null;
  }
};

export type DeeplinkConfigFile = {
  deeplinks: NavigationBean[];
};


export async function getBeanFromDeeplinkFile(
  deeplinkIdentifier: string,
): Promise<NavigationBean | null> {
  const deeplinkConfigFile = await getDeeplinkConfig();
  JMLogger.log(
    'getBeanFromDeeplinkFile deeplinks size' +
      deeplinkConfigFile?.deeplinks.length,
  );
  if (deeplinkConfigFile && deeplinkConfigFile.deeplinks) {
    JMLogger.log(
      'getBeanFromDeeplinkFile ',
      'getBeanFromDeeplinkFile deeplinkIdentifier' + deeplinkIdentifier,
    );
    const filterData = getVersionSpecificFilterListData(
      deeplinkConfigFile.deeplinks,
    );
    const foundBean = filterData?.find(
      bean =>
      bean.deeplinkIdentifier &&
      deeplinkIdentifier?.startsWith(bean.deeplinkIdentifier)
    )
    return (
      foundBean ? {...foundBean, deeplinkParam: deeplinkIdentifier?.replace(foundBean.deeplinkIdentifier ?? "", "")} : null
    );
  }
  return null;
}
