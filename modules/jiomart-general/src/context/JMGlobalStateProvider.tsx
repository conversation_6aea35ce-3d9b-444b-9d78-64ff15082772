import {AuthState} from '../../../jiomart-common/src/JMAuthType';
import {
  DeeplinkData,
  ToastTypeData,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import React, {ReactNode, createContext, useContext, useState} from 'react';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';

// Define the shape of the global state object
interface GlobalState {
  userAuthenticationStatus: AuthState;
  setUserAuthenticationStatus: React.Dispatch<React.SetStateAction<AuthState>>;
  toastTypeData: ToastTypeData | undefined;
  setToastTypeData: React.Dispatch<
    React.SetStateAction<ToastTypeData | undefined>
  >;
  event: {[key: string]: any};
  setEvent: React.Dispatch<
    React.SetStateAction<{
      [key: string]: any;
    }>
  >;
  address: JMAddressModel | null;
  setAddress: React.Dispatch<React.SetStateAction<JMAddressModel | null>>;
  setQCDetails: React.Dispatch<React.SetStateAction<any>>;
  qcDetails: any;
  userInitials: string;
  setUserInitials: React.Dispatch<React.SetStateAction<string>>;
  permissionBtmSheet: boolean;
  setPermissionBtmSheet: React.Dispatch<React.SetStateAction<boolean>>;
  privacyPolicyBtmSheet: boolean;
  setPrivacyPolicyBtmSheet: React.Dispatch<React.SetStateAction<boolean>>;
  softUpdateBtmSheet: boolean;
  setSoftUpdateBtmSheet: React.Dispatch<React.SetStateAction<boolean>>;
  deeplinkData: DeeplinkData | undefined;
  setDeeplinkData: React.Dispatch<
    React.SetStateAction<DeeplinkData | undefined>
  >;
  refreshScreen: boolean;
  setRefreshScreen: React.Dispatch<React.SetStateAction<boolean>>;
  notificationCount: number;
  setNotificationCount: React.Dispatch<React.SetStateAction<number>>;
}

// Create a context with a defaultValue
const GlobalStateContext = createContext<GlobalState | null>(null);

// Create a provider component
export const GlobalStateProvider = ({children}: {children: ReactNode}) => {
  const [event, setEvent] = useState<{[key: string]: any}>(new Map());
  const [userInitials, setUserInitials] = useState('');
  const [userAuthenticationStatus, setUserAuthenticationStatus] =
    useState<AuthState>(JMSharedViewModel.Instance.userAuthenticationStatus);
  const [toastTypeData, setToastTypeData] = useState<ToastTypeData | undefined>(
    undefined,
  );
  const [address, setAddress] = useState<JMAddressModel | null>(null);
  const [qcDetails, setQCDetails] = useState({journey: ''});
  const [notificationCount, setNotificationCount] = useState<number>(0);

  // startup bottomsheet
  const [permissionBtmSheet, setPermissionBtmSheet] = useState(false);
  const [privacyPolicyBtmSheet, setPrivacyPolicyBtmSheet] = useState(false);
  const [softUpdateBtmSheet, setSoftUpdateBtmSheet] = useState(false);

  const [deeplinkData, setDeeplinkData] = useState<DeeplinkData | undefined>(
    undefined,
  );

  const [refreshScreen, setRefreshScreen] = useState(false);

  // Create an object representing the global state
  const globalState: GlobalState = {
    userAuthenticationStatus,
    setUserAuthenticationStatus,
    toastTypeData,
    setToastTypeData,
    event,
    setEvent,
    userInitials,
    setUserInitials,
    address,
    setAddress,
    permissionBtmSheet,
    setPermissionBtmSheet,
    privacyPolicyBtmSheet,
    setPrivacyPolicyBtmSheet,
    softUpdateBtmSheet,
    setSoftUpdateBtmSheet,
    setQCDetails,
    qcDetails,
    deeplinkData,
    setDeeplinkData,
    refreshScreen,
    setRefreshScreen,
    notificationCount,
    setNotificationCount,
  };

  return (
    <GlobalStateContext.Provider value={globalState}>
      {children}
    </GlobalStateContext.Provider>
  );
};

// Custom hook to consume the context
export const useGlobalState = (): GlobalState => {
  const context = useContext(GlobalStateContext);
  if (context === null) {
    throw new Error('useGlobalState must be used within a GlobalStateProvider');
  }
  return context;
};
