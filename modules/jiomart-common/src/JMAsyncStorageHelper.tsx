import AsyncStorage from '@react-native-async-storage/async-storage';
import {<PERSON><PERSON><PERSON><PERSON>} from './utils/JMLogger';
import {base64DecodeValue, base64EncodeValue} from './Helper';

export const TRANSACTION_REF_NUMBER = 'transactionRefNum';
export const JH_TOKEN = 'JHToken';

export enum AppPrefernceKeys {
  PREFERENCE_KEY_IS_BIOMETRIC_ENABLED = 'PREFERENCE_KEY_IS_BIOMETRIC_ENABLED',
  ENTER_PIN_COMPLETED = 'PREFERENCE_KEY_IS_ENTER_PIN_DONE',
  HOME_DASHBOARD_DATA = 'PREFERENCE_KEY_HOME_DASHBAORD_DATA',
  DISCOVERY_DASHBOARD_DATA = 'PREFERENCE_KEY_DISCOVERY_DASHBAORD_DATA',
  DISCOVERY_DASHBOARD_HASHVALUE = 'PREFERENCE_KEY_DISCOVERY_DASHBAORD_HASHVALUE',
  HOME_DASHBOARD_HASHVALUE = 'PREFERENCE_KEY_HOME_DASHBAORD_HASHVALUE',
  HEALTH_LOCKER_CACHED_DATA = 'HEALTH_LOCKER_CACHED_DATA',
}

export const addStringPref = async (
  key: string,
  value: string,
  encryptData: boolean = false,
) => {
  try {
    const finalvalue =
      encryptData && value ? base64EncodeValue(value) ?? value : value;
    await AsyncStorage.setItem(key, finalvalue);
  } catch (e) {
    // saving error
    console.log('This is error -- ', e);
    JMLogger.log(e);
  }
};
export const removeStringPref = async (key: string) => {
  try {
    const value = await AsyncStorage.removeItem(key);
  } catch (e) {
    JMLogger.log(e);
  }
};

export const getPrefString = async (
  key: string,
  decryptData: boolean = false,
) => {
  try {
    const value = await AsyncStorage.getItem(key);
    const finalvalue =
      decryptData && value ? base64DecodeValue(value) ?? value : value;
    return finalvalue;
  } catch (e) {
    JMLogger.log(e);
    return null; // Explicitly return null on error
  }
};

export const addNumberPref = async (key: string, value: number) => {
  try {
    await AsyncStorage.setItem(key, value.toString());
    // console.log("hashmap saved")
  } catch (e) {
    // saving error
    JMLogger.log(e);
  }
};
export const removePrefAllKeys = async () => {
  try {
    const value = await AsyncStorage.clear();
    JMLogger.log('removePrefAllKeys');
  } catch (e) {
    JMLogger.log(e);
  }
};
export const removeNumberPref = async (key: string) => {
  try {
    const value = await AsyncStorage.removeItem(key);
  } catch (e) {
    JMLogger.log(e);
  }
};

export const getPrefNumber = async (key: string) => {
  try {
    const value = await AsyncStorage.getItem(key);
    return value ? parseInt(value) : -1;
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const setRecordsToUpload = async (key: string, data: string) => {
  try {
    await AsyncStorage.setItem(key, data);
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const getRecordsToUpload = async (key: string) => {
  try {
    const data = await AsyncStorage.getItem(key);
    return data;
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const setDirectoryData = async (key: string, data: string) => {
  try {
    await AsyncStorage.setItem(key, data);
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const getDirectoryData = async (key: string) => {
  try {
    const data = await AsyncStorage.getItem(key);
    return data;
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};
