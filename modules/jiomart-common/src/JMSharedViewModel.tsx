import {NavigationContainerRef} from '@react-navigation/native';
import {useRef} from 'react';
import {Platform} from 'react-native';
import {EventTriggerChannel} from './AnalyticsParams';
import {AuthState} from './JMAuthType';
import {NavigationBean} from './JMNavGraphUtil';
import {DeeplinkData} from './JMScreenSlot.types';
import {AppSourceType} from './SourceType';
import { PlatformType } from './JMObjectUtility';

export class JMSharedViewModel {
  private static _instance: JMSharedViewModel;

  private constructor() { }

  public static get Instance() {
    return this._instance || (this._instance = new this());
  }
  public appSource = AppSourceType.JM_BAU;
  public userAuthenticationStatus = AuthState.AUTHENTICATED;
  public eventTriggerChannel = EventTriggerChannel.FIREBASE;
  public globalThemeToken = {
    primary: 'sky',
    secondary: 'red',
    sparkle: 'green',
    mode: 'light',
  };
  public deeplinkUrl = '';
  public externalDeeplinkData: DeeplinkData | undefined = undefined;
  public navigationRef = useRef<NavigationContainerRef<any>>(null);
  public loggedInStatus = false;
  private userProfileExist = false;
  private pinCreated = false;
  private navigationBeanData: NavigationBean | undefined = undefined;
  public profileSwitchedInLastSession = false;
  public isPrimaryAccount = true;
  private userProfileApiCalled: Map<string, boolean> = new Map();
  public linkedByMeApiCalled: boolean = false;
  public appVersion =
    this.appSource === AppSourceType.JM_BAU ? Platform.OS === PlatformType.IOS ? '2.0.46' : '2.0.58' : '312';
  public corporateId = '';
  public appLevelJwtToken = '';
  public accountSwitched: boolean = false;
  public tempMobileNumber = '';
  public pushToken: string = '';
  public webIosClientId = '';
  public iosClientId = '';
  public statusApiInProgress = false;
  public networkAvailable = true;
  public logoutCalled = false;
  public previousPageURL = '';
  public addressListApiCalled = false;
  public flowInitialised = false;
  public reactInAppScreen = true;
  public isQcJourneySelected = false;

  public resetViewModelData() {
    this.setUserProfileExist(false);
    this.setPinCreated(false);
    this.setPinCreated(false);
    this.accountSwitched = false;
    this.linkedByMeApiCalled = false;
    this.isPrimaryAccount = true;
    this.profileSwitchedInLastSession = false;
    this.pushToken = '';
    this.corporateId = '';
    this.loggedInStatus = false;
  }

  public getAddressListApiCalled(): boolean {
    return this.addressListApiCalled;
  }

  public setAddressListApiCalled(addressListApiCalled: boolean) {
    this.addressListApiCalled = addressListApiCalled;
  }

  public getUserProfileApiCalled(jioId: string): boolean {
    return this.userProfileApiCalled.get(jioId) ?? false;
  }

  public setUserProfileApiCalled(jioId: string, userProfileApiCalled: boolean) {
    this.userProfileApiCalled.set(jioId, userProfileApiCalled);
  }

  public getNavigationData(): NavigationBean | undefined {
    return this.navigationBeanData;
  }

  public getLoggedInStatus(): boolean {
    return this.loggedInStatus ?? false;
  }

  public getUserProfileExist(): boolean {
    return this.userProfileExist;
  }

  public getPinCreated(): boolean {
    return this.pinCreated;
  }

  public setLoggedInStatus(status: boolean) {
    this.loggedInStatus = status;
  }

  public setNavigationBeanData(navigationBean: NavigationBean) {
    this.navigationBeanData = navigationBean;
  }

  public setUserProfileExist(userProfileExist: boolean) {
    this.userProfileExist = userProfileExist;
  }

  public setPinCreated(pinCreated: boolean) {
    this.pinCreated = pinCreated;
  }

  public setAppSourceType(sourceType: AppSourceType) {
    this.appSource = sourceType;
  }
  public setEventTriggerChannel(eventTriggerChannel: EventTriggerChannel) {
    this.eventTriggerChannel = eventTriggerChannel;
  }

  public setNetworkAvailableStatus(status: boolean) {
    this.networkAvailable = status;
  }

  public setUserAuthenticationStatus(authStatus: AuthState) {
    this.userAuthenticationStatus = authStatus;
  }
  public setDeeplinkUrlData(deeplinkUrl: string) {
    this.deeplinkUrl = deeplinkUrl;
  }

  public setExternalDeeplink(data: DeeplinkData | undefined) {
    this.externalDeeplinkData = data;
  }

  public setPreviousPageURL(url: string) {
    this.previousPageURL = url;
  }

  public setIsQcJourneySelected = (status: boolean) => {
    this.isQcJourneySelected = status;
  }

  public getAppSourceTypeData(value: string): AppSourceType | undefined {
    return Object.values(AppSourceType).includes(value as AppSourceType)
      ? (value as AppSourceType)
      : undefined;
  }

}
