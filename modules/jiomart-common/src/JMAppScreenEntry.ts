import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import type {NavigationStackData} from './JMNavGraphUtil';

enum JMAddressScreenEntry {
  ADDRESS_SCREEN = 'JMAddressScreen',
  ADDRESS_LIST_SCREEN = 'JMAddressListScreen',
  ADDRESS_SEARCH_SCREEN = 'JMAddressSearchScreen',
  ADDRESS_MAP_SCREEN = 'JMAddressMapScreen',
  ADDRESS_FORM_SCREEN = 'JMAddressFormScreen',
  ADDRESS_FORM_V1_SCREEN = 'JMAddressFormV1Screen',
}

enum JMCategoryScreenEntry {
  ALL_CATEGORIES = 'AllCategories',
}

enum JMSearchScreenEntry {
  SEARCH_SCREEN = 'JMSearchScreen',
}

enum JMProductListingScreenEntry {
  PRODUCT_LISTING_SCREEN_START = 'JMProductListingScreenStart',
  PRODUCT_LISTING_SCREEN = 'JMProductListingScreen',
  PRODUCT_GRID_LISTING_SCREEN = 'JMProductGridListingScreen',
  PRODUCT_SEARCH_LISTING_SCREEN = 'JMProductSearchListingScreen',
}
enum JMOrderScreenEntry {
  ORDER_SCREEN = 'JMOrderScreen',
  ORDER_LIST_SCREEN = 'JMOrderListScreen',
  REFUND_DETAIL_SCREEN = 'JMRefundDetailScreen',
}

enum JMWishlistScreenEntry {
  WISHLIST_SCREEN = 'JMWishlistScreen',
}

enum JMFeedbackScreenEntry {
  FEEDBACK_SCREEN = 'JMFeedbackScreen',
  RATING_AND_REVIEW_FORM_SCREEN = 'JMRatingAndReviewFormScreen',
  RATING_AND_REVIEW_SUCCESS_SCREEN = 'JMRatingAndReviewSuccessScreen',
}

enum JMAccountScreenEntry {
  NOTIFICATION_SCREEN = 'JMNotificationScreen'
}

enum JMScreenEntry {
  HOME_SCREEN = 'HomeScreen',
  SPLASH_SCREEN = 'JMSplashScreen',
  JIOMART_MAIN_UI = 'JioMartMainUI',
  COMMON_WEB_VIEW = 'CommonWebViewScreen',
  ONE_RETAIL_SCREEN = 'OneRetailUI',
  ACCOUNT_SCREEN = 'JMAccount'
}

const AppScreens = {
  ...JMScreenEntry,
  ...JMCategoryScreenEntry,
  ...JMProductListingScreenEntry,
  ...JMAddressScreenEntry,
  ...JMSearchScreenEntry,
  ...JMWishlistScreenEntry,
  ...JMOrderScreenEntry,
  ...JMFeedbackScreenEntry,
  ...JMAccountScreenEntry
};

type AppScreensType = (typeof AppScreens)[keyof typeof AppScreens];

export type ScreenProps<T extends AppScreensType> = NativeStackScreenProps<
  NavigationStackData,
  T
>;
export {AppScreens};
