import {
  Dimensions,
  Keyboard,
  Platform,
  Share,
  ShareContent,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {PlatformType, isNullOrUndefinedOrEmpty} from '../JMObjectUtility';
import DeviceInfo from 'react-native-device-info';
import {JMLogger} from './JMLogger';

export const rnVersion = '1.6';

interface VersionedItem {
  rnVersion?: string;
  androidVersion?: string;
  iosVersion?: string;
}

export const getRnNumberVersion = () => {
  const appVersion = rnVersion;
  const versionNumber = parseFloat(appVersion.replace(/\./g, ''));
  console.log(
    'getRnNumberVersion ' + appVersion + ' versionNumber ' + versionNumber,
  );
  return versionNumber;
};

export const getVersionSpecificFilterListData = <T extends VersionedItem>(
  listData: T[] | undefined | null,
): T[] | undefined | null => {
  const currentRnVersion = getRnNumberVersion();
  const currentAndroidVersion =
    Platform.OS === PlatformType.ANDROID ? getNumberAppVersion() : -1;
  const currentIosVersion =
    Platform.OS === PlatformType.IOS ? getNumberAppVersion() : -1;
  return listData?.filter(item => {
    const rnVersion = item.rnVersion || '';
    const androidVersion = item.androidVersion || '';
    const iosVersion = item.iosVersion || '';
    JMLogger.log(
      'getVersionSpecificFilterData- rnVersion ' +
        rnVersion +
        ' androidVersion ' +
        androidVersion +
        ' iosVersion ' +
        iosVersion,
    );
    return (
      (isNullOrUndefinedOrEmpty(rnVersion.toString()) ||
        splitVersionAndCheck(rnVersion, currentRnVersion)) &&
      (currentAndroidVersion === -1 ||
        isNullOrUndefinedOrEmpty(androidVersion.toString()) ||
        splitVersionAndCheck(androidVersion, currentAndroidVersion)) &&
      (currentIosVersion === -1 ||
        isNullOrUndefinedOrEmpty(iosVersion.toString()) ||
        splitVersionAndCheck(iosVersion, currentIosVersion))
    );
  });
};

export const checkVersionSpecificObjectData = <T extends VersionedItem>(
  item: T,
): boolean => {
  const currentRnVersion = getRnNumberVersion();
  const currentAndroidVersion =
    Platform.OS === PlatformType.ANDROID ? getNumberAppVersion() : -1;
  const currentIosVersion =
    Platform.OS === PlatformType.IOS ? getNumberAppVersion() : -1;
  const rnVersion = item.rnVersion || '';
  const androidVersion = item.androidVersion || '';
  const iosVersion = item.iosVersion || '';
  JMLogger.log(
    'checkVersionSpecificObjectData rnVersion ' +
      rnVersion +
      ' androidVersion ' +
      androidVersion +
      ' iosVersion ' +
      iosVersion,
  );
  return (
    (isNullOrUndefinedOrEmpty(rnVersion.toString()) ||
      splitVersionAndCheck(rnVersion, currentRnVersion)) &&
    (currentAndroidVersion === -1 ||
      isNullOrUndefinedOrEmpty(androidVersion.toString()) ||
      splitVersionAndCheck(androidVersion, currentAndroidVersion)) &&
    (currentIosVersion === -1 ||
      isNullOrUndefinedOrEmpty(iosVersion.toString()) ||
      splitVersionAndCheck(iosVersion, currentIosVersion))
  );
};

export const splitVersionAndCheck = (
  versionData: string,
  currentVersion: number,
): boolean => {
  const versionArray = versionData.split(',');
  for (let i = 0; i < versionArray.length; i++) {
    try {
      const version = versionArray[i];
      JMLogger.log('splitVersionAndCheck version' + version);
      if (version.startsWith('>=')) {
        const splitVersion = version.replace('>=', '');
        if (currentVersion >= parseInt(splitVersion)) return true;
      } else if (version.startsWith('<=')) {
        const splitVersion = version.replace('<=', '');
        if (currentVersion <= parseInt(splitVersion)) return true;
      } else if (version.indexOf('-') >= 0) {
        const versionStrings = versionData.split('-');
        if (versionStrings.length === 2) {
          const firstPart = parseInt(versionStrings[0]);
          const secondPart = parseInt(versionStrings[1]);
          if (firstPart <= currentVersion && currentVersion <= secondPart)
            return true;
        }
      } else if (currentVersion === parseInt(version)) {
        return true;
      }
    } catch (error) {
      JMLogger.log('splitVersionAndCheck ' + error);
    }
  }
  return false;
};

export const getDynamicString = (string: string, values: string[]) => {
  const stringToReplace = '_VALUE_';
  let newString = `${string}`;
  values.forEach((value, index) => {
    newString = newString.replace(`${stringToReplace}${index}`, value);
  });
  return newString;
};

export const getScreenHeight = () => {
  return Dimensions.get('window').height;
};

export const getChipHeight = () => {
  return getScreenHeight() / 800;
};

export const getChipWidth = () => {
  return getScreenWidth() / 360;
};

export const getScreenWidth = () => {
  return Dimensions.get('window').width;
};

export const getStatusBarHeight = () => {
  return useSafeAreaInsets().top;
};

export const getNavigationBarHeight = () => {
  return useSafeAreaInsets().bottom;
};

export const hideKeyboard = () => {
  Keyboard.dismiss(); // Dismiss the keyboard
};

export const handleShareIntent = (shareData: ShareContent) => {
  Share.share(shareData)
    .then(res => console.log('Share result: ', res))
    .catch(err => console.log('Error sharing: ', err));
};

export const removePrefixes = (name: string): string => {
  const prefixes = ['mr.', 'mrs.', 'ms.', 'dr.'];
  const nameLowerCase = name?.toLowerCase();
  for (const prefix of prefixes) {
    if (nameLowerCase.startsWith(prefix)) {
      return name.slice(prefix.length).trim();
    }
  }
  return name;
};

export const getProfileNameInitials = (name: string): string => {
  const processedName = removePrefixes(name);
  const nameParts = processedName.split(' ');
  const firstInitial = nameParts[0] ? nameParts[0][0].toUpperCase() : '';
  const secondInitial = nameParts[1] ? nameParts[1][0].toUpperCase() : '';
  return `${firstInitial}${secondInitial}`;
};

export function convertHashMapToQueryParam(obj?: {[key: string]: string}) {
  if (isNullOrUndefinedOrEmpty(obj)) {
    return null;
  }
  return Object.entries(obj!)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
    )
    .join('&');
}

export function convertQueryParamToHashMap(
  queryString?: string,
): {[key: string]: string} | null {
  try {
    if (isNullOrUndefinedOrEmpty(queryString)) {
      return null;
    }
    return queryString!.split('&').reduce((acc, pair) => {
      const [key, value] = pair.split('=').map(decodeURIComponent);
      acc[key] = value;
      return acc;
    }, {} as {[key: string]: string});
  } catch (error) {
    return null;
  }
}

export const getAppVersion = () => {
  const appVersion = DeviceInfo.getVersion();
  console.log('getAppVersion ' + appVersion);
  return appVersion;
};

export const getNumberAppVersion = () => {
  const appVersion = DeviceInfo.getVersion();
  const versionNumber = parseFloat(appVersion.replace(/\./g, ''));
  console.log(
    'getAppVersion ' + appVersion + ' versionNumber ' + versionNumber,
  );
  return versionNumber;
};

export const isValidFileType = (
  fileName: string | null | undefined,
): boolean => {
  if (fileName === '') return false;
  try {
    const allowedExtensions = ['png', 'jpg', 'jpeg', 'pdf'];
    if (!fileName || !fileName.includes('.')) return false;
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    const result = allowedExtensions.includes(extension);
    console.log('isValidFileType result>>>>>>:', result);
    return result;
  } catch (error) {
    console.error('Error in isValidFileType:', error);
    return false;
  }
};

export const generateFilterReq = (req: {[key: string]: string[]}) => {
  return req
    ? Object.entries(req)
        .map(([key, values]) => `${key}:${values.join('||')}`)
        .join(':::')
    : '';
};

export const formatCategoryName = (value: string) => {
  if (value?.includes('::')) {
    return value?.split('::')?.pop() ?? '';
  }
  return value ?? '';
};

export const getSvgUrlData = (iconPath: string) => {
  if (iconPath.startsWith('http://') || iconPath.startsWith('https://')) {
    return iconPath;
  } else if (iconPath.startsWith('Ic')) {
    return iconPath;
  } else {
    return `https://myjiostatic.cdn.jio.com/JioMart/Common/${iconPath}.svg`;
  }
};

export const isHyperLocal = (seller_id: number, verticalCode: string) => {
  return (seller_id == 2 || seller_id == 1) && verticalCode == 'GROCERIES';
};
export const isScheduleDelivery = (seller_id: number, verticalCode: string) => {
  return seller_id == 1 && ['FASHION']?.includes(verticalCode);
};

export const isDateValid = (inputDate: any) => {
    const currentDate = new Date(); // Get the current date
    const givenDate = new Date(inputDate); // Convert the input to a Date object

    // Compare the given date with the current date
    return givenDate >= currentDate;
  };

  export function formatPrice(value: number | undefined) {
  if (value === 0) {
    return '0.00';
  }
  if (value) {
    let strValue = value.toString();

    let parts = strValue.split('.');
    let intValue = parts[0];
    let decimalValue = parts[1] || '00';

    if (intValue.length <= 3) {
      intValue = intValue;
    } else if (3 < intValue.length && intValue.length <= 5) {
      intValue = intValue.slice(0, -3) + ',' + intValue.slice(-3);
    } else {
      let cut = intValue.slice(0, -3);
      let o = [];
      while (cut) {
        o.push(cut.slice(-2));
        cut = cut.slice(0, -2);
      }
      o = o.reverse();
      intValue = o.join(',') + ',' + intValue.slice(-3);
    }

    decimalValue = decimalValue.padEnd(2, '0');
    let formattedValue = intValue + '.' + decimalValue;

    return formattedValue;
  }
  return null;
}

export const splitEqualText = (text: string, maxLength: number) => {
  if (text.length > maxLength) {
    const words = text.split(' '); // Split text into words
    let totalLength = 0;
    let midIndex = 0;

    // Find the index of the word closest to the middle
    for (let i = 0; i < words.length; i++) {
      totalLength += words[i].length;
      if (totalLength >= text.length / 2) {
        midIndex = i;
        break;
      }
    }

    // Concatenate words into two lines
    const firstLine = words.slice(0, midIndex).join(' ');
    const secondLine = words.slice(midIndex).join(' ');
    return `${firstLine}\n${secondLine}`;
  }
  return text;
};

export function breakSentence(sentence: string, maxLength: number): string {
  const words: string[] = sentence.split(' ');
  const chunks: string[] = [];
  let currentChunk: string = '';

  for (const word of words) {
    if ((currentChunk + ' ' + word).length <= maxLength) {
      currentChunk += (currentChunk === '' ? '' : ' ') + word;
    } else {
      chunks.push(currentChunk);
      currentChunk = word;
    }
  }

  if (currentChunk !== '') {
    chunks.push(currentChunk);
  }

  // Join the chunks with newline characters
  return chunks.join('\n');
}
export const generateUniqueNumber = () => {
  const currentTime: number = new Date().getTime();
  const uniqueNumber: number = parseInt(
    currentTime.toString() + Math.floor(Math.random() * 100000).toString(),
  );
  return uniqueNumber;
};

export const calculateRatingPercentage = (
  starNumber: number,
  rating: number,
) => {
  if (starNumber <= rating) {
    return '100%';
  } else if (starNumber - 1 < rating) {
    return `${(rating - (starNumber - 1)) * 100}%`;
  } else {
    return '0%';
  }
};
