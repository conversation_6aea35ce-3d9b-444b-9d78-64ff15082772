import {PERMISSIONS} from 'react-native-permissions';

export enum AppPrefernceKeys {
  PREFERENCE_KEY_IS_BIOMETRIC_ENABLED = 'PREFERENCE_KEY_IS_BIOMETRIC_ENABLED',
}

export enum RQKey {
  GET_ADDRESS = 'GET_ADDRESS',
  USER_PROFILE = 'USER PROFILE',
  PRODUCT_LIST = 'PRODUCT_LIST',
  PRODUCT_SEARCH_LIST = 'PRODUCT_SEARCH_LIST',
  CATEGORY = 'CATEGORY',
  PRODUCT_MULTI_VARIANTS_SIZE = 'PRODUCT_MULTI_VARIANTS_SIZE',
  ORDER_LIST = 'ORDER_LIST',
  REFUND_LIST = 'REFUND_LIST',
  REFUND_DETAILS = 'REFUND_DETAILS',
  FEEDBACK_CONFIG = 'FEEDBACK_CONFIG',
  
}

export const AndroidPermission = {...PERMISSIONS.ANDROID};
export const IOSPermission = {...PERMISSIONS.IOS};
export enum EventEmitterKeys {
  CLOSE = 'onClose',
  ON_LOGGED_IN = 'onLogin',
  WEB_VIEW_EVENT_EMITT = 'WebViewEventEmitt',
  PINCODE_CHANGE = 'PINCODE_CHANGE',
}

export enum WebViewEventKeys {
  SEND_TO_WEB_CRA_DETAILS = 'sendToWebCraDetails',
  PINCODE_MODIFIED_ON_NATIVE = 'pincodeModifiedOnNative',
}

export enum AsyncStorageKeys {
  USER_DETAILS = 'UserDetails',
  USER_SESSION = 'UserSession',
  GUEST_USER_SESSION = 'GuestUserSession',
  CRA_USER_SESSION_DATA = 'CraUserSessionData',
  JCP_USER_SESSION_DATA = 'JcpUserSessionData',
  X_LOCATION_DETAIL = 'xLocationDetail',
  ADDRESS_LIST_DETAIL = 'AddressListDetail',
  PINCODE = 'Pincode',
  CART_DATA = 'cartData',
  DISCOVER_MORE = 'DiscoverMore',
  RECENT_SEARCH = 'RecentSearch',
  ALL_CATEGORIES = 'AllCategories',
  RECOMMENDED_ITEMS = 'RecommendedItems',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PRIVACY_POLICY = 'PRIVACY_POLICY',
  QC_JOURNEY = 'QC_JOURNEY',
  QC_KEY = 'QC_KEY',
  DELIVER_TO_BAR_QC_TEXT = 'deliverToBarQcText',
  SET_DELIVER_TO_BAR_QC = 'SET_DELIVER_TO_BAR_QC',
  PROFILE_DETAILS = 'PROFILE_DETAILS',
  SEARCH_END_POINT_DETAILS = 'SearchEndPointDetails',
}

export enum StaticImages {
  PLACEHOLDER_IMAGE = 'https://cdn.pixelbin.io/v2/jiomart-fynd/jio-np/wrkr/jmrtz0/organization/64b003218f39a39ec141e0e1/theme/assets/jiomart-default-image.959a1b7a3b2d6bbecb905441f2832a89.png',
}
