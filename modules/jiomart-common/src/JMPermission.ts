import RNPermissions, {
  Permission,
  RESULTS,
  type PermissionStatus,
} from 'react-native-permissions';
import type {AndroidPermission, IOSPermission} from './JMConstants';

export type JMPermissionType =
  | keyof typeof AndroidPermission
  | keyof typeof IOSPermission;

export const checkSinglePermission = async (permission: JMPermissionType) => {
  return await RNPermissions.check(permission as Permission);
};
export const checkMultiPermission = async (permission: JMPermissionType[]) => {
  return await RNPermissions.checkMultiple(permission);
};

export const requestSinglePermission = async (permission: JMPermissionType) => {
  return await RNPermissions.request(permission as Permission);
};

export const requestMultiPermissions = async (
  permission: JMPermissionType[],
) => {
  return await RNPermissions.requestMultiple(permission);
};

export const checkAndRequestSinglePermission = async (
  permission: JMPermissionType,
) => {
  const status = await checkSinglePermission(permission);

  if (status === RESULTS.DENIED) {
    return await requestSinglePermission(permission);
  }
  return status;
};

export const checkAndRequestMultiPermission = async (
  permissions: JMPermissionType[],
) => {
  const results: Record<JMPermissionType, PermissionStatus> = {} as Record<
    JMPermissionType,
    PermissionStatus
  >;

  for (const permission of permissions) {
    const status = await checkSinglePermission(permission);

    if (status === RESULTS.DENIED) {
      results[permission] = await requestSinglePermission(permission);
    } else {
      results[permission] = status;
    }
  }

  return results;
};

export const openAppSetting = () => {
  return RNPermissions.openSettings();
};
