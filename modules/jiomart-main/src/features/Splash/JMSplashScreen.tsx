import React from 'react';
import LottieView from 'lottie-react-native';
import { StyleSheet, View } from 'react-native';
import {
  AppScreens,
  type ScreenProps,
} from '../../../../jiomart-common/src/JMAppScreenEntry';
import {
  ActionType,
  navBeanObj,
  NavigationType,
} from '../../../../jiomart-common/src/JMNavGraphUtil';
import { HeaderType } from '../../../../jiomart-common/src/JMScreenSlot.types';
import { DeeplinkHandler } from '../../../../jiomart-general/src/ui/JMScreenSlot';
import splashAnimationAssetData from './assets/jiomart_splash.json';
import {navigateTo} from '../../../../jiomart-general/src/navigation/JMNavGraph';
import {getBaseURL} from '../../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {CustomStatusBar} from '../../../../jiomart-general/src/ui/CustomStatusBar';
import useJMInitialisationController from '../../controllers/useJMInitialisationController';

// Define props for SplashScreen using NativeStackScreenProps
type Props = ScreenProps<typeof AppScreens.SPLASH_SCREEN>;

const JMSplashScreen = ({ route, navigation }: Props) => {
  useJMInitialisationController();
  const onAnimationFinish = () => {
    navigateTo(
      navBeanObj({
        actionType: ActionType.OPEN_WEB_URL,
        destination: AppScreens.HOME_SCREEN,
        headerVisibility: HeaderType.CUSTOM,
        navigationType: NavigationType.REPLACE,
        loginRequired: false,
        actionUrl: getBaseURL(),
        shouldShowBottomNavBar: false,
        shouldShowDeliverToBar: true,
        headerType: 5,
      }),
      navigation,
    );
  };
  const mainUI = () => {
    return (
      <View style={styles.container}>
        <CustomStatusBar color={'#0078AD'} />
        <LottieView
          source={splashAnimationAssetData}
          autoPlay
          loop={false}
          resizeMode="cover"
          onAnimationFinish={onAnimationFinish}
          style={{ width: '100%', height: '100%' }}
        />
      </View>
    );
  };
  return (
    <DeeplinkHandler
      navigationBean={{
        destination: AppScreens.SPLASH_SCREEN,
        headerVisibility: HeaderType.HIDDEN,
        actionUrl: '',
        userAuthenticationRequired: 0,
        actionType: ActionType.OPEN_NATIVE,
      }}
      navigation={navigation}
      children={bean => mainUI()}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0078AD', // Mart Default background for splash screen
  },
});

export default JMSplashScreen;
