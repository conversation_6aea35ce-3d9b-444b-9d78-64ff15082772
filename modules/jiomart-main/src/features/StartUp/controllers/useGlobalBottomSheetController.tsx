import { getPrefString } from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import { AsyncStorageKeys } from '../../../../../jiomart-common/src/JMConstants';
import { JMSharedViewModel } from '../../../../../jiomart-common/src/JMSharedViewModel';
import { useGlobalState } from '../../../../../jiomart-general/src/context/JMGlobalStateProvider';
import { useConfigFile } from '../../../../../jiomart-general/src/hooks/useJMConfig';
import { JMConfigFileName } from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import usePermissionBottomSheetController from './usePermissionBottomSheetController';

const useGlobalBottomSheetController = () => {
  const config = useConfigFile(
    JMConfigFileName.JMCommonContentFileName,
  );
  const permissionConfig = config?.permissionConfig;
  const privacyPolicyConfig = config?.privacyPolicyConfig;

  // const softUpdate = useSoftUpdateBottomSheetController();
  const permission = usePermissionBottomSheetController();
  // const {setKeyForEachLaunch, getValueForEachLaunch} = useEachAppLaunch();

  const {
    setPermissionBtmSheet,
    setPrivacyPolicyBtmSheet,
    setSoftUpdateBtmSheet,
  } = useGlobalState();

  const checkAndOpenNextSheet = async () => {
    try {
      const isPermissionGranted = await getPrefString(
        AsyncStorageKeys.PERMISSION_GRANTED,
      );
      // const isValidForSoftUpdate = await softUpdate.isValidToOpenSoftUpdate({
      //   appVersion: softUpdate.appVersion,
      //   ...softUpdate.softUpdateConfig,
      // });
      const isPrivacyPolicy = await getPrefString(
        AsyncStorageKeys.PRIVACY_POLICY,
      );
      if (isPermissionGranted !== 'true') {
        setPermissionBtmSheet(true);
      }
      // else if(// logic for soft update open) {
      //  setSoftUpdateBtmSheet(true)
      // }
      else if (
        JMSharedViewModel.Instance.loggedInStatus &&
        isPrivacyPolicy !== 'true'
      ) {
        setPrivacyPolicyBtmSheet(true);
      }
    } catch (error) {
      console.log('🚀 ~ checkAndOpenNextSheet ~ error:', error);
    }
  };

  return {
    permissionConfig,
    privacyPolicyConfig,
    // ...softUpdate,
    ...permission,
    checkAndOpenNextSheet,
  };
};

export default useGlobalBottomSheetController;
