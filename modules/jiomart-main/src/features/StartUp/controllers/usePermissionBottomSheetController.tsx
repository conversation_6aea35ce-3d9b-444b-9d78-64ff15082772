import PushNotificationIOS from '@react-native-community/push-notification-ios';
import {Platform} from 'react-native';
import {RESULTS} from 'react-native-permissions';
import useAddressOperation from '../../../../../jiomart-address/src/hooks/useAddressOperation';
import useCurrentLocation from '../../../../../jiomart-address/src/hooks/useCurrentLocation';
import {addStringPref} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  AndroidPermission,
  AsyncStorageKeys,
  IOSPermission,
} from '../../../../../jiomart-common/src/JMConstants';
import {
  checkAndRequestMultiPermission,
  type JMPermissionType,
} from '../../../../../jiomart-common/src/JMPermission';
import {useConfigFile} from '../../../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

let isPermissionDenied = true;

const usePermissionBottomSheetController = () => {
  const config: any = useConfigFile(JMConfigFileName.JMCommonContentFileName);
  const permissionConfig = config?.permissionConfig;

  const {fetchLocationFromReverseGeoCodeFromLatLong} = useCurrentLocation({
    alertBlocked: permissionConfig?.alert?.blockedLocation,
  });
  const {checkAndSetPincode} = useAddressOperation();

  const handleRequestPermssion = async () => {
    try {
      await addStringPref(AsyncStorageKeys.PERMISSION_GRANTED, 'true');
      if (Platform.OS === 'ios') {
        await PushNotificationIOS.requestPermissions({
          alert: true,
          badge: true,
          sound: true,
        });
      }
      const permissionRes = await checkAndRequestMultiPermission(
        permissionConfig?.requestPermissions[Platform.OS],
      );
      return permissionRes;
    } catch (error) {
      return null;
    }
  };

  const handlePermission = async () => {
    try {
      const permissionRes = await handleRequestPermssion();

      const locationPermission =
        Platform.OS === 'android'
          ? AndroidPermission.ACCESS_FINE_LOCATION
          : IOSPermission.LOCATION_WHEN_IN_USE;
      // const adsTargeting =
      //   Platform.OS === 'ios' ? IOSPermission.APP_TRACKING_TRANSPARENCY : '';

      if (
        permissionRes?.[locationPermission as JMPermissionType] ===
          RESULTS.GRANTED ||
        permissionRes?.[locationPermission as JMPermissionType] ===
          RESULTS.LIMITED
      ) {
        const stats = await fetchLocationFromReverseGeoCodeFromLatLong();
        if (stats?.address?.pin) {
          checkAndSetPincode({
            pincode: stats?.address?.pin,
            state: stats?.address?.state,
            city: stats?.address?.city,
          });
        }
      } else {
        isPermissionDenied = false;
      }
    } catch (error) {
    } finally {
      return isPermissionDenied;
    }
  };

  return {
    handlePermission,
  };
};

export default usePermissionBottomSheetController;
