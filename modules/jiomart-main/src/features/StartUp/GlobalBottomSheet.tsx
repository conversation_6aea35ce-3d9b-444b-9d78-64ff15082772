import React from 'react';
import {useGlobalState} from '../../../../jiomart-general/src/context/JMGlobalStateProvider';
import BottomSheet from '../../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import useGlobalBottomSheetController from './controllers/useGlobalBottomSheetController';
import PermissionBottomSheet from './PermissionBottomSheet';
import PrivacyPolicyBottomSheet from './PrivacyPolicyBottomSheet';
import {GlobalBottomSheetProps} from './types/GlobalBottomSheetType';

const GlobalBottomSheet = (props: GlobalBottomSheetProps) => {
  const {openPincodeBottomSheet} = props;
  const {
    permissionConfig,
    privacyPolicyConfig,
    checkAndOpenNextSheet,
    handlePermission,
  } = useGlobalBottomSheetController();
  const {
    permissionBtmSheet,
    privacyPolicyBtmSheet,
    softUpdateBtmSheet,
    setPermissionBtmSheet,
    setPrivacyPolicyBtmSheet,
    setSoftUpdateBtmSheet,
  } = useGlobalState();

  return (
    <>
      {permissionConfig?.isVisible ? (
        <BottomSheet
          visible={permissionBtmSheet}
          disabledBackDropClick
          disableBlurGain
          disabledGesture>
          <PermissionBottomSheet
            permissionConfigData={permissionConfig}
            onProceed={async () => {
              setPermissionBtmSheet(false);
              if (await handlePermission()) {
                checkAndOpenNextSheet();
              } else {
                openPincodeBottomSheet(true);
              }
            }}
          />
        </BottomSheet>
      ) : null}
      {privacyPolicyConfig?.isVisible ? (
        <BottomSheet
          visible={privacyPolicyBtmSheet}
          disabledBackDropClick
          disableBlurGain
          disabledGesture>
          <PrivacyPolicyBottomSheet
            onClose={() => {
              setPrivacyPolicyBtmSheet(false);
              checkAndOpenNextSheet();
            }}
            onAccept={() => {
              setPrivacyPolicyBtmSheet(false);
              checkAndOpenNextSheet();
            }}
            configData={privacyPolicyConfig}
          />
        </BottomSheet>
      ) : null}

      {/* <BottomSheet
        visible={softUpdateBtmSheet}
        disabledBackDropClick={softUpdateConfig?.immediate_update}
        disabledGesture={softUpdateConfig?.immediate_update}
        disableBlurGain
        onBackDropClick={() => {
          softUpdateRef.current?.close();
          checkAndHideBnb();
          checkAndOpenNextSheet();
        }}
        onDrag={() => {
          softUpdateRef.current?.close();
          checkAndHideBnb();
          checkAndOpenNextSheet();
        }}>
        <SoftUpdateBtmSheet
          config={softUpdateConfig}
          onClose={() => {
            if (softUpdateConfig?.immediate_update) {
              return;
            }
              checkAndHideBnb();
            softUpdateRef.current?.close();
            checkAndOpenNextSheet();
          }}
          onButtonPress={() => {softUpdateApp(); checkAndHideBnb();}}
        />
      </BottomSheet>
    */}
    </>
  );
};

export default GlobalBottomSheet;
