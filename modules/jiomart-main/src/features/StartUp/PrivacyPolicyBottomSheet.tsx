import React, {useCallback} from 'react';
import {SafeAreaView, View} from 'react-native';
import {ButtonKind, ButtonSize} from '@jio/rn_components/src/index.types';
import {JioButton} from '@jio/rn_components';
import type {PrivacyPolicyBottomSheetProps} from './types/PrivacyPolicyBottomSheetType';
import {styles} from './styles/PrivacyPolicyBottomSheetStyle';
import JMBtmSheetHeader from '../../../../jiomart-general/src/ui/JMBtmSheetHeader';
import JmLinkMixText from '../../../../jiomart-general/src/ui/JmLinkMixText';
import {addStringPref} from '../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../jiomart-common/src/JMConstants';

const PrivacyPolicyBottomSheet = (props: PrivacyPolicyBottomSheetProps) => {
  const {onClose, onAccept, configData, close} = props;

  const handleAccept = useCallback(async () => {
    await addStringPref(AsyncStorageKeys.PRIVACY_POLICY, 'true');
    close?.(onAccept);
  }, [close, onAccept]);
  const handleClose = useCallback(async () => {
    await addStringPref(AsyncStorageKeys.PRIVACY_POLICY, 'true');
    close?.(onClose);
  }, [close, onClose]);

  return (
    <SafeAreaView>
      <JMBtmSheetHeader title={configData?.title} onPress={handleClose} />
      <View style={styles.container}>
        <JmLinkMixText
          regularString={configData?.regularString}
          clickableString={configData?.clickableString}
          redirectionUrl={configData?.redirectionUrl}
        />
      </View>
      <JioButton
        title={configData?.buttonText}
        size={ButtonSize.MEDIUM}
        kind={ButtonKind.PRIMARY}
        stretch={true}
        onClick={handleAccept}
        innerStyle={styles.accept}
        style={styles.acceptButton}
      />
    </SafeAreaView>
  );
};

export default PrivacyPolicyBottomSheet;
