import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconKind,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';

type ProductsBottomSheetProps = {
  l3CategoryData: any;
  title?: string;
  hideDivider: boolean;
  selected?: boolean;
  onPress?: () => void;
};

const ProductsBottomSheet = (props: ProductsBottomSheetProps) => {
  const {title, selected, hideDivider, onPress} = props;

  return (
    <TouchableOpacity
      disabled={selected}
      onPress={onPress}
      style={[
        styles.products_container,
        selected && styles.selectedBorderColor,
        hideDivider && styles.hideBorder,
      ]}>
      <JioText
        style={styles.products_text}
        color={selected ? 'primary_60' : 'primary_grey_80'}
        appearance={JioTypography.BODY_XS}
        text={title ?? ''}
      />

      <View style={styles.toggle_container}>
        <JioIcon
          ic={'IcChevronRight'}
          color={IconColor.PRIMARY60}
          kind={selected ? IconKind.BACKGROUND : IconKind.DEFAULT}
          size={selected ? IconSize.LARGE : IconSize.MEDIUM}
        />
      </View>
    </TouchableOpacity>
  );
};

export default ProductsBottomSheet;

const styles = StyleSheet.create({
  products_container: {
    height: rh(41),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  products_text: {
    width: rw(252),
    fontFamily: 'JioType-Medium',
    fontSize: 14,
    fontWeight: '500',
    color: '#000000A6',
  },
  toggle_container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: rw(32),
    height: rw(32),
  },
  selectedBorderColor: {
    borderBottomColor: '#67C3EF',
  },
  hideBorder: {
    borderBottomWidth: 0,
  },
});
