import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import OneRetailUI from '@ucp/one-retail';
import {NavigationStackData} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getDestinationFromDeeplinkUrl} from '../../../jiomart-general/src/deeplink/JMDeeplinkUtility';
import CommonWebViewScreen from '../../../jiomart-webmanager/src/WebViewScreen';
import JMSearchScreen from '../../../jiomart-general/src/ui/SearchScreen/JMSearchScreen';
import AllCategoriesScreen from '../../../jiomart-category/src/AllCategoriesScreen';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import JMSplashScreen from '../features/Splash/JMSplashScreen';
import JMOrderNavigation from '../../../jiomart-order/src/JMOrderNavigation';
import JMAddressNavigation from '../../../jiomart-address/src/JMAddressNavigation';
import JMProductListingNavigation from '../../../jiomart-product-list/src/JMProductListingNavigation';
import Account from '../../../jiomart-account/src/screens/Account';
import JMFeedbackNavigation from '../../../jiomart-feedback/src/JMFeedbackNavigation';
import JMHomeScreen from '../../../jiomart-home/src/screen/JMHomeScreen';
import NotificationScreen from '../../../jiomart-account/src/screens/NotificationScreen';

const NavStack = createNativeStackNavigator<NavigationStackData>();

const JMNavigationStack = () => {
  return (
    <NavStack.Navigator
      initialRouteName={
        getDestinationFromDeeplinkUrl(
          '',
          AppScreens.SPLASH_SCREEN,
          true,
        ) as keyof NavigationStackData
      }
      screenOptions={{headerShown: false}}>
      <NavStack.Screen
        name={AppScreens.SPLASH_SCREEN}
        component={JMSplashScreen}
        options={{
          animation: 'none',
        }}
      />
      <NavStack.Screen
        name={AppScreens.HOME_SCREEN}
        component={JMHomeScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.COMMON_WEB_VIEW}
        component={CommonWebViewScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ADDRESS_SCREEN}
        component={JMAddressNavigation}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ORDER_SCREEN}
        component={JMOrderNavigation}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.PRODUCT_LISTING_SCREEN_START}
        component={JMProductListingNavigation}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ONE_RETAIL_SCREEN}
        component={OneRetailUI}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ALL_CATEGORIES}
        component={AllCategoriesScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.SEARCH_SCREEN}
        component={JMSearchScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ACCOUNT_SCREEN}
        component={Account}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
       name={AppScreens.FEEDBACK_SCREEN}
        component={JMFeedbackNavigation}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.NOTIFICATION_SCREEN}
        component={NotificationScreen}
        options={{
          animation: 'default',
        }}
      />
    </NavStack.Navigator>
  );
};

export default JMNavigationStack;
