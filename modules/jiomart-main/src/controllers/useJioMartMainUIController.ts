import {useCallback, useEffect} from 'react';
import {
  subscribeToRNEvent,
  unsubscribeToRNEvent,
} from '../../../../node_modules/@ucp/one-retail/src/utils/bridge/Emitter';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import JMUserApiNetworkController from '../../../../modules/jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import useCartOperation from '../../../jiomart-cart/src/hooks/useCartOperation';
import {
  AsyncStorageKeys,
  EventEmitterKeys,
} from '../../../jiomart-common/src/JMConstants';
import {
  ActionType,
  navBeanObj,
  NavigationBean,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import useWishlistOperation from '../../../jiomart-wishlist/src/hooks/useWishlistOperation';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useDispatch} from 'react-redux';
import {resetCart} from '../../../jiomart-cart/src/slices/cartSlice';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {HeaderType} from '../../../jiomart-common/src/JMScreenSlot.types';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {resetWishlist} from '../../../jiomart-wishlist/src/slices/wishlistSlice';
import { JMStorageService } from '../../../jiomart-networkmanager/src/api/utils/JMStorageService';
import { JMDatabaseManager } from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import useNotification from '../../../jiomart-account/src/hooks/useNotification';

const userApiNetworkController = new JMUserApiNetworkController();

const useJioMartMainUIController = (
  navigation: NativeStackNavigationProp<any>,
) => {
  const dispatch = useDispatch();
  JMLogger.log('useJioMartMainUIController');
  const {saveUserData} = useUserProfile();
  const {setEvent, setUserInitials, setNotificationCount} = useGlobalState();

  const {getCart} = useCartOperation();
  const {getWishlistIds} = useWishlistOperation();

  const clearOnLogout = () => {
    JMStorageService.clearAllKeys();
    setUserInitials('');
    dispatch(resetCart());
    dispatch(resetWishlist());
    userApiNetworkController.fetchGuestUserSession();
  };
  const handleChildEvent = useCallback(async (data: any) => {
    if (data?.logout) {
      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
        await userApiNetworkController.logoutUser();
      }
      clearOnLogout();
      if (navigation && navigation.canGoBack!!) {
        navigateTo(
          navBeanObj({
            actionType: ActionType.OPEN_WEB_URL,
            destination: AppScreens.HOME_SCREEN,
            headerVisibility: HeaderType.CUSTOM,
            navigationType: NavigationType.RESET,
            loginRequired: false,
            actionUrl: getBaseURL(),
            shouldShowBottomNavBar: false,
            shouldShowDeliverToBar: true,
            headerType: 5,
          }),
          navigation,
        );
      }
      return;
    } else if (navigation && navigation.canGoBack!!) {
      navigation.goBack();
    }
  }, []);

  async function setUserInitialsToGlobal(userDetailRes) {
    const userDetails = JSON.stringify(userDetailRes);
    saveUserData(userDetailRes);
    JMDatabaseManager.user.saveUserDetails(userDetails)
    setUserInitials(
      userDetailRes?.first_name + ' ' + userDetailRes?.last_name,
    );
  }

  const handleDidLoggedInEvent = useCallback(async (data?: any) => {
    try {
      if (data.authCode) {
        const response =
          await userApiNetworkController.fetchLoggedInUserSession(
            data.authCode,
          );

        if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
          if (response && response.status === 'success') {
            const profileResponse =
              await userApiNetworkController.fetchUserDetails();
            setUserInitials(profileResponse?.full_name);
            setEvent({
              WebViewEventEmitt: {
                sendToWebCraDetails: JSON.stringify(response),
              },
            });
            console.log('🚀 ~ handleDidLoggedInEvent ~ response.data:');
            getCart.mutate();
            getWishlistIds.mutate();
          }
        } else {
          if (response && response?.success === true) {
            const userDetailRaw = response?.data?.jcp_user_details?.users?.[0];
            if (userDetailRaw) {
              setUserInitialsToGlobal(userDetailRaw);
            }
            setEvent({
              WebViewEventEmitt: {
                sendToWebCraDetails: JSON.stringify(response),
              },
            });
            console.log(
              '🚀 ~ handleDidLoggedInEvent ~ response.data:',
              JSON.stringify(response),
            );
          }
          getCart.mutate();
          getWishlistIds.mutate();
        }
      }
      if (
        JMSharedViewModel.Instance.getNavigationData() != null &&
        JMSharedViewModel.Instance.getNavigationData() != undefined
      ) {
        navigation?.pop();
        navigateTo(
          JMSharedViewModel.Instance.getNavigationData() as NavigationBean,
          navigation,
        );
      }
    } catch (error) {
      JMLogger.log('Error in handleDidLoggedInEvent - ', error);
      if (navigation && navigation.canGoBack!!) {
        navigation.goBack();
      }
    }
  }, []);

  useEffect(() => {
    subscribeToRNEvent(EventEmitterKeys.CLOSE, handleChildEvent);
    subscribeToRNEvent(EventEmitterKeys.ON_LOGGED_IN, handleDidLoggedInEvent);
    return () => {
      console.log('cleanup EventEmitterKeys.CLOSE');
      unsubscribeToRNEvent(EventEmitterKeys.CLOSE, () =>
        handleChildEvent(null),
      );
      unsubscribeToRNEvent(
        EventEmitterKeys.ON_LOGGED_IN,
        handleDidLoggedInEvent,
      );
    };
  }, []);
  useNotification(val => {
    setNotificationCount(val);
  });
};

export default useJioMartMainUIController;
