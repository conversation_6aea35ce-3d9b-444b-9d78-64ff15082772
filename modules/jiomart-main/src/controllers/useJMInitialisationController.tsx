import { useEffect} from 'react';
import JMUserApiNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import useCartOperation from '../../../jiomart-cart/src/hooks/useCartOperation';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {
  useAppStartupConfig,
  useVersionFile,
} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import JMDataLoaderNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMDataLoaderNetworkController';

const userApiNetworkController = new JMUserApiNetworkController();
const dataLoaderNetworkController = new JMDataLoaderNetworkController();
const useJMInitialisationController = () => {
  useVersionFile({enabled: true});
  const startupConfig: any = useAppStartupConfig([
    JMConfigFileName.JMCommonContentFileName,
    JMConfigFileName.JMAddressConfigurationFileNAme,
  ]);

  const commonContentConfig =
    startupConfig?.[JMConfigFileName.JMCommonContentFileName];

  const {setUserInitials, setAddress} = useGlobalState();

  useEffect(() => {
    if (commonContentConfig?.location) {
      JMDatabaseManager.address.setIntialAddress({
        ...commonContentConfig?.location,
        pin: commonContentConfig?.location?.pincode,
      });
      handleAddress({
        ...commonContentConfig?.location,
        pin: commonContentConfig?.location?.pincode,
      });
    }
  }, [commonContentConfig?.location]);

  const handleAddress = async (initialAddress?: JMAddressModel) => {
    let address: any = await JMDatabaseManager.address.getDefaultAddress();
    JMLogger.log('handleAddress ', 'handleAddress ' + JSON.stringify(address));
    address = !address ? initialAddress : JSON.parse(address ?? '');
    setAddress(address);
  };

  const {getCart} = useCartOperation();

  const callSessionApis = async () => {
    try {
      JMLogger.log('callIntialApis');
      const guestUserSession = await JMDatabaseManager.user.getGuestUserSession();
      const userSession = await JMDatabaseManager.user.getUserSession();
      if (!guestUserSession && !userSession) {
        await userApiNetworkController.fetchGuestUserSession();
      }
      await setUserInitialsToGlobal();
      await dataLoaderNetworkController.fetchDataLoader();
      getCart.mutate();
    } catch (error) {
      throw error;
    }
  };
  useEffect(() => {
    callSessionApis();
  }, []);

  async function setUserInitialsToGlobal() {
    const userDetailsString = await JMDatabaseManager.user.getUserDetails();
    if (userDetailsString) {
      const userDetails = JSON.parse(userDetailsString);
      if(userDetails){
        JMSharedViewModel.Instance.setLoggedInStatus(userDetails !== null);
        if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU)
        setUserInitials(userDetails?.full_name);
        else setUserInitials(userDetails?.full_name);
      }
    }
  }
};

export default useJMInitialisationController;
