import React from 'react';
import JMNavigationStack from './navigation/JMNavigationStack';
import useJioMartMainUIController from './controllers/useJioMartMainUIController';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {Provider} from 'react-redux';
import store from './store/store';
import {CustomTokenProvider} from '@jio/rn_components';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {GlobalStateProvider} from '../../jiomart-general/src/context/JMGlobalStateProvider';
import {Platform} from 'react-native';
import Config from 'react-native-config';
import {initializeGooglePlacesSdk} from '../../jiomart-common/src/GoogleSdkUtility';
import {JMSharedViewModel} from '../../jiomart-common/src/JMSharedViewModel';
import { isNullOrUndefinedOrEmpty } from '../../jiomart-common/src/JMObjectUtility';
import { setCurrentEnvironment } from '../../jiomart-networkmanager/src/JMEnvironmentConfig';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { NavigationBean, NavigationStackData } from '../../jiomart-common/src/JMNavGraphUtil';
import {NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';

export interface PropData {
  env?: string;
  directDeeplink?: string;
  deeplink?: string;
  mUri?: string;
  payload?: string;
  mobileNumber?: string;
  source?: string;
  reactInAppScreen?: boolean;
}

const queryClient = new QueryClient();
const googleMapKey =
  Platform.OS === 'ios'
    ? Config.IOS_GOOGLE_MAP_KEY_DEBUG
    : 'AIzaSyBKUOZtIHL2zbbmA9mMizy0Nv0iPvQfPLo';

initializeGooglePlacesSdk(`${googleMapKey}`);

// Internal component that uses the controller hook inside QueryClientProvider context
function JioMartMainUIContent(navigation: NativeStackNavigationProp<any>) {
  useJioMartMainUIController(navigation);
  return <JMNavigationStack />;
}


export function setReactNativeEnv(props: PropData) {
  if (props?.env && !isNullOrUndefinedOrEmpty(props?.env)) {
    setCurrentEnvironment(props?.env);
  }
}
export function setAppSource(source: string) {
  if (source && !isNullOrUndefinedOrEmpty(source)) {
    const sourceType = JMSharedViewModel.Instance.getAppSourceTypeData(source);
    if (sourceType) {
      JMSharedViewModel.Instance.setAppSourceType(sourceType);
    }
  }
}

export function setReactInAppScreen(props: PropData) {
  JMSharedViewModel.Instance.reactInAppScreen = props.reactInAppScreen ?? false;
}

function setPropDataInApp(paramBean: PropData) {
  setReactNativeEnv(paramBean);
  setAppSource(paramBean?.source ?? '');
  setReactInAppScreen(paramBean);
}

export function setExternalDeeplinkData(props: PropData) {
  if (
    !isNullOrUndefinedOrEmpty(props.mUri) ||
    !isNullOrUndefinedOrEmpty(props.payload)
  ) {
    const deeplinkData = {
      mUri: props.mUri ?? '',
      payload: props.payload ?? '',
    };
    JMSharedViewModel.Instance.setExternalDeeplink(deeplinkData);
  } else if (props.deeplink && !isNullOrUndefinedOrEmpty(props.deeplink)) {
    const deeplinkData = {
      mUri: props.deeplink ?? '',
      payload: '',
    };
    JMSharedViewModel.Instance.setExternalDeeplink(deeplinkData);
  }
}


type props = NativeStackScreenProps<NavigationStackData>;
export function JioMartMainUI({route, navigation}: props) {
  const beanData = route.params as NavigationBean;
  console.debug('JioHealthMainUI' + 'JioHealthMainUI flowInitialised'+JMSharedViewModel.Instance.flowInitialised);
  const handleDeeplinkParams = () => {
    if (beanData?.params && !JMSharedViewModel.Instance.flowInitialised) {
        JMSharedViewModel.Instance.flowInitialised = true
        const paramBean = beanData.params as PropData;
        setPropDataInApp(paramBean);
        JMSharedViewModel.Instance.setDeeplinkUrlData(paramBean.directDeeplink ?? '');
        setExternalDeeplinkData(paramBean);
    }
  };
  handleDeeplinkParams();
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
      <SafeAreaProvider>
        <CustomTokenProvider
          value={JMSharedViewModel.Instance.globalThemeToken}>
          <GestureHandlerRootView>
            <GlobalStateProvider>
              <JioMartMainUIContent {...navigation}/>
            </GlobalStateProvider>
          </GestureHandlerRootView>
        </CustomTokenProvider>
        </SafeAreaProvider>
      </Provider>
    </QueryClientProvider>
  );
}
