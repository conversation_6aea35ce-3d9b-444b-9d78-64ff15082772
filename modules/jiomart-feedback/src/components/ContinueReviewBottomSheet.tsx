import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import JMBtmSheetHeader from '../../../jiomart-general/src/ui/JMBtmSheetHeader';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';

interface ContinueReviewBottomSheetProps extends BottomSheetChildren {
  config: any;
  onGoBackPress?: () => void;
  onClose?: () => void;
}

const ContinueReviewBottomSheet = (props: ContinueReviewBottomSheetProps) => {
  const {config, close, onClose, onGoBackPress} = props;

  const insets = useSafeAreaInsets();

  return (
    <>
      <JMBtmSheetHeader
        title={config?.headerTitle}
        onPress={() => {
          close?.(onClose);
        }}
      />
      <View style={styles.container}>
        <JioText
          text={config?.modalText}
          appearance={JioTypography.BODY_S}
          color={'primary_grey_80'}
          style={styles.text}
        />
        <View
          style={[styles.btnContainer, {paddingBottom: insets.bottom + 12}]}>
          <TouchableOpacity
            onPress={() => {
              close?.(onGoBackPress);
            }}
            style={styles.goBackBtn}>
            <JioText
              text={config?.goBackBtn?.title}
              appearance={JioTypography.BODY_S}
              color={'primary_60'}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.continueBtn}
            onPress={() => {
              close?.(onClose);
            }}>
            <JioText
              text={config?.continueBtn?.title}
              appearance={JioTypography.BODY_S}
              color={'white'}
            />
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: rw(24),
  },
  btnContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  text: {
    paddingBottom: 34,
    paddingTop: 16,
  },
  goBackBtn: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(224, 224, 224, 1)',
    paddingVertical: rh(12),
    borderRadius: 1000,
  },
  continueBtn: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: rh(12),
    borderRadius: 1000,
    backgroundColor: 'rgba(0, 120, 173, 1)',
  },
});

export default ContinueReviewBottomSheet;
