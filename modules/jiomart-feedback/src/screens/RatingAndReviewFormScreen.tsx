import React from 'react';
import type {RatingAndReviewFormScreenProps} from '../types/JMRatingAndReviewFormScreenType';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import useRatingAndReviewFormScreenController, {
  FormNameField,
} from '../controller/useRatingAndReviewFormScreenController';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import {
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import ReviewProduct from '../components/ReviewProduct';
import {JioButton, JioIcon, JioText} from '@jio/rn_components';
import {
  ButtonKind,
  ButtonSize,
  ButtonState,
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import SelectStarRating from '../../../jiomart-general/src/ui/SelectStarRating';
import Divider, {
  DividerGap,
  DividerType,
} from '../../../jiomart-general/src/ui/Divider';
import type {IconKey} from '@jio/rn_components/src/utils/IconUtility';
import {SvgUri} from 'react-native-svg';
import {getSvgUrlData} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import BottomSheet from '../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import ContinueReviewBottomSheet from '../components/ContinueReviewBottomSheet';
import {NavigationBeanType} from '../../../jiomart-general/src/ui/Header/JioMartHeader';

const RatingAndReviewFormScreen = (props: RatingAndReviewFormScreenProps) => {
  const {
    config,
    navigation,
    navigationBean,
    insets,
    productName,
    productImage,
    formData,
    formError,
    handleFormData,
    mappedConfig,
    handleInputRef,
    handleFormError,
    onBlurValidata,
    continueReviewBottomSheet,
    openContinueReviewBottomSheet,
    closeContinueReviewBottomSheet,
    uploadImages,
    imageSelectionLimit,
    removeImage,
    images,
    editImages,
    loading,
    handleFAQLink,
    onPressOfSubmit,
  } = useRatingAndReviewFormScreenController(props);

  const bottomSheetContent = () => {
    return (
      <BottomSheet
        visible={continueReviewBottomSheet}
        isStretchEnabled
        onBackDropClick={closeContinueReviewBottomSheet}
        onDrag={closeContinueReviewBottomSheet}>
        <ContinueReviewBottomSheet
          config={config?.bottomSheet?.continueReview}
          onGoBackPress={() => {
            navigation.goBack();
          }}
          onClose={closeContinueReviewBottomSheet}
        />
      </BottomSheet>
    );
  };
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          bottomSheetContent={bottomSheetContent}
          header={{
            customFunctionality: {
              [NavigationBeanType.BACK]: {
                disableDefaultCall: true,
                onPress: openContinueReviewBottomSheet,
              },
            },
          }}
          children={_ => {
            return (
              <>
                <KeyboardAwareScrollView
                  bounces={false}
                  showsHorizontalScrollIndicator={false}
                  showsVerticalScrollIndicator={false}
                  extraHeight={150}
                  style={styles.container}
                  contentContainerStyle={{
                    paddingBottom: rh(insets.bottom + 40),
                  }}>
                  <ReviewProduct
                    name={productName ?? ''}
                    image={productImage}
                    style={{paddingHorizontal: 24}}
                  />
                  <JioText
                    text={config?.RateYourProduct?.title ?? ''}
                    appearance={JioTypography.BODY_S_BOLD}
                    color={'primary_grey_100'}
                    style={styles.headerTitle}
                  />
                  <SelectStarRating
                    starRating={formData[FormNameField.RATE]}
                    onStarPress={handleFormData(FormNameField.RATE)}
                    style={{paddingHorizontal: 24}}
                  />
                  <Divider
                    type={DividerType.THIN}
                    vertical={DividerGap.GAP24}
                    horizontal={DividerGap.GAP24}
                  />
                  {!mappedConfig?.title?.hidden && (
                    <View style={styles.inputContainer}>
                      <JioText
                        text={config?.WriteATitle?.title}
                        appearance={JioTypography.BODY_S_BOLD}
                        color={'primary_grey_100'}
                        style={styles.textInputLabel}
                      />
                      <TextInput
                        ref={handleInputRef(FormNameField.TITLE)}
                        style={styles.textInput}
                        value={formData.title}
                        onChangeText={text => {
                          const regex = new RegExp(
                            config?.WriteATitle?.regex,
                            'g',
                          );
                          const cleanedText = text?.replace?.(regex, '');
                          handleFormData(FormNameField.TITLE)(cleanedText);
                        }}
                        onBlur={handleFormError(
                          FormNameField.TITLE,
                          onBlurValidata(FormNameField.TITLE),
                        )}
                        placeholder={config?.WriteATitle?.placeholder}
                        placeholderTextColor={'#B5B5B5'}
                      />
                      <JioText
                        text={config?.WriteATitle?.message
                          ?.replace('[CHAR]', `${formData.title?.length ?? 0}`)
                          ?.replace('[ERROR]', formError[FormNameField.TITLE])
                          ?.replace(
                            '[MAX]',
                            mappedConfig[FormNameField.TITLE]?.maxCharLimit,
                          )}
                        style={styles.textInputCounter}
                        color={
                          formError[FormNameField.TITLE]
                            ? 'feedback_error_50'
                            : 'primary_grey_80'
                        }
                        appearance={JioTypography.BODY_XXS}
                      />
                      {config?.WriteATitle?.subText?.isVisible ? (
                        <View style={styles.textInputSubText}>
                          {config?.WriteATitle?.subText?.icon?.isVisible ? (
                            <JioIcon
                              ic={
                                config?.WriteATitle?.subText?.icon
                                  ?.ic as IconKey
                              }
                              color={IconColor.GREY80}
                              size={IconSize.MEDIUM}
                            />
                          ) : null}
                          <JioText
                            text={config?.WriteATitle?.subText?.title ?? ''}
                            appearance={JioTypography.BODY_XXS}
                            color={'primary_grey_80'}
                            style={styles.subText}
                          />
                        </View>
                      ) : null}
                    </View>
                  )}
                  {!mappedConfig?.content?.hidden && (
                    <View style={styles.inputContainer}>
                      <JioText
                        text={config?.ShareAReview?.title}
                        appearance={JioTypography.BODY_S_BOLD}
                        color={'primary_grey_100'}
                      />
                      <View>
                        <TextInput
                          ref={handleInputRef(FormNameField.CONTENT)}
                          multiline
                          style={styles.textInput}
                          value={formData.content}
                          onChangeText={text => {
                            const regex = new RegExp(
                              config?.ShareAReview?.regex as string,
                              'g',
                            );
                            const cleanedText = text?.replace?.(regex, '');
                            handleFormData(FormNameField.CONTENT)(cleanedText);
                          }}
                          onBlur={handleFormError(
                            FormNameField.CONTENT,
                            onBlurValidata(FormNameField.CONTENT),
                          )}
                          placeholder={config?.ShareAReview?.placeholder}
                          placeholderTextColor={'#B5B5B5'}
                        />
                        <SvgUri
                          uri={getSvgUrlData('TextAreaIndicator')}
                          style={styles.TextAreaIndicator}
                          stroke="#000000"
                          strokeWidth={2}
                          strokeLinecap="round"
                        />
                      </View>
                      <JioText
                        text={config?.ShareAReview?.message
                          ?.replace(
                            '[CHAR]',
                            `${formData[FormNameField.CONTENT]?.length ?? 0}`,
                          )
                          ?.replace('[ERROR]', formError[FormNameField.CONTENT])
                          ?.replace(
                            '[MAX]',
                            mappedConfig[FormNameField.CONTENT]?.maxCharLimit,
                          )}
                        style={styles.textInputCounter}
                        color={
                          formError[FormNameField.CONTENT]
                            ? 'feedback_error_50'
                            : 'primary_grey_80'
                        }
                        appearance={JioTypography.BODY_XXS}
                      />
                      {config?.ShareAReview?.subText?.isVisible ? (
                        <View style={styles.textInputSubText}>
                          {config?.ShareAReview?.subText?.icon?.isVisible ? (
                            <JioIcon
                              ic={
                                config?.ShareAReview?.subText?.icon
                                  ?.ic as IconKey
                              }
                              color={IconColor.GREY80}
                              size={IconSize.MEDIUM}
                            />
                          ) : null}
                          <JioText
                            text={config?.ShareAReview?.subText?.title ?? ''}
                            appearance={JioTypography.BODY_XXS}
                            color={'primary_grey_80'}
                            style={styles.subText}
                          />
                        </View>
                      ) : null}
                    </View>
                  )}
                  <Divider
                    type={DividerType.THIN}
                    bottom={DividerGap.GAP24}
                    horizontal={DividerGap.GAP24}
                  />

                  {!mappedConfig?.image?.hidden ? (
                    <>
                      <View style={styles.imageUploadContainer}>
                        <JioText
                          text={config?.AddPhotosBlock?.title ?? ''}
                          appearance={JioTypography.BODY_S_BOLD}
                          color={'primary_grey_100'}
                        />
                        {!mappedConfig?.image?.required ? (
                          <JioText
                            text={config?.AddPhotosBlock?.titleTag ?? ''}
                            appearance={JioTypography.BODY_XS}
                            color={'primary_grey_60'}
                          />
                        ) : null}
                      </View>
                      <JioText
                        text={config?.AddPhotosBlock?.subtitle ?? ''}
                        appearance={JioTypography.BODY_XS}
                        color={'primary_grey_80'}
                        style={{paddingTop: 4, paddingHorizontal: 24}}
                      />
                      <TouchableOpacity
                        onPress={uploadImages}
                        disabled={imageSelectionLimit <= 0}
                        style={[
                          styles.uploadImagesBtn,
                          imageSelectionLimit <= 0
                            ? {
                                opacity: 0.5,
                              }
                            : null,
                        ]}>
                        <JioIcon
                          ic={'IcUpload'}
                          color={IconColor.PRIMARY60}
                          size={IconSize.MEDIUM}
                        />
                        <JioText
                          text={config?.AddPhotosBlock?.UploadBtnTitle}
                          appearance={JioTypography.BUTTON}
                          color={'primary_60'}
                        />
                      </TouchableOpacity>
                      <JioText
                        text={
                          config?.AddPhotosBlock?.ButtonTag?.replace(
                            '[LENGTH_LIMIT]',
                            mappedConfig?.image?.countLimit,
                          ).replace(
                            '[SIZE_LIMIT]',
                            mappedConfig?.image?.sizeLimitInMegaBytes,
                          ) ?? ''
                        }
                        appearance={JioTypography.BODY_XXS}
                        color={'primary_grey_80'}
                        style={{paddingTop: 4, paddingHorizontal: 24}}
                      />
                      <ScrollView
                        contentContainerStyle={{
                          columnGap: 8,
                          paddingHorizontal: 24,
                        }}
                        scrollEnabled={images?.length + editImages?.length > 5}
                        horizontal={true}
                        bounces={false}
                        showsHorizontalScrollIndicator={false}>
                        {editImages?.map((img: any, index: number) => {
                          return (
                            <View key={index} style={styles.imageBox}>
                              <TouchableOpacity
                                style={[
                                  styles.closeIconContainer,
                                  styles.imageClose,
                                ]}
                                onPress={() => removeImage(index, true)}>
                                <JioIcon
                                  ic={'IcClose'}
                                  color={IconColor.PRIMARY60}
                                  size={IconSize.SMALL}
                                />
                              </TouchableOpacity>
                              <Image
                                style={styles.image}
                                source={{
                                  uri: img?.url,
                                }}
                              />
                            </View>
                          );
                        })}
                        {images?.map((img: any, index: number) => {
                          return (
                            <View key={index} style={styles.imageBox}>
                              <TouchableOpacity
                                style={[
                                  styles.closeIconContainer,
                                  styles.imageClose,
                                ]}
                                onPress={() => removeImage(index)}>
                                <JioIcon
                                  ic={'IcClose'}
                                  color={IconColor.PRIMARY60}
                                  size={IconSize.SMALL}
                                />
                              </TouchableOpacity>
                              <Image
                                style={styles.image}
                                source={{
                                  uri: img?.uri,
                                }}
                              />
                            </View>
                          );
                        })}
                      </ScrollView>
                      <Divider
                        type={DividerType.THIN}
                        vertical={DividerGap.GAP24}
                        horizontal={DividerGap.GAP24}
                      />
                    </>
                  ) : null}
                  <Text style={styles.FAQSection}>
                    {config?.FAQSection?.FAQPrompt + ' '}
                    <Text onPress={handleFAQLink} style={styles.FAQLink}>
                      {config?.FAQSection?.FAQSection}
                    </Text>
                  </Text>
                </KeyboardAwareScrollView>
                <View
                  style={[
                    styles.ButtonContainer,
                    {paddingBottom: insets.bottom + 8},
                  ]}>
                  <JioButton
                    title={config?.FAQSection?.submitBtnTitle ?? ''}
                    stretch={true}
                    size={ButtonSize.LARGE}
                    state={
                      loading
                        ? ButtonState.LOADING
                        : formData[FormNameField.RATE]
                        ? ButtonState.NORMAL
                        : ButtonState.DISABLED
                    }
                    onClick={onPressOfSubmit}
                    kind={ButtonKind.PRIMARY}
                  />
                </View>
              </>
            );
          }}
        />
      )}
    />
  );
};

export default React.memo(RatingAndReviewFormScreen);

const styles = StyleSheet.create({
  ProductTopContainer: {
    flexDirection: 'row',
    columnGap: rw(12),
    marginBottom: 16,
  },
  container: {
    paddingVertical: 24,
    flex: 1,
    backgroundColor: '#ffffff',
  },
  ProductImage: {
    width: rw(84),
    height: rw(84),
    borderRadius: 16,
    borderWidth: 0.8,
    borderColor: '#E0E0E0',
  },
  inputContainer: {
    marginBottom: 24,
    paddingHorizontal: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  textInput: {
    minHeight: 40,
    paddingVertical: 5,
    borderBottomWidth: 2,
    borderBottomColor: '#141414',
    fontSize: 14,
    lineHeight: 24,
    letterSpacing: -0.05,
    fontFamily: 'JioType-Bold',
    fontWeight: '500',
    ...Platform.select({
      android: {
        paddingLeft: -2,
      },
    }),
  },
  imageUploadContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 4,
    paddingHorizontal: 24,
  },
  uploadImagesBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 100,
    marginTop: 16,
    columnGap: 8,
    marginHorizontal: 24,
  },
  closeIconContainer: {
    position: 'absolute',
    top: -6,
    right: -6,
    zIndex: 5,
    width: 24,
    height: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E5F1F7',
    borderRadius: 200,
  },
  submitBtn: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 120, 173, 1)',
    borderRadius: 1000,
    paddingVertical: rh(12),
    paddingTop: 8,
  },
  ButtonContainer: {
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    backgroundColor: '#ffffff',
  },
  errorText: {},
  TextAreaIndicator: {
    position: 'absolute',
    right: 0,
    bottom: 8,
  },
  FAQSection: {
    fontFamily: 'JioType',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: -0.5,
    color: '#B5B5B5',
    paddingHorizontal: 24,
  },
  FAQLink: {
    fontFamily: 'JioType',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: -0.5,
    color: '#0C5273',
  },
  headerTitle: {paddingBottom: 4, paddingHorizontal: 24},
  textInputLabel: {paddingTop: 8},
  textInputCounter: {
    alignSelf: 'flex-end',
    paddingVertical: 2,
  },
  textInputSubText: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    columnGap: 12,
  },
  subText: {flexShrink: 1},
  imageBox: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 22,
  },
  image: {width: 64, height: 64, borderRadius: 12},
  imageClose: {overflow: 'visible'},
});
