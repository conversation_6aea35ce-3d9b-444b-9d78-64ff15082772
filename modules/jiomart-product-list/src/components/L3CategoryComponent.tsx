import React, {useRef} from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  Image,
  ListRenderItem,
  StyleSheet,
  Platform,
} from 'react-native';
import JioText from '@jio/rn_components/src/components/JioText/JioText';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import { formatCategoryName } from '../../../jiomart-common/src/utils/JMCommonFunctions';

type PropsType = {
  slug: string;
  categories: any[] | undefined;
  isL3Slug: boolean;
  onPress?: (data: any) => void;
};

const reArrangeData = (categories: any, slug: string) => {
  if (categories) {
    const data = [...categories];
    const foundObject = data.find(item => item.slug === slug);
    const index = foundObject ? data.indexOf(foundObject) : 0;

    if (foundObject) {
      const selectedItem = data.splice(index, 1)[0];
      data.unshift(selectedItem);
    }
    return data;
  }
  return [];
};

const L3CategoryComponent = (props: PropsType) => {
  const {categories, slug, isL3Slug, onPress} = props;
  const level3Data = reArrangeData(categories, slug);

  const flatListRef = useRef<FlatList | null>(null);
  const scrollToTop = () => {
    flatListRef.current?.scrollToIndex({index: 0, animated: true});
  };

  const renderItem: ListRenderItem<any> = ({item, index}) => {
    return (
      <TouchableOpacity
        key={`l-${item?.name}-${index}`}
        activeOpacity={0.85}
        onPress={() => {
          scrollToTop();
          onPress?.(item);
        }}
        style={[
          styles.l3Card,
          index !== 0 ? {marginHorizontal: rw(4)} : null,
          index === 0
            ? !isL3Slug
              ? styles.selectedCard1
              : styles.selectedCard
            : null,
          styles.cardElevated,
        ]}>
        <View>
          <Image
            source={{
              uri: item?.logo.url
                ? item?.logo.url
                : 'https://cdn.pixelbin.io/v2/jiomart-fynd/jio-np/wrkr/jmrtz0/organization/64b003218f39a39ec141e0e1/theme/assets/jiomart-default-image.959a1b7a3b2d6bbecb905441f2832a89.png',
            }}
            style={styles.image}
          />
        </View>

        <View style={{maxWidth: 100, paddingRight: 24}}>
          <JioText
            maxLines={2}
            text={formatCategoryName(item?.name)}
            ellipsizeMode="tail"
            appearance={
              index === 0
                ? !isL3Slug
                  ? JioTypography.BODY_XXS
                  : JioTypography.BODY_XXS_BOLD
                : JioTypography.BODY_XXS
            }
            color={
              index === 0
                ? !isL3Slug
                  ? 'primary_grey_80'
                  : 'black'
                : 'primary_grey_80'
            }
          />
        </View>
      </TouchableOpacity>
    );
  };

  if (level3Data?.length <= 0) {
    return null;
  }

  return (
    <FlatList
      initialNumToRender={4}
      data={level3Data}
      renderItem={renderItem}
      ref={flatListRef}
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: rh(12),
    paddingTop: 2,
    paddingRight: 24,
  },

  selectedCard: {
    borderWidth: 1,
    borderColor: '#0078AD',
    marginLeft: 24,
  },
  selectedCard1: {
    // borderWidth: 1,
    // borderColor: '#0078AD',
    marginLeft: rw(24),
  },
  l3Card: {
    flexDirection: 'row',
    borderRadius: 12,
    paddingVertical: rh(4),
    paddingLeft: rh(4),
    paddingRight: rh(8),
    columnGap: rw(4),
    alignItems: 'center',
    marginHorizontal: 4,
    width: rw(125),
    height: rh(50),
    // borderWidth: 1,
  },
  cardElevated: {
    backgroundColor: '#ffff',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.14,
        shadowRadius: 6,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  image: {
    width: rw(40),
    height: rw(40),
    resizeMode: 'contain',
    borderRadius: 8,
  },
  content: {
    flexDirection: 'row',
    columnGap: rw(8),
  },
});

export default L3CategoryComponent;
