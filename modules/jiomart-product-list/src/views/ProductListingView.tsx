import {
  ActivityIndicator,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useRef} from 'react';
import PlpListShimmer from '../components/shimmer/PlpShimmer';
import FilterAndSortBar from '../components/FilterAndSortBar';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import JMFab from '../components/JMFab';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import Divider, {
  DividerGap,
  DividerType,
} from '../../../jiomart-general/src/ui/Divider';
import ProductCard from '../../../jiomart-general/src/ui/ProductCard';
import {checkValueIsExistInArray} from '../../../jiomart-common/src/utils/JMArrayUtility';
import {FlashList} from '@shopify/flash-list';
import L3CategoryComponent from '../components/L3CategoryComponent';
import CategoryChips from '../components/CategoryChips';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import type {ProductListingViewProps} from '../types/ProductListingType';

const ProductListingView = (props: ProductListingViewProps) => {
  const {
    loading,
    config,
    filterAndSortBar,
    data,
    l3CategoryData,
    l4CategoryData,
    selectedL3Category,
    department,
    slug,
    hasNextPageDataIsLoading,
    multiSearchQuery,
    onl3CategoryPress,
    onl4CategoryPress,
    onPressMultiSearchQuery,
    onMultiVariantPress,
    onProductCardPress,
    onLoadMore,
    onListRef,
  } = props;
  const listRef = useRef<any>(null);
  const scrollYd = useSharedValue(0);
  const fabAnimatedStyle = useAnimatedStyle(() => {
    const outputRange = scrollYd.value > 400 ? 1 : 0;
    const opacity = outputRange;
    return {opacity};
  });

  const scrollToTop = () => {
    listRef.current?.scrollToOffset({offset: 0, animated: true});
  };

  const handleScroll = (e: any) => {
    if (
      e.nativeEvent.layoutMeasurement.height + e.nativeEvent.contentOffset.y <=
      e.nativeEvent.contentSize.height - 80
    ) {
      scrollYd.value = e.nativeEvent.contentOffset.y;
    }
  };

  const renderItem = ({item}: any) => {
    try {
      // if (item?.adSpotID) {
      //   return (
      //     <JioAds
      //       key={item?.adSpotID}
      //       adSpotId={item?.adSpotID}
      //       adHeight={item?.adHeight}
      //       adWidth={item?.adWidth}
      //       adType={item?.adType}
      //       adjHeight={item?.adjHeight}
      //       adjWidth={Math.floor(getScreenDim()?.width - 48)}
      //       style={styles.ads}
      //       adMetaData={{
      //         l1: '' + slug,
      //         l2: '',
      //         l3: '' + slug,
      //         search_query: '',
      //       }}
      //     />
      //   );
      // }
      var offer = [];
      if (
        config?.card?.offer?.exchange?.isVisible &&
        item.attributes?.['trade-in']
      ) {
        offer.push(config?.card?.offer?.exch);
      }
      if (
        config?.card?.offer?.buyMoreSaveMore?.isVisible &&
        item.attributes?.['payment-tag']
      ) {
        offer.push({
          ...config?.card?.offer?.buyMoreSaveMore,
          title: config?.card?.offer?.buyMoreSaveMore?.title?.replace(
            '[TITLE]',
            item.attributes?.['payment-tag']?.split(',')?.[0],
          ),
        });
      }
      return (
        <View
          style={{
            paddingHorizontal: rw(24),
          }}>
          <ProductCard
            uid={item?.uid ?? ''}
            slug={item?.slug ?? ''}
            imageUrl={item.medias?.[0]?.url}
            disableWishlist={
              checkValueIsExistInArray(
                item?.attributes?.['vertical-code'],
                config?.wishlistVerticalCode,
              ) || !config?.wishlist?.isVisible
            }
            disableAddToCart={
              (checkValueIsExistInArray(
                item?.attributes?.['vertical-code'],
                config?.addToCartVerticalCode,
              ) &&
                item.sellable) ||
              !config?.addToCart?.isVisible
            }
            shouldShowVeg={item.attributes?.['food-type']}
            shouldShowSmartBazzar={
              item.seller_id === config?.smartBazzarToShowSellerId &&
              checkValueIsExistInArray(
                item?.attributes?.['vertical-code'],
                config?.smartBazzarToShowVerticalCode,
              )
            }
            vegIcon={config?.card?.vegIcon}
            smartBazzarImage={config?.smartBazzarImage}
            tag={item?.attributes?.tag}
            tagBackgroundColor={config?.card?.tag?.backgroundColor}
            tagColor={config?.card?.tag?.color}
            disableTag={config?.card?.tag?.isVisible}
            title={item?.name ?? ''}
            titleColor={config?.card?.title?.color}
            titleMaxLine={config?.card?.title?.maxLine}
            effectivePrice={item.price.effective.max}
            effectivePriceColor={config?.card?.effectivePriceColor}
            markedPrice={item.price.marked.max}
            markedPriceColor={config?.card?.markedPriceColor}
            discount={item.discount}
            discountBackgroundColor={config?.card?.discount?.backgroundColor}
            discountColor={config?.card?.discount?.color}
            shouldShowMultiVariant={item?.variants?.[0]?.items?.length > 0}
            size={item?.sizes[0] ?? 'Demo'}
            totalMultiVariant={config?.card?.multiVariant?.total?.replace(
              '[TOTAL]',
              item?.variants?.[0]?.total,
            )}
            multiVariantColor={config?.card?.multiVariant?.title?.color}
            multiVariantIcon={config?.card?.multiVariant?.icon}
            totalMultiVariantColor={config?.card?.multiVariant?.totalColor}
            onMultiVariantPress={() => {
              onMultiVariantPress?.(item?.variants);
            }}
            addToCartTitle={config?.addToCart?.title}
            verticalCode={item?.attributes?.['vertical-code']}
            shouldShowOutofStock={!item.sellable}
            outOfStockText={config?.card?.outOfStock?.text}
            outOfStockColor={config?.card?.outOfStock?.color}
            offers={offer}
            onPress={() => {
              onProductCardPress?.(item);
            }}
          />
          <Divider
            type={DividerType.THIN}
            top={DividerGap.GAP12}
            bottom={DividerGap.GAP16}
          />
        </View>
      );
    } catch (error) {
      return null;
    }
  };
  const renderListHeaderComponent = () => {
    // const TopAds = config?.jioAds?.topAds?.map((item, index) => {
    //   return item?.isVisible ? (
    //     <JioAds
    //       key={`${item?.adSpotID}-${index}`}
    //       adSpotId={item?.adSpotID}
    //       adHeight={item?.adHeight}
    //       adWidth={item?.adWidth}
    //       adType={item?.adType}
    //       adjHeight={item?.adjHeight}
    //       adjWidth={
    //         Platform.OS === 'ios'
    //           ? Math.floor(getScreenDim.width - 48)
    //           : item?.adjWidth
    //       }
    //       style={styles.ads}
    //     />
    //   ) : null;
    // });
    return (
      <>
        {/* {TopAds} */}
        {!config?.disableCategoryList &&
        checkValueIsExistInArray(
          department,
          config?.categoryListDepartment ?? [],
        ) &&
        l3CategoryData ? (
          <L3CategoryComponent
            slug={slug}
            categories={l3CategoryData}
            isL3Slug={!!selectedL3Category}
            onPress={onl3CategoryPress}
          />
        ) : null}
        {!config?.disableL4CategoryList && slug ? (
          <CategoryChips
            categories={l4CategoryData || []}
            onCategorySelect={category => {
              onl4CategoryPress?.(category);
            }}
          />
        ) : null}
        {!config?.disableMultiSearchList &&
        multiSearchQuery &&
        multiSearchQuery?.length > 1 ? (
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            style={{marginHorizontal: rw(24)}}>
            <View style={styles.multiSearchBadge}>
              {multiSearchQuery?.map(
                (multiSearchKeyword: string, index: number) => {
                  return (
                    <TouchableOpacity
                      key={'multiSearch' + index.toString()}
                      style={[
                        styles.multiSearch,
                        index === 0 ? styles.selectedMultiSearch : null,
                      ]}
                      disabled={index === 0}
                      onPress={() => {
                        onPressMultiSearchQuery?.(multiSearchKeyword);
                      }}>
                      <JioText
                        text={multiSearchKeyword.trim()}
                        appearance={JioTypography.BODY_XS}
                        color={index === 0 ? 'white' : 'primary_60'}
                      />
                    </TouchableOpacity>
                  );
                },
              )}
            </View>
          </ScrollView>
        ) : null}
      </>
    );
  };

  if (loading) {
    return <PlpListShimmer style={{paddingTop: 66}} />;
  }
  return (
    <View style={styles.container}>
      <FilterAndSortBar {...filterAndSortBar} />
      <View style={styles.wrapper}>
        <FlashList
          bounces={false}
          ref={ref => {
            onListRef?.(ref);
            listRef.current = ref;
          }}
          data={data || []}
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
          estimatedItemSize={130}
          contentContainerStyle={styles.listContainer}
          onScroll={handleScroll}
          onEndReached={onLoadMore}
          onEndReachedThreshold={0.35}
          ListFooterComponent={
            hasNextPageDataIsLoading ? (
              <View style={styles.footer}>
                <ActivityIndicator size="large" color="#0078AD" />
              </View>
            ) : null
          }
          ListHeaderComponent={renderListHeaderComponent}
          estimatedListSize={{height: 865, width: 392}}
        />
      </View>
      {!config?.disableFabIcon && (
        <Animated.View style={fabAnimatedStyle}>
          <JMFab onPress={scrollToTop} style={styles.fab} />
        </Animated.View>
      )}
    </View>
  );
};

export default ProductListingView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  wrapper: {flex: 1},
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingTop: 60,
    paddingBottom: 143,
  },
  footer: {
    padding: 16,
    alignItems: 'center',
  },
  ads: {
    alignItems: Platform.OS === 'ios' ? 'center' : 'baseline',
    borderBottomWidth: 1,
    paddingBottom: 12,
    marginBottom: 16,
    borderColor: '#E0E0E0',
    marginHorizontal: rw(24),
  },
  firstColumn: {
    // marginLeft: rw(24),
    marginRight: rw(8),
  },
  secondColumn: {
    marginLeft: rw(8),
    // marginRight: rw(24),
  },
  multiSearch: {
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 50,
    borderColor: '#E0E0E0',
  },
  selectedMultiSearch: {
    backgroundColor: '#0078AD',
    borderWidth: 0,
  },
  multiSearchBadge: {flexDirection: 'row', gap: 8, marginBottom: rh(24)},
  fab: {
    bottom: 97,
  },
});
