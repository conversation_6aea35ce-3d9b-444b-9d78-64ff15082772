import React from 'react';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import useNotificationScreenController from '../controllers/useNotificationScreenController';
import {Pressable, ScrollView, Text, View} from 'react-native';
import { rh, rw } from '../../../jiomart-common/src/JMResponsive';
import { RedDot } from '../assets';
import { JioText } from '@jio/rn_components';
import { JioTypography } from '@jio/rn_components/src/index.types';

const NotificationScreen = (props: any) => {
  const {navigation, navigationBean, notificationData, onPressOfNotification} = useNotificationScreenController(props);
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={(bean) => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          children={_ => {
            return notificationData && notificationData?.length > 0 ? (
              <ScrollView showsVerticalScrollIndicator={false}>
                {notificationData?.map((notification, index) => {
                  return (
                    <Pressable
                      key={index}
                      onPress={() =>
                        onPressOfNotification(
                          notification?.messageId,
                          notification?.action,
                        )
                      }
                      style={{
                        backgroundColor: notification?.backgroundColor,
                        marginBottom: rh(8),
                        paddingVertical: rh(24),
                        paddingHorizontal: rw(24),
                      }}>
                      {!notification?.isRead && (
                        <View
                          style={{
                            position: 'absolute',
                            backgroundColor: 'red',
                            width: 8,
                            height: 8,
                            borderRadius: 5,
                            right: rw(24),
                            top: rh(30),
                          }}
                        />
                      )}
                      <JioText
                        style={{
                          color: notification?.title?.color,
                        }}
                        text={notification?.title?.text}
                        appearance={JioTypography.BODY_XS_BOLD}
                      />
                      <JioText
                        style={{
                          color: notification?.description?.color,
                        }}
                        text={notification?.description?.text}
                        appearance={JioTypography.BODY_S}
                      />
                    </Pressable>
                  );
                })}
              </ScrollView>
            ) : null;
          }}
        />
      )}
    />
  );
};

export default NotificationScreen;
