import React, {useState, useEffect} from 'react';
import {View, TouchableOpacity, StyleSheet, Dimensions, Text} from 'react-native';

import {JioText, JioIcon} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import MicroTap from '../../../jiomart-general/src/ui/Animation/Micro/MicroTap';
import CleverTap from 'clevertap-react-native';


interface AccountAccordianItemProps {
  id: string;
  title: string;
  image: any;
  isNotification: boolean;
  onPress?: () => void;
}

interface AccountAccordianViewProps {
  item: {
    id: string;
    title: string;
    items: [AccountAccordianItemProps];
  };
  onPress: any;
  notificationCount?: number;
}

const AccountAccordianView: React.FC<AccountAccordianViewProps> = ({
  item,
  onPress,
  notificationCount
}) => {
  const [expanded, setExpanded] = useState(true);

  const toggleAccordion = () => {
    setExpanded(!expanded);
  };



  useEffect(() => {
    if (item?.isAccordianClose) {
      setExpanded(false);
    }
  }, [item?.isAccordianClose]);

  return item?.is_visible ? (
    <View style={styles.container}>
      <TouchableOpacity
        activeOpacity={1}
        style={styles.header}
        onPress={toggleAccordion}>
        <JioText
          style={styles.headerText}
          text={item?.title}
          appearance={JioTypography.BODY_S_BOLD}></JioText>
        {expanded ? (
          <MicroTap color={'#E5F1F7'} borderRadius={100} padding={0}>
            <JioIcon ic={'IcChevronUp'} size={IconSize.MEDIUM} />
          </MicroTap>
        ) : (
          <MicroTap color={'#E5F1F7'} borderRadius={100} padding={0}>
            <JioIcon ic={'IcChevronDown'} size={IconSize.MEDIUM} />
          </MicroTap>
        )}
      </TouchableOpacity>
      {expanded ? (
        <View>
          {item?.items?.map((subItem, index) =>
            subItem?.isVisible ? (
              <TouchableOpacity
                key={subItem.id || index}
                activeOpacity={1}
                style={styles.itemContainer}
                onPress={() => {
                  const option = subItem.title.toLowerCase().replace(/\s+/g, '_');
                  // logAnalyticsEventWithGAModel({
                  //   action: `${subItem.title.toLowerCase()} clicked`,
                  //   label: `my_account_${option}_clicked`,
                  //   category: MyAccountScreen.MyAccountCategory,
                  //   eventName: EventName.EVENT_MY_ACCOUNT,
                  // });
                  onPress?.(subItem);
                }}>
                <View style={styles.item}>
                  <View style={styles.image}>
                    <JioIcon ic={subItem?.iconName} color={IconColor.PRIMARY} />
                    {(subItem?.cta?.destination == 'JMNotificationScreen') &&
                      notificationCount ? <View style={styles.redDot} /> : null}
                  </View>
                  <JioText
                    style={styles.itemText}
                    text={
                      subItem?.cta?.destination == 'JMNotificationScreen'
                        ? subItem?.title?.replace(
                            '[NOTIFY_COUNT]',
                            `${
                              notificationCount ?? 0 > 0
                                ? `(${notificationCount})`
                                : ''
                            }`,
                          )
                        : subItem?.title
                    }
                    appearance={JioTypography.BODY_S}
                    color="secondary_grey_80"
                  />
                </View>
                {index !== item?.items?.length - 1 && (
                  <View style={styles.borderBottom}></View>
                )}
              </TouchableOpacity>
            ) : null,
          )}
        </View>
      ) : null}
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    height: 53,
  },
  headerText: {
    marginRight: 16,
  },
  arrowIcon: {
    width: 20,
    height: 20,
    tintColor: 'black',
  },
  itemContainer: {
    flexDirection: 'column',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    height: 63,
  },
  itemText: {
    marginRight: 16,
  },
  image: {
    width: 25,
    height: 25,
    marginRight: 10,
    marginLeft: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    width: Dimensions.get('window').width - 32,
    marginLeft: 16,
    marginRight: 16,
    alignSelf: 'center',
  },
  redDot: {
    position: 'absolute',
    backgroundColor: 'red',
    width: 8,
    height: 8,
    borderRadius: 5,
    top: 0,
    left: 16,
  },
});

export default AccountAccordianView;
