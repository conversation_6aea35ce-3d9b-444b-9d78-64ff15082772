import {Jio<PERSON><PERSON>, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconKind,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import React from 'react';
import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';

interface ProfileViewProps {
  item: {
    id: string;
    name: string;
    email: string;
    phoneNumber: string;
    userData: undefined;
    onPress?: () => void;
  };
  userData: any;
  onPress?: any;
}

const ProfileView: React.FC<ProfileViewProps> = ({item, userData, onPress}) => {
  const userName = userData?.first_name + ' ' + userData?.last_name;
  const email = userData?.emails?.[0]?.email;
  const phoneNumber = userData?.phone_numbers?.[0]?.phone;

  return (
    <View>
      <TouchableOpacity
        activeOpacity={1}
        style={[styles.profileContainer, {columnGap: 16}]}
        onPress={async () => {
          onPress?.(item);
        }}>
        <JioIcon
          ic={'IcProfile'}
          size={IconSize.XXL}
          kind={IconKind.BACKGROUND}
        />
        <View style={styles.textContainer}>
          {userName && (
            <JioText
              style={styles.name}
              appearance={JioTypography.BODY_M_BOLD}
              text={userName}
            />
          )}
          {email && (
            <JioText
              style={styles.name}
              appearance={JioTypography.BODY_XS}
              text={email}
              maxLines={2}
            />
          )}
          {phoneNumber && (
            <JioText
              style={styles.name}
              appearance={JioTypography.BODY_XS}
              text={`+91 ${phoneNumber}`}
            />
          )}
        </View>
        <JioIcon ic={'IcChevronRight'} color={IconColor.PRIMARY60} />
      </TouchableOpacity>
      <View style={styles.borderBottom} />
    </View>
  );
};

const styles = StyleSheet.create({
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    height: 118,
  },
  container: {
    flexDirection: 'column',
    padding: 10,
    height: 118,
    columnGap: 16,
  },
  profileImage: {
    marginRight: rw(16),
  },
  textContainer: {
    flex: 1,
  },
  name: {
    // marginRight: 16,
    marginBottom: 5,
  },
  email: {
    fontSize: 14,
    // marginRight: 16,
    fontWeight: '500',
    color: '#000000A6',
    fontFamily: 'JioType-Medium',
    marginBottom: 5,
  },
  phoneNumber: {
    fontSize: 14,
    // marginRight: 16,
    fontWeight: '500',
    color: '#000000A6',
    fontFamily: 'JioType-Medium',
    marginBottom: 5,
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    width: Dimensions.get('window').width - rw(24),
    marginLeft: 16,
    marginRight: 16,
    alignSelf: 'center',
  },
});
export default ProfileView;
