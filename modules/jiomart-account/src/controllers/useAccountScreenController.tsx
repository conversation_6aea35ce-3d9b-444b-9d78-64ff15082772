import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useEffect, useState} from 'react';
import {BackHandler, NativeModules} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {JMAccountScreenProps} from '../types/JMAccountScreenType';
import JMUserApiNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {
  ActionType,
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import JMWebGlobalInfo from '../../../jiomart-webmanager/src/WebGlobalInfo';
import {useDispatch} from 'react-redux';
import {resetCart} from '../../../jiomart-cart/src/slices/cartSlice';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {HeaderType} from '../../../jiomart-common/src/JMScreenSlot.types';
import {resetWishlist} from '../../../jiomart-wishlist/src/slices/wishlistSlice';
import { JMStorageService } from '../../../jiomart-networkmanager/src/api/utils/JMStorageService';

const {JMRNNavigatorModule} = NativeModules;
const userApiNetworkController = new JMUserApiNetworkController();

const useAccountScreenController = (props: JMAccountScreenProps) => {
  const {setUserInitials, notificationCount} = useGlobalState();
  const dispatch = useDispatch();
  const {navigation} = props;

  //   const [allCategoryDataResponse, setAllCategoryDataResponse] = useState<
  //     CustomCategoryItems[] | null | undefined
  //   >(null);

  const myProfileConfig = useConfigFile(
    JMConfigFileName.JMProfileConfigurationFileName,
  );

  const [userDataResponse, setUserData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserDetials();
    const backAction = () => {
      if (navigation.canGoBack()) {
        navigation.goBack();
        return true;
      }
      return false;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => {
      backHandler.remove();
    };
  }, []);

  const fetchUserDetials = async () => {
    const storedData = await getPrefString(AsyncStorageKeys.PROFILE_DETAILS);
    console.log('storedData', storedData);
    if (storedData) {
      setUserData(JSON.parse(storedData));
      setLoading(false);
      return;
    }
    const userDetails = await userApiNetworkController.fetchUserDetails();
    if (userDetails) {
      setUserData(userDetails);
      setLoading(false);
    }
  };
  const clearOnLogout = () => {
    JMStorageService.clearAllKeys()
    setUserInitials('');
    dispatch(resetCart());
    dispatch(resetWishlist());
    userApiNetworkController.fetchGuestUserSession();
  }
  const signOutUser = async () => {
    try {
      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
        await userApiNetworkController.logoutUser();
      }
      clearOnLogout()
      if (navigation && navigation.canGoBack!!) {
        navigateTo(
          navBeanObj({
            actionType: ActionType.OPEN_WEB_URL,
            destination: AppScreens.HOME_SCREEN,
            headerVisibility: HeaderType.CUSTOM,
            navigationType: NavigationType.RESET,
            loginRequired: false,
            actionUrl: getBaseURL(),
            shouldShowBottomNavBar: false,
            shouldShowDeliverToBar: true,
            headerType: 5,
          }),
          navigation,
        );
      }
    } catch (error) {}
  };
  var statusBarColor = '#0078ac';
  const insets = useSafeAreaInsets();
  const statusBarHeight = insets.top;

  function redirectTo(item: any) {
    console.log('item', item);
    if (item?.deeplink?.DeeplinkIdentifier === 'sign_out') {
      signOutUser();
      return;
    }

    navigateTo(
      navBeanObj({
        ...item?.cta,
        actionUrl: `${getBaseURL()}/${item?.cta?.actionUrl}`,
      }),
      navigation,
    );

    // const currentScreen =
    //   navigation.getState()?.routes?.[navigation.getState()?.index]?.name;
    // if (currentScreen === item?.rnActionScrren) {
    //   return;
    // }

    // if (item?.openInRN) {
    //   navigation.push(item?.rnActionScrren, {
    //     webViewUrl: item?.webUrl,
    //     ...item?.header,
    //     ...item?.params,
    //   });
    //   return;
    // }
  }

  return {
    statusBarHeight,
    statusBarColor,
    shouldShowDeliverToBar,
    userDataResponse,
    JMRNNavigatorModule,
    myProfileConfig,
    loading,
    insets,
    ...props,
    navigationBean: props.route.params,
    redirectTo,
    notificationCount,
  };
};

// const fetchAllCategoryApi = async setAllCategoryDataResponse => {
//   try {
//     const data = await getAllCategoryApi();
//     if (data && data.length > 0) {
//       setAllCategoryDataResponse(data);
//     } else {
//       setAllCategoryDataResponse(undefined);
//     }
//   } catch (error) {
//     // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
//     setAllCategoryDataResponse(undefined);
//   }
// };

const shouldShowDeliverToBar = false;

export default useAccountScreenController;
