import {getCurrentEnvironment, JMEnvironment} from '../JMEnvironmentConfig';

export const enum JMBaseUrlKeys {
  HELIOS = 'HELIOS',
  AWS_STORAGE = 'AWS_STORAGE',
}

export const enum JMConfigNames {
  CONFIG_FILES = 'config_files',
  IMAGES = 'images',
}

const environmentConfig = {
  [JMEnvironment.SIT]: {
    [JMBaseUrlKeys.AWS_STORAGE]: 'https://myjiostatic.cdn.jio.com',
  },
  [JMEnvironment.PROD]: {
    [JMBaseUrlKeys.AWS_STORAGE]: 'https://myjiostatic.cdn.jio.com',
  },
  [JMEnvironment.UAT]: {
    [JMBaseUrlKeys.AWS_STORAGE]: 'https://myjiostatic.cdn.jio.com',
  },
};
const awsConfigPath = {
  [JMEnvironment.SIT]: {
    [JMConfigNames.CONFIG_FILES]: 'JioMart/RN_files/SIT',
    [JMConfigNames.IMAGES]: '',
  },
  [JMEnvironment.PROD]: {
    [JMConfigNames.CONFIG_FILES]: 'JioMart/RN_files/PROD',
    [JMConfigNames.IMAGES]: '',
  },
  [JMEnvironment.UAT]: {
    [JMConfigNames.CONFIG_FILES]: 'JioMart/RN_files/JcpUat',
    [JMConfigNames.IMAGES]: '',
  },
};

const currentEnvironment = getCurrentEnvironment();

export const getHeliosCurrentEnv = () => {
  return currentEnvironment;
};

export const getAwsConfigPath = () => {
  return awsConfigPath[currentEnvironment];
};

export const getJioMartEnvironmentConfig = () => {
  return environmentConfig[currentEnvironment];
};
