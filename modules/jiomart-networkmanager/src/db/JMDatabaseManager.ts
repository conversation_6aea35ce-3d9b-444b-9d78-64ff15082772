import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {
  addStringPref,
  getPrefString,
  removeStringPref,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';

class JMAddressManager {
  static setIntialAddress = (address: JMAddressModel) => {
    getPrefString(AsyncStorageKeys.X_LOCATION_DETAIL)
      .then(val => {
        if (!val) {
          addStringPref(
            AsyncStorageKeys.X_LOCATION_DETAIL,
            JSON.stringify(val),
          );
        }
      })
      .catch(() => {
        addStringPref(
          AsyncStorageKeys.X_LOCATION_DETAIL,
          JSON.stringify(address),
        );
      });
  };

  static updateDBAddress = async (val: JMAddressModel) => {
    try {
      const existingData = await this.getDefaultAddress();
      const parsedExisting = existingData ? JSON.parse(existingData) : {};

      const updatedData = {...parsedExisting, ...val};

      await addStringPref(
        AsyncStorageKeys.X_LOCATION_DETAIL,
        JSON.stringify(updatedData),
      );
    } catch (error) {
      throw error;
    }
  };

  static getDefaultAddress = async () => {
    try {
      return await getPrefString(AsyncStorageKeys.X_LOCATION_DETAIL);
    } catch (error) {
      throw error;
    }
  };
  static getDBAddressList = async () => {
    try {
      return await getPrefString(AsyncStorageKeys.ADDRESS_LIST_DETAIL);
    } catch (error) {
      throw error;
    }
  };
  static updateAddressListInDB = async (val: JMAddressModel[]) => {
    await addStringPref(
      AsyncStorageKeys.ADDRESS_LIST_DETAIL,
      JSON.stringify(val),
    );
  };
}

class JMUserManager {

  static isUserLoggedInFlag = (): boolean => {
      const loggedInUser = JMSharedViewModel.Instance.loggedInStatus;
      JMLogger.log("JMUserManager isUserLoggedInFlag ", "isUserLoggedInFlag "+loggedInUser)
      return loggedInUser;
  };

  static isUserLoggedIn = async () => {
    try {
      const userDetails = await this.getUserDetails();
      if (isNullOrUndefinedOrEmpty(userDetails)) {
        return false;
      }
      return true;
    } catch {
      return false;
    }
  };

  static getUserDetails = async() => {
    const userDetails = await getPrefString(AsyncStorageKeys.PROFILE_DETAILS);
    if (isNullOrUndefinedOrEmpty(userDetails)) {
      return null;
    }
    JMLogger.log("JMUserManager getUserDetails ", "getUserDetails "+JSON.stringify(userDetails))
    JMSharedViewModel.Instance.setLoggedInStatus(true);
    return userDetails;
  };

  static saveUserDetails = async (userDetails: string) => {
    if(!isNullOrUndefinedOrEmpty(userDetails)){
      await addStringPref(AsyncStorageKeys.PROFILE_DETAILS, userDetails);
      JMLogger.log("JMUserManager saveUserDetails ", "saveUserDetails true")
      JMSharedViewModel.Instance.setLoggedInStatus(true);
    }
  };
  
  static getUserSession = async() => {
    const userSession = await getPrefString(AsyncStorageKeys.USER_SESSION);
    if (isNullOrUndefinedOrEmpty(userSession)) {
      return null;
    }
    return userSession;
  };

  static saveUserSession = async (userSession: string) => {
    if(!isNullOrUndefinedOrEmpty(userSession)){
      await addStringPref(AsyncStorageKeys.USER_SESSION, userSession);
      JMLogger.log("JMUserManager saveUserSession ", "saveUserSession true")
      JMSharedViewModel.Instance.setLoggedInStatus(true);
    }
  };
  
  static getGuestUserSession = async () => {
    const guestUserSession = await getPrefString(AsyncStorageKeys.GUEST_USER_SESSION);
    if (isNullOrUndefinedOrEmpty(guestUserSession)) {
      return null;
    }
    return guestUserSession;
  };

  static saveGuestUserSession = async (userDetails: string) => {
    await addStringPref(AsyncStorageKeys.GUEST_USER_SESSION, userDetails);
  };
  
  
  static deleteUserDetails = () => {
    removeStringPref(AsyncStorageKeys.USER_DETAILS);
  };
  
  static deleteUserSession = () => {
    removeStringPref(AsyncStorageKeys.USER_SESSION);
  };
  
  static deleteGuestUserSession = () => {
    removeStringPref(AsyncStorageKeys.GUEST_USER_SESSION);
  };
  
  static deleteProfileDetails = () => {
    removeStringPref(AsyncStorageKeys.PROFILE_DETAILS)
  }
}

class JMCartManager {
  static updateDBCart = async (val: any) => {
    try {
      const existingData = await this.readDBCart();
      const parsedExisting = existingData ? JSON.parse(existingData) : {};

      const updatedData = {...parsedExisting, ...val};

      await addStringPref(
        AsyncStorageKeys.CART_DATA,
        JSON.stringify(updatedData),
      );
    } catch (error) {
      throw error;
    }
  };

  static readDBCart = async () => {
    try {
      let data = await getPrefString(AsyncStorageKeys.CART_DATA);
      return data ? JSON.parse(data || '') : null;
    } catch (error) {}
  };

  static deleteDBCart = async () => {
    try {
      await removeStringPref(AsyncStorageKeys.CART_DATA);
    } catch (error) {}
  };
}

export class JMDatabaseManager {
  static address = JMAddressManager;
  static user = JMUserManager;
  static cart = JMCartManager;
}
