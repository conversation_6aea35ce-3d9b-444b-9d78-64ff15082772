import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';

class JMAddressMapper {
  static mapToUnifiedAddresses = (data: any): JMAddressModel[] => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return (
          data?.address?.map((item: any) => ({
            id: item?.id || item?.address_id,
            name: item?.name,
            phone: item?.phone,
            address_type: item?.address_type,
            address: item?.address,
            flat_or_house_no: item?._custom_json?.flat_or_house_no || '',
            floor_no: item?._custom_json?.floor_no || '',
            tower_no: item?._custom_json?.tower_no || '',
            area: item?.area,
            landmark: item?.landmark,
            city: item?.city,
            state: item?.state,
            pin: item?.area_code,
            lat: item?.geo_location?.latitude,
            lon: item?.geo_location?.longitude,
            input_mode: item?._custom_json?.input_mode || '',
            is_default_address: item?.is_default_address || false,
            created_time: null,
            updated_time: null,
          })) || []
        );
      case AppSourceType.JM_BAU:
        return (
          data?.result?.address_list?.map((item: any) => ({
            id: item?.id,
            name: item?.addressee_name,
            phone: item?.mobile_no,
            address_type:
              item?.address_type === 'other'
                ? item?.address_type_other
                : item?.address_type,
            address: item?.building_address,
            flat_or_house_no: item?.flat_or_house_no || '',
            floor_no: item?.floor_no || '',
            tower_no: item?.tower_no || '',
            area: item?.area_name,
            landmark: item?.area_name,
            city: item?.city,
            state: item?.state,
            pin: item?.pin,
            lat: item?.lat,
            lon: item?.lon,
            input_mode: item?.input_mode || '',
            is_default_address: false, //item?.is_active,
            created_time: item?.created_time,
            updated_time: item?.updated_time,
          })) || []
        );
      default:
        return (
          data?.result?.address_list?.map((item: any) => ({
            id: item?.id,
            name: item?.addressee_name,
            phone: item?.mobile_no,
            address_type:
              item?.address_type === 'other'
                ? item?.address_type_other
                : item?.address_type,
            address: item?.building_address,
            flat_or_house_no: item?.flat_or_house_no || '',
            floor_no: item?.floor_no || '',
            tower_no: item?.tower_no || '',
            area: item?.area_name,
            landmark: item?.area_name,
            city: item?.city,
            state: item?.state,
            pin: item?.pin,
            lat: item?.lat,
            lon: item?.lon,
            input_mode: item?.input_mode || '',
            is_default_address: false, //item?.is_active,
            created_time: item?.created_time,
            updated_time: item?.updated_time,
          })) || []
        );
    }
  };
  static mapToUnifiedPincodeCity = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        return data;
      default:
        return data;
    }
  };
  static mapToUnifiedDefaultAddress = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        return data;
      default:
        return data;
    }
  };
  static mapToUnifiedRemoveAddress = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        return data;
      default:
        return data;
    }
  };
  static mapToUnifiedInsertAddress = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        return data;
      default:
        return data;
    }
  };
  static mapToUnifiedEditAddress = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        return data;
      default:
        return data;
    }
  };
}

export default JMAddressMapper;
