import {sign} from '@gofynd/fp-signature';
import {ApiConstant, getPath, getURL} from './APIConstant';
import fetchToCurl from './APICurl';
import JMRequestHelper from '../api/helpers/JMRequestHelper';

export const createAPIRequest = (
  host: string,
  jsonHeaderItems: any,
  endpoint: string,
  method: string,
  queryParam: any,
  body: any,
) => {
  const path = getPath(endpoint, method, queryParam);
  console.log('The path is:' + path);
  const hostName = host !== '' ? removeProtocol(host) : ApiConstant.host;
  const requestToSign = {
    method: method,
    host: hostName,
    path: path,
    headers: jsonHeaderItems,
    body: body ? JSON.stringify(body) : null,
  };
  const headers = getSignedHeaders(requestToSign, jsonHeaderItems);

  const requestOptions = {
    method: method,
    headers: headers,
    body: body ? JSON.stringify(body) : null,
  };

  return requestOptions;
};

function removeProtocol(url: string) {
  if (url.startsWith('https://')) {
    return url.slice(8);
  } else if (url.startsWith('http://')) {
    return url.slice(7);
  } else {
    return url;
  }
}

async function apiCall(
  host: string = '',
  endpoint: string,
  method: string,
  queryParams: any,
  body: any,
  isUserIdRequired: boolean = false,
  header: any,
  formData?: any,
): Promise<any> {
  try {
    let jsonHeaderItems; //  await JSON.parse(headerItems);
    const obj = new JMRequestHelper({});
    jsonHeaderItems = await obj.getHeaders();

    console.log('The header is:' + JSON.stringify(jsonHeaderItems));

    const request = createAPIRequest(
      host,
      formData ? {'Content-Type': 'multipart/form-data'} : jsonHeaderItems,
      endpoint,
      method,
      queryParams,
      body ?? formData,
    );

    const apiPath = getURL(host, endpoint, method, queryParams);
    console.log(fetchToCurl(apiPath, {...request, body: body}));
    return fetch(apiPath, request)
      .then(response => {
        return response.json();
      })
      .then(json => {
        return json;
      })
      .catch(error => {
        throw error;
      });
  } catch (e) {
    console.log('Exception : ' + e);
    throw e;
  }
}

function fetchDataFromAPI(
  endpoint: string,
  method: string,
  queryParams: any = null,
  body: any = null,
  isUserIdRequired: boolean = false,
  host: string = '',
  header?: any,
  formData?: any,
) {
  return new Promise((resolve, reject) => {
    apiCall(
      host,
      endpoint,
      method,
      queryParams,
      body,
      isUserIdRequired,
      header,
      formData,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        reject(error);
      });
  });
}

function getSignedHeaders(requestToSign: any, jsonHeaderItems: any) {
  const signature = sign(requestToSign);

  const headers = {
    'X-Fp-Signature': signature['x-fp-signature'],
    'X-Fp-Date': signature['x-fp-date'],
    ...jsonHeaderItems,
  };

  return headers;
}

function splitUrl(url: string) {
  try {
    // Extract host and path
    const [fullMatch, protocol, host, pathAndParams] =
      url.match(/^(https?):\/\/([^\/]+)(.*)$/) || [];

    if (!fullMatch) throw new Error('Invalid URL');

    // Separate path and query parameters
    const [path, queryString] = pathAndParams.split('?');
    const params = {};

    if (queryString) {
      queryString.split('&').forEach(pair => {
        const [key, value] = pair.split('=');
        (params as Record<string, string>)[key] = decodeURIComponent(value);
      });
    }

    return {
      host: `${protocol}://${host}`, // Construct the base URL
      path: path || '/', // Default to root if no path
      params,
    };
  } catch (error) {
    return null;
  }
}

export {fetchDataFromAPI, splitUrl};
