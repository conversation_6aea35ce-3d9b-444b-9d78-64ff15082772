import {ApiConstant} from './APIConstant';
import {fetchDataFromAPI} from './SearchService';

export const searchProducts = async (queryParams: any, body: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      /*DataLoaderMap['getProducts'] ||*/ ApiConstant.getSearchResultSubPath
        .path,
      ApiConstant.getSearchResultSubPath.method,
      queryParams,
      body,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};
