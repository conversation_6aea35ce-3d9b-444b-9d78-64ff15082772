export const ApiConstant = {
  //   host: 'jiomartplaypitcompany.jiox1.de',
  //   hostURL: 'https://jiomartplaypitcompany.jiox1.de',
  host: 'jiomart.sit.jiomartjcp.com',
  hostURL: 'https://jiomart.sit.jiomartjcp.com',
  subPath: '/api/service/application/catalog/v1.0/',
  // extUrlTrendingSearch: '/api/ext/algolia/application/api/v1.0/suggest',
  extUrlTrendingSearch: '/ext/algolia/application/api/v1.0/discover-more',
  // extUrlSearchResults: '/api/service/application/catalog/v1.0/auto-complete',
  extUrlSearchResults: '/ext/algolia/application/api/v1.0/auto-complete',
  extUrlRecommendedItems: '/ext/mylist/rra',
  sizeChartURL: 'https://novus-advanced-cms.extensions.jiox1.de',
  mapApiKey: '',

  RecommendedProductsServiceabilityPath: {
    path: '/ext/algolia/application/api/v1.0/deliverable/products',
    method: 'POST',
  },
  addressListingPath: {
    path: '/api/service/application/cart/v1.0/address',
    method: 'GET',
  },
  removeAddressSubPath: {
    path: '/api/service/application/cart/v1.0/address/{address_id}',
    method: 'DELETE',
  },
  defaultAddressSubPath: {
    path: '/api/service/application/cart/v1.0/address/{address_id}',
    method: 'PUT',
  },
  getAddAddress: {
    path: '/api/service/application/cart/v1.0/address',
    method: 'POST',
  },
  getPincodeCitySubPath: {
    path: '/api/service/application/logistics/v1.0/pincode/{pincode}',
    method: 'GET',
  },

  fetchWishListItems: {
    path: '/api/service/application/catalog/v1.0/follow/{collection_type}/',
    method: 'GET',
  },

  addToWishList: {
    path: '/api/service/application/catalog/v1.0/follow/{collection_type}/{collection_id}/',
    method: 'POST',
  },

  removeFromWishList: {
    path: '/api/service/application/catalog/v1.0/follow/{collection_type}/{collection_id}/',
    method: 'DELETE',
  },

  productListingSubPath: {
    // path: '/ext/algolia/application/api/v1.0/collections/{slug}/items',
    path: '/api/service/application/catalog/v1.0/collections/{slug}/items',
    method: 'GET',
  },
  getCartSubPath: {
    path: '/api/service/application/cart/v1.0/detail',
    method: 'GET',
  },
  getNewCartSubPath: {
    path: '/ext/jmshipmentfee/cart/v1.0/get_cart',
    method: 'GET',
  },
  getPriceSubPath: {
    path: '/api/service/application/catalog/v1.0/products/sizes/price',
    method: 'POST',
  },
  initiateAddToCartSubPath: {
    // path: '/api/service/application/cart/v1.0/detail',
    path: 'ext/jmshipmentfee/cart/v1.0/add_items',
    method: 'POST',
  },
  updateCartSubPath: {
    //for update cart quantity=> both increment and decrement
    // path: '/api/service/application/cart/v1.0/detail',
    path: '/ext/jmshipmentfee/cart/v1.0/update_cart',
    method: 'PUT',
  },
  getSearchResultSubPath: {
    path: '/ext/algolia/application/api/v1.0/products',
    method: 'GET',
  },
  getProductDetailsSubPath: {
    path: '/api/service/application/catalog/v1.0/products/{slug}',
    method: 'GET',
  },
  getSaveForLaterProductsSubPath: {
    path: '/ext/sfl/get',
    method: 'POST',
  },
  saveProductItemForLaterSubPath: {
    path: '/ext/sfl/save',
    method: 'POST',
  },
  deleteSaveForLaterItemSubPath: {
    path: '/ext/sfl/delete/{item_code}',
    method: 'DELETE',
  },
  getProductSizesBySlugSubPath: {
    path: '/api/service/application/catalog/v1.0/products/{slug}/sizes',
    method: 'GET',
  },
  getProductPriceBySlugSubPath: {
    path: '/api/service/application/catalog/v4.0/products/{slug}/sizes/{size}/price/',
    method: 'GET',
  },
  getProductPrice: {
    path: '/api/service/application/catalog/v1.0/products/sizes/price',
    method: 'POST',
  },
  getCouponsSubPath: {
    path: '/api/service/application/cart/v1.0/coupon',
    method: 'GET',
  },
  applyCouponSubPath: {
    path: '/api/service/application/cart/v1.0/coupon',
    method: 'POST',
  },
  removeCouponSubPath: {
    path: '/api/service/application/cart/v1.0/coupon',
    method: 'DELETE',
  },
  getEMIDetailsSubPath: {
    path: '/ext/emi/get-plans',
    method: 'GET',
  },

  installationAndWarrantyProductsSubPath: {
    path: '/api/service/application/catalog/v1.0/product-grouping',
    method: 'GET',
  },
  productVariantsBySlugSubPath: {
    path: '/api/service/application/catalog/v1.0/products/{slug}/variants/',
    method: 'GET',
  },
  getAdditionalOffersSubPath: {
    path: '/ext/novus/list_offers',
    method: 'GET',
  },
  getProductPromotionsSubPath: {
    path: '/api/service/application/cart/v1.0/available-promotions',
    method: 'GET',
  },
  exchangePincodeAvailabliitySubPath: {
    path: '/ext/exchangewidget/v2.0/pincode-availability',
    method: 'GET',
  },
  getExchangeCacheQuoteSubPath: {
    path: '/ext/exchangewidget/v2.0/quote',
    method: 'GET',
  },
  getExchangeAvailableModelListSubPath: {
    path: '/ext/exchangewidget/v1.0/models-data',
    method: 'GET',
  },
  getExchangePhoneConditionSubPath: {
    path: '/ext/exchangewidget/v1.0/phone-conditions',
    method: 'GET',
  },
  getExchangeEmiValidationSubPath: {
    path: '/ext/exchangewidget/v1.0/verify-imei',
    method: 'POST',
  },
  getExchangeApplyExchangeSubPath: {
    path: '/ext/exchangewidget/v1.0/get-quote/',
    method: 'POST',
  },
  initiateExchangeAddToCartSubPath: {
    path: '/ext/exchangewidget/v1.0/cart/add/',
    method: 'POST',
  },
  setApplyQuoteInDifferentProductExchangeSubPath: {
    path: '/ext/exchangewidget/v1.0/get-quote/',
    method: 'POST',
  },
  removeApplyQuoteOnDifferentProductExchangeSubPath: {
    path: '/ext/exchangewidget/v2.0/quote/remove/',
    method: 'DELETE',
  },
  getAllCategoriesSubPath: {
    path: '/api/service/application/catalog/v1.0/categories/',
    method: 'GET',
  },
  getDepartmentsSubPath: {
    path: '/api/service/application/catalog/v1.0/departments/',
    method: 'GET',
  },
  getRecommendationListSubPath: {
    path: '/ext/rraextension/recommendation/list',
    method: 'GET',
  },
  getBulkProductSubPath: {
    // path: '/ext/rraextension/bulk-product/',
    path: '/ext/algolia/application/api/v1.0/deliverable/products',
    // method: 'GET',
    method: 'POST',
  },
  getSizeChartPath: {
    path: '/cms/proxy/sizeChartByCode',
    method: 'GET',
  },

  getItemSize: {
    path: '/api/service/application/catalog/v1.0/products/{slug}/sizes',
    method: 'GET',
  },
  // ### Reviews Apis ###
  getReviewsSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/op/review/product/{productid}',
    method: 'GET',
  },
  getImageReviewsSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/op/review/product/images',
    method: 'GET',
  },
  uploadMultipleImagesSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/file/generateSignedURLInternal',
    method: 'POST',
  },
  authenticateUploadedImage: {
    path: '/customer/op/v1/file/upload',
    method: 'PUT',
  },
  getUsersReviewSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/review/user/product',
    method: 'GET',
    // /ext/jcp-jiomart/application/v1.0/review/user/product?productId=490005532&alternateCode1=490005532
  },
  addReviewSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/review/product',
    method: 'POST',
  },
  editUsersReviewSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/review/product/{reviewid}',
    method: 'PUT',
  },
  getAverageRatingSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/op/review/product-statistics/{productid}',
    method: 'GET',
  },
  addHelpfulReviewSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/review/vote/count',
    method: 'PUT',
  },
  getVerifiedPurchasedSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/review/verifyPurchase/{productid}',
    method: 'GET',
  },
  getReviewConfigSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/op/config/bulkConfig',
    method: 'POST',
  },
  addReportAbuseSubPath: {
    path: '/ext/jcp-jiomart/application/v1.0/abuse/reportReview',
    method: 'POST',
  },

  // Data Loader API

  getDataLoader: {
    path: '/api/service/application/content/v1.0/data-loader',
    method: 'GET',
  },

  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
};
export const getPath = (
  endpoint: string,
  method: string,
  queryParams: any | null,
) => {
  //method === ApiConstant.GET &&
  if (queryParams != null) {
    const encodedParams = getEncodedParams(queryParams);
    return `${endpoint}?${encodedParams}`;
  }
  return `${endpoint}/`;
};

export const getURL = (
  host: string = '',
  endpoint: string,
  method: string,
  queryParams: any,
) => {
  const path = getPath(endpoint, method, queryParams);
  const url = host !== '' ? host : ApiConstant.hostURL;
  return url + path;
};

const getEncodedParams = (queryParams: any) => {
  return Object.keys(queryParams)
    .map(
      key =>
        encodeURIComponent(key) + '=' + encodeURIComponent(queryParams[key]),
    )
    .join('&');
};
