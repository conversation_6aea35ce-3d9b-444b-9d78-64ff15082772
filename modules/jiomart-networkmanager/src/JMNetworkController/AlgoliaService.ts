import algoliasearch from 'algoliasearch/lite';
import { base64DecodeValue } from '../../../jiomart-common/src/Helper';


interface FetchAlgoliaIndexSearchProps {
  algoliaConfig?: any;
  query?: string;
  algoliaIndex?: string[] | string;
  hitsPerPage?: number[] | number;
  configApiId: string;
  configApiHash: string;
}

let prevQuery = '';

export const fetchAlgoliaSearch = async ({
  query,
  algoliaConfig,
  algoliaIndex = ['prod_mart_query_suggestions', 'prod_mart_master_vertical'],
  hitsPerPage,
  configApiId,
  configApiHash,
}: FetchAlgoliaIndexSearchProps): Promise<{[indexName: string]: any[]}> => {
  prevQuery = query as string;
  const decryptedconfigApiId = base64DecodeValue(configApiId);
  const decryptedconfigApiHash = base64DecodeValue(configApiHash);
  const searchClient = algoliasearch(
    decryptedconfigApiId,
    decryptedconfigApiHash,
  );
  try {
    const searchRequests = [
      {
        indexName: algoliaIndex?.[0],
        query,
        params: {
          hitsPerPage: hitsPerPage?.[0],
          filters: algoliaConfig?.endpointDetails?.filters ?? '',
          analyticsTags: algoliaConfig?.endpointDetails?.analyticsTags,
          clickAnalytics: algoliaConfig?.endpointDetails?.clickAnalytics,
          page: 0,
          facets: ['*'],
        },
      },
      {
        indexName: algoliaIndex?.[1],
        query,
        params: {
          hitsPerPage: hitsPerPage?.[1],
          filters: algoliaConfig?.endpointDetails?.plpFilters ?? '',
          analyticsTags: algoliaConfig.endpointDetails?.analyticsTags,
          clickAnalytics: false,
          analytics: false,
          page: 0,
          facets: ['*'],
          attributesToRetrieve: [
            'product_code',
            'display_name',
            'brand',
            'category_level.level4',
            'food_type',
            'buybox_mrp',
            'vertical_code',
            'image_path',
            'url_path',
          ],
        },
      },
    ];

    const response = await searchClient.search(searchRequests);

    const filterResponse = response?.results;

    let searchResults: {[indexName: string]: any[]} = {};

    if (
      filterResponse?.[0]?.query == prevQuery &&
      filterResponse?.[1]?.query == prevQuery
    ) {
      filterResponse.forEach((result: any) => {
        searchResults[result.index] = result.hits;
      });
      return searchResults;
    }
  } catch (error) {
    console.error('Error fetching Algolia data:', error);
    return {};
  }
};

export const fetchSingleIndexSearch = async ({
  algoliaConfig,
  query = '',
  algoliaIndex = 'prod_mart_query_suggestions',
  hitsPerPage,
  configApiId,
  configApiHash,
}: FetchAlgoliaIndexSearchProps): Promise<any[]> => {
  const decryptedconfigApiId = base64DecodeValue(configApiId);
  const decryptedconfigApiHash = base64DecodeValue(configApiHash);
  const searchClient = algoliasearch(
    decryptedconfigApiId,
    decryptedconfigApiHash,
  );
  const algoliaData = {
    indexName: algoliaIndex,
    query: query,
    params: {
      hitsPerPage: hitsPerPage,
      filters: algoliaConfig?.endpointDetails?.filters || '',
      analyticsTags: algoliaConfig?.endpointDetails?.analyticsTags,
      clickAnalytics: algoliaConfig?.endpointDetails?.clickAnalytics,
      page: 0,
      facets: ['*'],
    },
  };
  try {
    const index = searchClient.initIndex(algoliaData?.indexName);
    const {hits} = await index?.search(algoliaData.query, algoliaData.params);
    return hits;
  } catch (error) {
    console.error('Error fetching Algolia data:', error);
    return [];
  }
};
