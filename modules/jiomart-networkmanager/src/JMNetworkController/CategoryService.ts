// import {FirebaseCrashlytics} from '../../../utilities/utils/FirebaseUtility';
import { ApiConstant } from './APIConstant';
import { fetchDataFromAPI } from './SearchService';

const service = () => {
  const fetchCategoryApi = async (queryParams: any) => {
    return new Promise((resolve, reject) => {
      fetchDataFromAPI(
        ApiConstant.getAllCategoriesSubPath.path,
        ApiConstant.getAllCategoriesSubPath.method,
        queryParams,
        null,
      )
        .then(items => {
          resolve(items);
        })
        .catch(error => {
        //   FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
          reject(error);
        });
    });
  };

  const fetchDepartmentsApi = async () => {
    return new Promise((resolve, reject) => {
      fetchDataFromAPI(
        ApiConstant.getDepartmentsSubPath.path,
        ApiConstant.getDepartmentsSubPath.method,
        null,
        null,
      )
        .then(items => {
          resolve(items);
        })
        .catch(error => {
        //   FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
          reject(error);
        });
    });
  };

  const fetchProductsApi = () => {
    return new Promise((resolve, reject) => {
      fetchDataFromAPI(
        ApiConstant.getSearchResultSubPath.path,
        ApiConstant.getSearchResultSubPath.method,
        null,
        null,
      )
        .then(items => {
          resolve(items);
        })
        .catch(error => {
        //   FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
          reject(error);
        });
    });
  };

  return {
    fetchCategoryApi,
    fetchDepartmentsApi,
    fetchProductsApi,
  };
};

const CategoryService = service();

export default CategoryService;
