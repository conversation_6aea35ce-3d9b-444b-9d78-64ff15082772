{"initialTab": "Orders", "tabs": ["Orders", "Refund"], "disableTabGesture": false, "Orders": {"pageNo": 1, "pageSize": 10, "card": {"header": {"title": {}, "subTitle": {}, "icon": {"ic": "IcChevronRight"}}, "verticalCode": ["groceries"], "channelType": ["whatsapp"], "smartBazzarImage": {"source": {"uri": "https://cdn.pixelbin.io/v2/jiomart-fynd/jminfi/original/images/othe/egurhk/.j/pg/bambino-premium-penne-pasta-250-g-0-20201113.jpg.aab8ca123c.jpg"}}, "grouping": {"productSubTitle": {"text": "[ITEM_LENGTH] Items"}}, "shouldShowRateBlock": {"status": ["Delivered", "Returned"]}, "shouldShowReviewBlock": {"status": ["Delivered", "Returned"]}, "shouldShowReviewMessageBlock": {"status": ["Delivered", "Returned"]}, "deliveryOtp": {"text": "Delivery OTP: [DELIVERY_OTP]"}, "refund": {"text": "Refund of Rs. [TOTAL_REFUND_AMOUNT] [REFUND_STATUS] on [REFUND_DATE]"}, "refundDateFormat": "[MMM] [DD] [YYYY]", "rateTitle": {"text": "Rate this product: "}, "reviewMessage": {"rateMessage": {"text": "Rate items in order"}}, "reviewTitle": {"1": {"text": "Review this product 1"}, "2": {"text": "Review this product 2"}, "3": {"text": "Review this product 3"}, "4": {"text": "Review this product 4"}, "5": {"text": "Review this product 5"}}, "reviewSubTitle": {"1": {"text": "Write a review 1"}, "2": {"text": "Write a review 2"}, "3": {"text": "Write a review 3"}, "4": {"text": "Write a review 4"}, "5": {"text": "Write a review 5"}}, "cta": {"navTitle": "Order Details", "headerType": 1, "source": "", "destination": "CommonWebViewScreen", "actionType": "T003", "actionUrl": "/customer/orderhistory/view/[FROM]/[ORDER_ID]/[SHIPMENT_ID]/[SOURCE]", "bundle": "", "navigationType": "push"}, "ratingAndReviewCta": {"navTitle": "Rate & Review", "headerType": 1, "source": "", "destination": "JMRatingAndReviewFormScreen", "actionType": "T001", "actionUrl": "", "bundle": "", "navigationType": "push"}}, "negativeCases": {"image": {"uri": "https://myjiostatic.cdn.jio.com/JioMart/Common/qna.jpg"}, "title": {"text": "You don't have any address saved!"}, "subTitle": {"text": "Add a new address to find the best products and offers in your area."}, "button": {"title": "Add New Address", "iconLeft": "IcAdd"}, "isButtonVisible": true, "shouldShowContentInCenter": false, "cta": {"navTitle": "Add Address", "HeaderType": 1, "source": "", "destination": "JMAddressSearchScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}, "filterBottomSheet": {"data": [{"key": {"display": "Order Type", "name": "orderStatusText", "kind": "singlevalued"}, "values": "[ORDER_STATUS_LIST]"}, {"key": {"display": "Order Date", "name": "timeFilter", "kind": "singlevalued"}, "values": "[ORDER_DATE_LIST]"}], "title": "Filter", "button": {"clearAll": {"isVisible": true, "title": "Clear All"}, "apply": {"isVisible": true, "title": "Apply"}}}}, "Refund": {"pageNo": 1, "pageSize": 10, "card": {"headerTitle": {"text": "Order ID: [ORDER_ID]"}, "icon": {"ic": "IcChevronRight"}, "title": {}, "subTitle": {}, "cta": {"actionType": "T001", "navTitle": "Refund Details", "source": "", "destination": "JMRefundDetailScreen", "bundle": "", "headerType": 1, "navigationType": "push"}}, "negativeCases": {"image": {"uri": "https://myjiostatic.cdn.jio.com/JioMart/Common/qna.jpg"}, "title": {"text": "You don't have any address saved!"}, "subTitle": {"text": "Add a new address to find the best products and offers in your area."}, "button": {"title": "Add New Address", "iconLeft": "IcAdd"}, "isButtonVisible": true, "shouldShowContentInCenter": false, "cta": {"navTitle": "Add Address", "HeaderType": 1, "source": "", "destination": "JMAddressSearchScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}, "filterBottomSheet": {"data": [{"key": {"display": "Date & Time", "name": "timeFilter", "kind": "singlevalued"}, "values": "[REFUND_DATE_LIST]"}], "title": "Filter", "button": {"clearAll": {"isVisible": true, "title": "Clear All"}, "apply": {"isVisible": true, "title": "Apply"}}}}}