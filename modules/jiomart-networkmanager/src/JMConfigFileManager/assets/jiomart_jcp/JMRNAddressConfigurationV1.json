{"screen": {"addressList": {"headerTitle": {"text": "Saved Address", "appearance": "heading_xxs", "color": "primary_grey_100"}, "shouldShowNoOfAddress": 20, "dropDown": {"default": [{"isVisible": true, "type": "EDIT", "icon": {"ic": "IcEditPen", "color": "primary_grey_80"}, "label": {"text": "Edit"}, "cta": {"navTitle": "Set Delivery Location", "HeaderType": 1, "source": "", "destination": "JMAddressMapScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}], "normal": [{"isVisible": true, "type": "EDIT", "icon": {"ic": "IcEditPen", "color": "primary_grey_80"}, "label": {"text": "Edit"}, "cta": {"navTitle": "Set Delivery Location", "HeaderType": 1, "source": "", "destination": "JMAddressMapScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}, {"isVisible": true, "type": "DELETE", "icon": {"ic": "IcTrash", "color": "primary_grey_80"}, "label": {"text": "Delete"}}, {"isVisible": true, "type": "MARK_AS_DEFAULT", "icon": {"ic": "IcMessageSend", "color": "primary_grey_80"}, "label": {"text": "<PERSON> as <PERSON><PERSON><PERSON>"}}]}, "alert": {"addressBook": {"leftIcon": {"ic": "IcWarningColored", "size": "medium", "color": ""}, "title": {"text": "Your address book is full", "appearance": "body_xxs_bold", "color": "black"}, "subTitle": {"text": "To add a new address, please delete one of your saved address.", "appearance": "body_xxs", "color": "black"}, "backgroundColor": "feedback_warning_20"}}, "cards": {"addressListCard": {"name": {"color": "primary_grey_100", "appearance": "body_xs_bold", "maxLines": 2}, "type": {}, "icon": {"ic": "IcMoreVertical", "color": "primary_60", "size": "medium"}, "address": {}, "phone": {"text": "Phone: [TEXT]"}, "subText": {"text": "<PERSON><PERSON><PERSON> Address"}, "hideAddress": {}}, "orderReviewListCard": {"name": {"color": "primary_grey_100", "appearance": "body_xs_bold", "maxLines": 2}, "type": {}, "phone": {"text": "Phone: [TEXT]"}, "icon": {"ic": "IcEditPen"}, "subText": {"text": "<PERSON><PERSON><PERSON> Address"}, "hideAddress": {}}}, "negativeCases": {"emptyAddress": {"image": {"uri": "https://myjiostatic.cdn.jio.com/JioMart/Common/qna.jpg"}, "title": {"text": "You don't have any address saved!"}, "subTitle": {"text": "Add a new address to find the best products and offers in your area."}, "button": {"title": "Add New Address", "iconLeft": "IcAdd"}, "isButtonVisible": true, "shouldShowContentInCenter": false, "cta": {"navTitle": "Add Address", "HeaderType": 1, "source": "", "destination": "JMAddressSearchScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}}, "addAddressButton": {"title": "Add New Address", "iconLeft": "IcAdd", "cta": {"navTitle": "Add Address", "HeaderType": 1, "source": "", "destination": "JMAddressFormV1Screen", "actionType": "T001", "actionUrl": "", "bundle": ""}, "ctaAlt": {"navTitle": "Set Delivery Location", "HeaderType": 1, "source": "", "destination": "JMAddressMapScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}, "bottomSheet": {"delete": {"headerTitle": "Delete Address", "name": {}, "address": {}, "description": {}, "button": {"title": "Delete"}}}}, "addressSearch": {"headerTitle": {"text": "To add a new address, search for your area, landmark, street name or apartment.", "appearance": "heading_xxs", "color": "primary_grey_80"}, "addressListHeaderTitle": {"text": "Your Saved Addresses", "appearance": "overline", "color": "primary_grey_60"}, "shouldShowNoOfSearchSuggestion": 4, "googlePredictionFilter": {"countries": ["in"]}, "alert": {"blockedLocation": {"title": "", "message": "Please provide your location or search using your closest landmark/apartment name", "button": {"text": "Enable Now"}}}, "location": {"icon": {"ic": "IcCameraFocus", "color": "primary_60", "size": "medium"}, "title": {"text": "Use Current Location", "color": "primary_60", "appearance": "body_s_bold"}, "subTitle": {"text": "For more accurate delivery using GPS", "color": "primary_grey_80", "appearance": "body_xxs"}, "loader": {"animating": true, "hidesWhenStopped": false, "color": "#0C5273", "size": "small"}}, "searchBar": {"placeholder": "Search for area, landmark"}, "searchSuggestion": {"icon": {"ic": "IcLocation", "color": "primary_grey_80", "size": "medium"}, "title": {"text": "", "color": "primary_grey_100", "appearance": "body_s"}, "subTitle": {"text": "", "color": "primary_grey_80", "appearance": "body_xxs", "maxLines": 1}, "cta": {"navTitle": "Set Delivery Location", "HeaderType": 1, "source": "", "destination": "JMAddressMapScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}}, "addressMap": {"headerTitle": {"text": "To add a new address, search for your area, landmark, street name or apartment.", "appearance": "heading_xxs", "color": "primary_grey_80"}, "shouldShowNoOfSearchSuggestion": 4, "googlePredictionFilter": {"countries": ["in"]}, "alert": {"blockedLocation": {"title": "", "message": "Please provide your location or search using your closest landmark/apartment name", "button": {"text": "Enable Now"}}, "invalidLocation": {"leftIcon": {"ic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size": "medium", "color": "feedback_warning"}, "title": {"text": "Unable to fetch location", "appearance": "body_xxs_bold"}, "subTitle": {"text": "You haven't set your location pin yet! Please confirm your location to proceed", "appearance": "body_xxs", "color": "black"}, "backgroundColor": "feedback_warning_20"}}, "searchSuggestion": {"icon": {"ic": "IcLocation", "color": "primary_grey_80", "size": "medium"}, "title": {"text": "", "color": "primary_grey_100", "appearance": "body_s"}, "subTitle": {"text": "", "color": "primary_grey_80", "appearance": "body_xxs", "maxLines": 1}}, "searchBar": {"placeholder": "Search for area, landmark"}, "map": {"scrollEnabled": true, "zoomEnabled": true, "pitchEnabled": false, "rotateEnabled": true, "showsCompass": false, "showsBuildings": true, "showsTraffic": false, "showsIndoors": false, "provider": "google", "initialCamera": {"pitch": 0, "heading": 0, "zoom": 18}}, "defaultLocation": {"latitude": 18.9348965, "longitude": 72.8161317}, "cameraFocus": {"icon": {"ic": "IcCameraFocus", "color": "primary_grey_80"}, "loader": {"animating": true, "hidesWhenStopped": false, "color": "#0C5273", "size": "small"}}, "addressBox": {"title": {"text": "YOUR LOCATION", "appearance": "overline", "color": "primary_grey_60"}, "address": {"icon": {"ic": "IcLocation", "color": "primary_grey_80", "size": "medium"}, "subTitle": {"text": "", "color": "primary_grey_80", "appearance": "body_xxs"}, "button": {"title": "Confirm Location", "size": "large", "kind": "primary", "stretch": true, "cta": {"navTitle": "Add Address", "HeaderType": 1, "source": "", "destination": "JMAddressFormV1Screen", "actionType": "T001", "actionUrl": "", "bundle": ""}}}}}, "addressForm": {"header": {"editNavTitle": "Edit Address"}, "map": {"scrollEnabled": false, "zoomEnabled": false, "pitchEnabled": false, "rotateEnabled": false, "showsCompass": false, "showsBuildings": true, "showsTraffic": false, "showsIndoors": false, "provider": "google", "initialCamera": {"pitch": 45, "heading": 90, "zoom": 18}}, "headerTitle": {"color": "primary_grey_80", "appearance": "body_xxs"}, "link": {"text": "Edit Location on Map", "color": "primary_60", "appearance": "body_xxs_link"}, "formHeaderTitle": {"text": "Address Details", "color": "black", "appearance": "heading_xxs"}, "deliveryContactDetail": {"headerTitle": {"text": "Delivery Contact Details", "appearance": "heading_xxs", "color": "primary_grey_100"}, "subTitle": {"text": "This mobile number will receive an OTP required for collecting the order.", "appearance": "body_xxs", "color": "primary_grey_100"}}, "submitButton": {"title": "Save & Proceed", "cta": {"navTitle": "Delivery Addresses", "HeaderType": 1, "source": "", "destination": "JMAddressListScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}, "saveAs": [{"title": "Home", "value": "Home"}, {"title": "Work", "value": "Work"}, {"title": "Other", "value": ""}], "saveAsHeaderTitle": {"text": "Save As", "appearance": "heading_xxs", "color": "primary_grey_100"}, "form": {"flat_or_house_no": {"min": 0, "max": 8, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter House No. (min 0 characters)", "max": "Please enter House No. (max 8 characters)", "required": "Please enter your House No.", "default": "Please enter a valid House No."}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "House No."}, "feedback": {"state": "error"}}, "floor_no": {"min": 0, "max": 8, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter Floor No. (min 0 characters)", "max": "Please enter Floor No. (max 8 characters)", "required": "Please enter your Floor No.", "default": "Please enter a valid Floor No."}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Floor No."}, "feedback": {"state": "error"}}, "tower_no": {"min": 0, "max": 8, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter Tower No. (min 0 characters)", "max": "Please enter Tower No. (max 8 characters)", "required": "Please enter your Tower No.", "default": "Please enter a valid Tower No."}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Tower No."}, "feedback": {"state": "error"}}, "area": {"min": 3, "max": 40, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter Building / Apartment Name (min 3 characters)", "max": "Please enter Building / Apartment Name (max 40 characters)", "required": "Please enter Building / Apartment Name (min 3 - max 40 characters)", "default": "Please enter valid Building / Apartment Name"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Building / Apartment Name"}, "feedback": {"state": "error"}}, "address": {"min": 3, "max": 512, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9,./ -]*$", "error": {"min": "Please enter address (min 3 characters)", "max": "Please enter address (max 512 characters)", "required": "Please enter valid address (min 3 - max 512 characters)", "default": "Please enter a valid address"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Address*"}, "feedback": {"state": "error"}}, "landmark": {"min": 3, "max": 80, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "[A-Za-z0-9,./ -]*$", "error": {"min": "Please enter a valid Landmark / Area  (min 3 characters)", "max": "Please enter a valid Landmark / Area (max 80 characters)", "required": "Please enter your Landmark / Area", "default": "Please enter a valid Landmark / Area"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Landmark / Area*"}, "feedback": {"state": "error"}}, "pincode": {"min": 6, "max": 6, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[0-9]{6}$", "error": {"min": "Please enter a ZIP or postal code", "max": "Please enter a ZIP or postal code", "required": "Please enter your ZIP or postal code", "default": "Please enter a valid ZIP or postal code"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Pincode*"}, "feedback": {"state": "error"}}, "city": {"min": 3, "max": 200, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid City (min 3 characters)", "max": "Please enter a valid City (max 200 characters)", "required": "Please enter your City", "default": "Please enter a valid City"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "City*"}, "feedback": {"state": "error"}}, "state": {"min": 3, "max": 200, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid State (min 3 characters)", "max": "Please enter a valid State (max 200 characters)", "required": "Please enter your State", "default": "Please enter a valid State"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "State*"}, "feedback": {"state": "error"}}, "name": {"min": 3, "max": 80, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid Receiver Name (min 3 characters)", "max": "Please enter a valid Receiver Name (max 80 characters)", "required": "Please enter your Receiver Name", "default": "Please enter a valid Receiver Name"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Receiver Name*"}, "feedback": {"state": "error"}}, "phone": {"min": 10, "max": 10, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[0-9]{10}$", "error": {"min": "Please enter a valid Receiver Number (min 10 digits)", "max": "Please enter a valid Receiver Number (max 10 digits)", "required": "Please enter your Receiver Number", "default": "Please enter a valid Receiver Number"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Receiver Number*"}, "prefix": {"title": {"text": "+91"}, "icon": {"ic": "IcChevronDown"}}, "feedback": {"state": "error"}}, "address_type": {"min": 3, "max": 30, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid Address Type (min 3 digits)", "max": "Please enter a valid Address Type (max 30 digits)", "required": "Please enter your Address Type", "default": "Please enter a valid Address Type"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Eg. Club House, Kumar's Home*"}, "feedback": {"state": "error"}}}}, "addressFormV1": {"header": {"editNavTitle": "Edit Address"}, "map": {"scrollEnabled": false, "zoomEnabled": false, "pitchEnabled": false, "rotateEnabled": false, "showsCompass": false, "showsBuildings": true, "showsTraffic": false, "showsIndoors": false, "provider": "google", "initialCamera": {"pitch": 45, "heading": 90, "zoom": 18}}, "locationBlock": {"subTitle": {"text": "Additionally, for more accurate delivery to this address use your current location, or search for your building or area.", "color": "primary_grey_100", "appearance": "body_xxs"}, "shouldShowLocationBlock": ["pincode", "city", "state", "address", "landmark"], "shouldShowMapBlock": ["pincode", "city", "state"], "currentLocation": {"icon": {"ic": "IcCameraFocus", "color": "primary_60"}, "title": {"text": "Use Current Location", "color": "primary_60", "appearance": "button"}, "cta": {"navTitle": "Set Delivery Location", "HeaderType": 1, "source": "", "destination": "JMAddressMapScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}, "search": {"icon": {"ic": "IcSearch", "color": "primary_grey_80"}, "cta": {"navTitle": "Add Address", "HeaderType": 1, "source": "", "destination": "JMAddressSearchScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}}, "alert": {"blockedLocation": {"title": "", "message": "Please provide your location or search using your closest landmark/apartment name", "button": {"text": "Enable Now"}}}, "editDeliveryLocation": {"title": "Edit Delivery Location", "iconLeft": "IcEditPen", "cta": {"navTitle": "Set Delivery Location", "HeaderType": 1, "source": "", "destination": "JMAddressMapScreen", "actionType": "T001", "actionUrl": "", "bundle": ""}}, "headerTitle": {"color": "primary_grey_80", "appearance": "body_xxs"}, "link": {"text": "Edit Location on Map", "color": "primary_60", "appearance": "body_xxs_link"}, "formHeaderTitle": {"text": "Address Details", "color": "black", "appearance": "heading_xxs"}, "deliveryContactDetail": {"headerTitle": {"text": "Delivery Contact Details", "appearance": "heading_xxs", "color": "primary_grey_100"}, "subTitle": {"text": "This mobile number will receive an OTP required for collecting the order.", "appearance": "body_xxs", "color": "primary_grey_100"}}, "submitButton": {"title": "Save & Proceed"}, "saveAs": [{"title": "Home", "value": "Home"}, {"title": "Work", "value": "Work"}, {"title": "Other", "value": ""}], "saveAsHeaderTitle": {"text": "Save As", "appearance": "heading_xxs", "color": "primary_grey_100"}, "form": {"pincode": {"min": 6, "max": 6, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[0-9]{6}$", "error": {"min": "Please enter a ZIP or postal code", "max": "Please enter a ZIP or postal code", "required": "Please enter your ZIP or postal code", "default": "Please enter a valid ZIP or postal code"}, "textInput": {"autoCorrect": false, "keyboardType": "number-pad", "maxLength": 6}, "label": {"text": "Pincode*"}, "feedback": {"state": "error"}}, "city": {"min": 3, "max": 200, "isReadOnly": true, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid City (min 3 characters)", "max": "Please enter a valid City (max 200 characters)", "required": "Please enter your City", "default": "Please enter a valid City"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "City*"}, "feedback": {"state": "error"}}, "state": {"min": 3, "max": 200, "isReadOnly": true, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid State (min 3 characters)", "max": "Please enter a valid State (max 200 characters)", "required": "Please enter your State", "default": "Please enter a valid State"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "State*"}, "feedback": {"state": "error"}}, "flat_or_house_no": {"min": 0, "max": 8, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter House No. (min 0 characters)", "max": "Please enter House No. (max 8 characters)", "required": "Please enter your House No.", "default": "Please enter a valid House No."}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "House No."}, "feedback": {"state": "error"}}, "floor_no": {"min": 0, "max": 8, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter Floor No. (min 0 characters)", "max": "Please enter Floor No. (max 8 characters)", "required": "Please enter your Floor No.", "default": "Please enter a valid Floor No."}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Floor No."}, "feedback": {"state": "error"}}, "tower_no": {"min": 0, "max": 8, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter Tower No. (min 0 characters)", "max": "Please enter Tower No. (max 8 characters)", "required": "Please enter your Tower No.", "default": "Please enter a valid Tower No."}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Tower No."}, "feedback": {"state": "error"}}, "area": {"min": 3, "max": 40, "isReadOnly": false, "isRegexClean": false, "required": false, "regex": "", "error": {"min": "Please enter Building / Apartment Name (min 3 characters)", "max": "Please enter Building / Apartment Name (max 40 characters)", "required": "Please enter Building / Apartment Name (min 3 - max 40 characters)", "default": "Please enter valid Building / Apartment Name"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Building / Apartment Name"}, "feedback": {"state": "error"}}, "address": {"min": 3, "max": 512, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9,./ -]*$", "error": {"min": "Please enter address (min 3 characters)", "max": "Please enter address (max 512 characters)", "required": "Please enter valid address (min 3 - max 512 characters)", "default": "Please enter a valid address"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Address*"}, "feedback": {"state": "error"}}, "landmark": {"min": 3, "max": 80, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "[A-Za-z0-9,./ -]*$", "error": {"min": "Please enter a valid Landmark / Area  (min 3 characters)", "max": "Please enter a valid Landmark / Area (max 80 characters)", "required": "Please enter your Landmark / Area", "default": "Please enter a valid Landmark / Area"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Landmark / Area*"}, "feedback": {"state": "error"}}, "name": {"min": 3, "max": 80, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid Receiver Name (min 3 characters)", "max": "Please enter a valid Receiver Name (max 80 characters)", "required": "Please enter your Receiver Name", "default": "Please enter a valid Receiver Name"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Receiver Name*"}, "feedback": {"state": "error"}}, "phone": {"min": 10, "max": 10, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[0-9]{10}$", "error": {"min": "Please enter a valid Receiver Number (min 10 digits)", "max": "Please enter a valid Receiver Number (max 10 digits)", "required": "Please enter your Receiver Number", "default": "Please enter a valid Receiver Number"}, "textInput": {"autoCorrect": false, "keyboardType": "number-pad", "maxLength": 10}, "label": {"text": "Receiver Number*"}, "prefix": {"title": {"text": "+91"}, "icon": {"ic": "IcChevronDown"}}, "feedback": {"state": "error"}}, "address_type": {"min": 3, "max": 30, "isReadOnly": false, "isRegexClean": false, "required": true, "regex": "^[A-Za-z0-9. ]*$", "error": {"min": "Please enter a valid Address Type (min 3 digits)", "max": "Please enter a valid Address Type (max 30 digits)", "required": "Please enter your Address Type", "default": "Please enter a valid Address Type"}, "textInput": {"autoCorrect": false, "keyboardType": "default"}, "label": {"text": "Eg. Club House, Kumar's Home*"}, "feedback": {"state": "error"}}}, "message": {"invalidPincode": "We are currently not delivering at this location", "pincodeChangeFailed": "We are unable to process your request, retry later"}}}, "bottomSheet": {"deliverToBar": {"title": "Select Delivery Location", "description": {"title": "Select delivery address to see product availability and offers.", "maxLine": 2, "color": "primary_grey_80"}, "guestUserBtn": {"isVisible": true, "title": "Sign in to select address", "cta": {"navTitle": "Account", "source": "", "destination": "OneRetailUI", "actionUrl": "", "actionType": "T001", "userJourneyRequiredState": 0, "bundle": "", "type": "avatar", "navigationType": "push"}}, "addAddress": {"isVisible": true, "icon": {"ic": "IcAdd", "color": "primary_60", "size": "medium"}, "text": {"text": "Add New Address", "color": "primary_60", "appearance": "body_s_bold"}, "cta": {"navTitle": "Select Addresses", "source": "", "destination": "JMAddressFormV1Screen", "actionType": "T005", "actionUrl": "", "bundle": "", "headerType": 1, "navigationType": "navigate"}, "ctaAlt": {"navTitle": "Set Delivery Location", "source": "", "destination": "JMAddressMapScreen", "actionType": "T005", "actionUrl": "", "bundle": "", "headerType": 1, "navigationType": "navigate"}}, "addressBox": {"hideAddress": {"flat_or_house_no": {"isVisible": false}, "floor_no": {"isVisible": false}, "tower_no": {"isVisible": false}, "area": {"isVisible": false}, "address": {"isVisible": true, "length": 40}, "landmark": {"isVisible": false}, "pin": {"isVisible": false}}}, "options": [{"type": "PINCODE", "isVisible": true, "icon": {"ic": "IcLocation", "color": "primary_60", "size": "medium"}, "text": {"text": "Enter a pincode", "color": "primary_60", "appearance": "body_s_bold"}}, {"type": "DETECT_MY_LOCATION", "isVisible": true, "icon": {"ic": "IcCameraFocus", "color": "primary_60", "size": "medium"}, "text": {"text": "Detect my location", "color": "primary_60", "appearance": "body_s_bold"}}], "showNoOfAddress": 20, "alert": {"blockedLocation": {"title": "", "message": "Please provide your location or search using your closest landmark/apartment name", "button": {"text": "Enable Now"}}}}, "deliverToBarPincode": {"title": "Enter PIN Code", "description": {"title": "Enter PIN code to see product availability, offers and discounts.", "color": "primary_grey_80"}, "textInput": {"label": {"text": "PIN Code", "color": "primary_grey_80"}, "icon": {"ic": "IcLocation", "color": "primary_grey_80"}, "placeholder": "Enter a Pincode"}, "button": {"apply": {"text": "Apply"}}, "message": {"invalidPincode": "We are currently not delivering at this location", "pincodeChangeFailed": "We are unable to process your request, retry later"}, "successIcon": {"ic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size": "small", "color": "feedback_success"}, "errorIcon": {"ic": "IcSuccess", "size": "small", "color": "feedback_error"}}}}