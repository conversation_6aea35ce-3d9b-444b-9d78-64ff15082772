{"location": {"pincode": "400001", "city": "Mumbai", "name": "", "state": "Maharashtra", "country_iso_code": "IN", "country": "India"}, "privacyPolicyConfig": {"isVisible": true, "title": "Privacy Policy", "regularString": "Please note that our Privacy Policy has been updated and we have also added Retail Account Privacy Policy. By continuing to use our platform and its services, you consent to the updated policies.", "clickableString": "Privacy Policy,Retail Account Privacy Policy", "redirectionUrl": "privacy-policy,https://account.relianceretail.com/privacy-policy/", "buttonText": "Agree"}, "permissionConfig": {"isVisible": true, "title": "Allow access to JioMart", "subTitle": "Please provide us with the required permissions for a more personalised shopping experience.", "buttonText": "Proceed", "permissions": {"ios": [{"permissionTitle": {"text": "Allow location access"}, "permissionSubTitle": {"text": "Stay updated with location-based offers and product availability."}, "permissionIcon": {"ic": "IcLocation"}}, {"permissionTitle": {"text": "Allow Notifications"}, "permissionSubTitle": {"text": "Get notified about the latest offers, deals & new arrivals."}, "permissionIcon": {"ic": "IcNotification"}}], "android": [{"permissionTitle": {"text": "Allow location access"}, "permissionSubTitle": {"text": "Stay updated with location-based offers and product availability."}, "permissionIcon": {"ic": "IcLocation"}}, {"permissionTitle": {"text": "Allow Access to SMS"}, "permissionSubTitle": {"text": "Track your orders better by enabling real time order status notifications."}, "permissionIcon": {"ic": "IcNotification"}}, {"permissionTitle": {"text": "Allow Notifications"}, "permissionSubTitle": {"text": "Get notified about the latest offers, deals & new arrivals."}, "permissionIcon": {"ic": "IcNotification"}}]}, "requestPermissions": {"ios": ["ios.permission.LOCATION_WHEN_IN_USE", "ios.permission.APP_TRACKING_TRANSPARENCY"], "android": ["android.permission.ACCESS_FINE_LOCATION", "android.permission.READ_SMS", "android.permission.POST_NOTIFICATIONS"]}, "alert": {"blockedLocation": {"title": "", "message": "Please provide your location or search using your closest landmark/apartment name", "button": {"text": "Enable Now"}}}}, "quickCommerceConfig": {"featureEnabled": true, "quickDeliveryKey": "jiomart_quick", "scheduledDeliveryKey": "scheduled_delivery", "quickDeliveryMessage": "Quick Delivery in 10 to 30 minutes", "scheduledDeliveryMessage": "Scheduled Delivery", "verticalCode": ["GROCERIES"], "quickActiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "quickInactiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "scheduledActiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "scheduledInactiveImageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>", "initialQcTab": "jiomart_quick"}}