{
  "initialTab": "Orders",
  "tabs": ["Orders", "Refund"],
  "disableTabGesture": false,
  "Orders": {
    "pageNo": 1,
    "pageSize": 10,
    "card": {
      "header": {"title": {}, "subTitle": {}, "icon": {"ic": "IcChevronRight"}},
      "verticalCode": ["groceries"],
      "channelType": ["whatsapp"],
      "smartBazzarImage": {
        "source": {
          "uri": "https://cdn.pixelbin.io/v2/jiomart-fynd/jminfi/original/images/othe/egurhk/.j/pg/bambino-premium-penne-pasta-250-g-0-20201113.jpg.aab8ca123c.jpg"
        }
      },
      "grouping": {"productSubTitle": {"text": "[ITEM_LENGTH] Items"}},
      "shouldShowRateBlock": {"status": ["Delivered", "Returned"]},
      "shouldShowReviewBlock": {"status": ["Delivered", "Returned"]},
      "shouldShowReviewMessageBlock": {"status": ["Delivered", "Returned"]},
      "deliveryOtp": {"text": "Delivery OTP: [DELIVERY_OTP]"},
      "refund": {
        "text": "Refund of Rs. [TOTAL_REFUND_AMOUNT] [REFUND_STATUS] on [REFUND_DATE]"
      },
      "refundDateFormat": "[MMM] [DD] [YYYY]",
      "rateTitle": {"text": "Rate this product: "},
      "reviewMessage": {"rateMessage": {"text": "Rate items in order"}},
      "reviewTitle": {
        "1": {"text": "Review this product 1"},
        "2": {"text": "Review this product 2"},
        "3": {"text": "Review this product 3"},
        "4": {"text": "Review this product 4"},
        "5": {"text": "Review this product 5"}
      },
      "reviewSubTitle": {
        "1": {"text": "Write a review 1"},
        "2": {"text": "Write a review 2"},
        "3": {"text": "Write a review 3"},
        "4": {"text": "Write a review 4"},
        "5": {"text": "Write a review 5"}
      },
      "cta": {
        "navTitle": "Order Details",
        "headerType": 1,
        "source": "",
        "destination": "CommonWebViewScreen",
        "actionType": "T003",
        "actionUrl": "/customer/orderhistory/view/[FROM]/[ORDER_ID]/[SHIPMENT_ID]/[SOURCE]",
        "bundle": "",
        "navigationType": "push"
      },
      "ratingAndReviewCta": {
        "navTitle": "Rate & Review",
        "headerType": 1,
        "source": "",
        "destination": "JMRatingAndReviewFormScreen",
        "actionType": "T001",
        "actionUrl": "",
        "bundle": "",
        "navigationType": "push"
      }
    },
    "negativeCases": {
      "image": {
        "uri": "https://myjiostatic.cdn.jio.com/JioMart/Common/shopping-bag.svg"
      },
      "title": {"text": "We're waiting for your first order"},
      "subTitle": {
        "text": "No orders placed yet. Shop from our categories and grab the best deals on your order."
      },
      "button": {"title": "Explore Categories"},
      "isButtonVisible": true,
      "shouldShowContentInCenter": false,
      "cta": {
        "navTitle": "Add Address",
        "HeaderType": 5,
        "source": "",
        "destination": "HomeScreen",
        "actionType": "T003",
        "actionUrl": "",
        "bundle": "",
        "headerVisibility": 2,
        "navigationType": "reset",
        "shouldShowBottomNavBar": false,
        "shouldShowDeliverToBar": true,
      }
    },
    "filterBottomSheet": {
      "data": [
        {
          "key": {
            "display": "Order Type",
            "name": "orderStatusText",
            "kind": "singlevalued"
          },
          "values": "[ORDER_STATUS_LIST]"
        },
        {
          "key": {
            "display": "Order Date",
            "name": "timeFilter",
            "kind": "singlevalued"
          },
          "values": "[ORDER_DATE_LIST]"
        }
      ],
      "title": "Filter",
      "button": {
        "clearAll": {"isVisible": true, "title": "Clear All"},
        "apply": {"isVisible": true, "title": "Apply"}
      }
    }
  },
  "Refund": {
    "pageNo": 1,
    "pageSize": 10,
    "card": {
      "headerTitle": {"text": "Order ID: [ORDER_ID]"},
      "icon": {"ic": "IcChevronRight"},
      "title": {},
      "subTitle": {},
      "cta": {
        "actionType": "T001",
        "navTitle": "Refund Details",
        "source": "",
        "destination": "JMRefundDetailScreen",
        "bundle": "",
        "headerType": 1,
        "navigationType": "push"
      }
    },
    "negativeCases": {
      "image": {
        "uri": "https://myjiostatic.cdn.jio.com/JioMart/Common/no-refunds.svg"
      },
      "title": {"text": "There are no refunds to show"},
      "subTitle": {
        "text": "You haven't placed any refund request yet. Get help with returns and refunds."
      },
      "button": {"title": "Need Help"},
      "isButtonVisible": true,
      "shouldShowContentInCenter": false,
      "cta": {
        "actionType": "T003",
        "actionUrl": "faqs",
        "bundle": "",
        "destination": "CommonWebViewScreen",
        "headerType": 6,
        "headerVisibility": 1,
        "navTitle": "FAQ's",
        "navigationType": "push",
        "params": null,
        "shouldShowDeliverToBar": false,
        "source": "",
        "userAuthenticationRequired": 2,
        "userJourneyRequiredState": 0
      }
    },
    "filterBottomSheet": {
      "data": [
        {
          "key": {
            "display": "Date & Time",
            "name": "timeFilter",
            "kind": "singlevalued"
          },
          "values": "[REFUND_DATE_LIST]"
        }
      ],
      "title": "Filter",
      "button": {
        "clearAll": {"isVisible": true, "title": "Clear All"},
        "apply": {"isVisible": true, "title": "Apply"}
      }
    }
  }
}
