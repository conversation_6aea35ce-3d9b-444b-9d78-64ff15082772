{"myProfile": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "profileContent": [{"type": "AccountCard", "profile_header": {"profileLeftIconName": "IcProfile", "defaultNameText": "<PERSON>"}, "cta": {"navTitle": "OneRetailUI", "source": "", "destination": "OneRetailUI", "actionUrl": "", "actionType": "T001", "bundle": "", "type": "avatar", "navigationType": "navigate"}}, {"type": "AccountMenuCard", "isVisible": true, "quickOptions": [{"iconName": "IcOrder", "title": "Orders", "isVisible": true, "cta": {"actionType": "T001", "actionUrl": "customer/orderhistory", "bundle": "", "destination": "JMOrderListScreen", "headerType": 11, "headerVisibility": 1, "navTitle": "My Orders", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1, "shouldShowBottomNavBar": true, "type": "Orders"}}, {"iconName": "IcFavorite", "title": "Wishlist", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "customer/wishlist", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 6, "headerVisibility": 1, "navTitle": "Wishlist", "navigationType": "push", "params": null, "shouldShowDeliverToBar": true, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}, {"iconName": "IcCoupon", "title": "Coupons", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "customer/couponstore", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 7, "headerVisibility": 1, "navTitle": "Coupon Store", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}, {"iconName": "IcHelp", "title": "Help", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "faqs", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 6, "headerVisibility": 1, "navTitle": "FAQ's", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 0}}]}, {"type": "AccountAccordian<PERSON>iew", "item": {"title": "My Account", "isAccordianClose": false, "is_visible": true, "items": [{"iconName": "IcNotification", "title": "My Notifications [NOTIFY_COUNT]", "isVisible": true, "cta": {"actionType": "T001", "actionUrl": "", "bundle": "", "destination": "JMNotificationScreen", "headerType": 1, "headerVisibility": 1, "navTitle": "My Notifications", "navigationType": "navigate", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 0}}, {"iconName": "IcPlaylistSuccessful", "title": "My List", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "customer/mylist", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 6, "headerVisibility": 1, "navTitle": "My List", "navigationType": "push", "params": null, "shouldShowDeliverToBar": true, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}, {"iconName": "IcLocation", "title": "Delivery Addresses", "isVisible": true, "cta": {"actionType": "T001", "actionUrl": "", "bundle": "", "destination": "JMAddressListScreen", "headerType": 1, "headerVisibility": 1, "navTitle": "Delivery Addresses", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}, {"iconName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "PAN Card Information", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "customer/account/pancard", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 1, "headerVisibility": 1, "navTitle": "PAN Card Information", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}]}}, {"type": "AccountAccordian<PERSON>iew", "item": {"title": "Payment Modes", "isAccordianClose": false, "is_visible": true, "items": [{"iconName": "IcWallet", "title": "<PERSON><PERSON><PERSON><PERSON>", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "customer/account/jiomartwallets", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 1, "headerVisibility": 6, "navTitle": "<PERSON><PERSON><PERSON><PERSON>", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}]}}, {"type": "AccountAccordian<PERSON>iew", "item": {"title": "Help & Support", "isAccordianClose": false, "is_visible": true, "items": [{"iconName": "IcDocumentViewer", "title": "Guide", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "how-to-buy-on-jiomart", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 6, "headerVisibility": 1, "navTitle": "How to buy on Jiomart", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}]}}, {"type": "AccountAccordian<PERSON>iew", "item": {"title": "Offer & Discounts", "isAccordianClose": false, "is_visible": true, "items": [{"iconName": "IcOfferCoupon", "title": "Offer Store", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "offers", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 9, "headerVisibility": 1, "navTitle": "", "navigationType": "push", "params": null, "shouldShowDeliverToBar": true, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}, {"iconName": "IcGiftCard", "title": "JioMart Gift Card/Store", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "customer/giftcard", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 6, "headerVisibility": 1, "navTitle": "JioMart Gift Card", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 1}}]}}, {"type": "AccountAccordian<PERSON>iew", "item": {"title": "More Information", "isAccordianClose": false, "is_visible": true, "items": [{"iconName": "IcTeam", "title": "About <PERSON><PERSON><PERSON><PERSON>", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "about-us", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 6, "headerVisibility": 1, "navTitle": "About Us", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 0}}, {"iconName": "IcForm", "title": "Legal Information", "isVisible": true, "cta": {"actionType": "T003", "actionUrl": "terms-and-conditions", "bundle": "", "destination": "CommonWebViewScreen", "headerType": 1, "headerVisibility": 1, "navTitle": "Legal Information", "navigationType": "push", "params": null, "shouldShowDeliverToBar": false, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 0}}, {"iconName": "IcLogout", "title": "Sign Out", "isVisible": true, "cta": {"actionType": "T001", "actionUrl": "", "bundle": "", "destination": "OneRetailUI", "headerType": 1, "headerVisibility": 1, "navTitle": "", "navigationType": "push", "params": null, "shouldShowDeliverToBar": true, "source": "", "userAuthenticationRequired": 2, "userJourneyRequiredState": 0}, "deeplink": {"DeeplinkIdentifier": "sign_out"}}]}}, {"type": "CloseAccountAccordian<PERSON>iew", "item": {"title": "Account <PERSON><PERSON>", "isAccordianClose": true, "is_visible": true, "items": [{"iconName": "IcTrash", "title": "Close Account", "openInRN": true, "rnActionScrren": "CommonWebViewScreen", "webUrl": "c/delete-account", "header": {"header_type": 2}, "isVisible": true, "deeplink": {"titleID": "", "subTitle": "My Orders", "subTitleID": "", "visibility": 1, "iconURL": "jm_ic_jds_myorders.xml", "actionTag": "T003", "callActionLink": "jiomart_my_orders", "orderNo": 1, "commonActionURL": "profile/orders", "headerVisibility": 6, "headerTypeApplicable": "jiomart_title,jiomart_back,filter,jiomart_cart"}}]}}, {"type": "AppVersionView", "item": {"title": "JioMart App Version", "is_visible": true}}]}, {"isVisibleForVersion": "2", "versionNumber": "3.1.0", "profileContent": []}]}