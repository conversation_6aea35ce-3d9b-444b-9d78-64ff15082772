import { isNullOrUndefinedOrEmpty } from '../../../../jiomart-common/src/JMObjectUtility';
import { JMBaseUrlKeys } from '../../JMEnvironmentConfig';
import { JMError, JMErrorCodes, JMErrorMessages } from './JMErrorCodes';

export const handleApiError = (error: any): JMError => {
  if (error.response) {
    const apiErrorData = error.response.data;
    console.log({ apiErrorData });
    if (apiErrorData && apiErrorData.message && error.response.status) {
      return {
        code: error.response.status as JMErrorCodes,
        message: apiErrorData.message || JMErrorMessages.UNKNOWN_ERROR,
        response: error.response.data
      };
    }
    const status = error.response.status;
    switch (status) {
      case JMErrorCodes.TOKEN_EXPIRED:
      case JMErrorCodes.FORBIDDEN:
        return {
          code: JMErrorCodes.TOKEN_EXPIRED,
          message: JMErrorMessages.TOKEN_EXPIRED,
          response: apiErrorData
        };
      case JMErrorCodes.TOKEN_MISMATCH:
        return {
          code: JMErrorCodes.TOKEN_MISMATCH,
          message: JMErrorMessages.TOKEN_MISMATCH,
          response: apiErrorData
        };
      case JMErrorCodes.BAD_REQUEST:
      case JMErrorCodes.UNPROCESSABLE_ENTITY:
        return {
          code: status,
          message: JMErrorMessages.BAD_REQUEST,
          response: apiErrorData
        };
      case JMErrorCodes.FORCE_LOGOUT:
        return {
          code: JMErrorCodes.FORCE_LOGOUT,
          message: JMErrorMessages.FORCE_LOGOUT,
          response: apiErrorData
        };
      case JMErrorCodes.SERVER_ERROR:
        return {
          code: JMErrorCodes.SERVER_ERROR,
          message: JMErrorMessages.SERVER_ERROR,
          response: apiErrorData
        };
      default:
        return {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.UNKNOWN_ERROR,
          response: apiErrorData
        };
    }
  }
  // fallback for unhandled cases
  console.log(' --------- error message is not present --------- ', error);
  // Error case NetworkTimeout : In case of network timeout check for this condition and thorow timeout error.
  if (
    error.code === JMErrorCodes.NETWORK_ERR_TIMEOUT &&
    error.message.includes('Network Error')
  ) {
    return {
      code: JMErrorCodes.NETWORK_ERR_TIMEOUT,
      message: JMErrorMessages.NETWORK_ERR_TIMEOUT,
      response: error.response.data
    };
  }
  return {
    code: JMErrorCodes.UNKNOWN_ERROR,
    message: JMErrorMessages.UNKNOWN_ERROR,
    response: error.response.data
  };
};

export const isAuthTokenExpired = (
  error: Error,
  platform: JMBaseUrlKeys,
): boolean => {
  if (isNullOrUndefinedOrEmpty(error) || isNullOrUndefinedOrEmpty(platform)) {
    return false;
  }
  switch (platform) {
    case JMBaseUrlKeys.BIFROST:
      return error.code === JMErrorCodes.FORBIDDEN;
    default:
      return error.code === JMErrorCodes.TOKEN_EXPIRED;
  }
};
