import {AxiosRequestConfig} from 'axios';
import {AppSourceType} from '../../../../jiomart-common/src/SourceType';
import {JMSharedViewModel} from '../../../../jiomart-common/src/JMSharedViewModel';
import {JMRequestConfig, JMDefaultHeaders} from './JMRequestConfig';
import {JMNetworkRequestConstants} from '../constants/JMNetworkRequestConstants';
import {isNullOrUndefinedOrEmpty} from '../../../../jiomart-common/src/JMObjectUtility';
import {
  getApplicationId,
  getApplicationToken,
  getEnvironmentConfig,
} from '../../JMEnvironmentConfig';
import {sign} from '@gofynd/fp-signature';
import CryptoJS from 'crypto-js';
import JMCookieManager from './JMCookieManager';
import {JMDatabaseManager} from '../../db/JMDatabaseManager';

export default class JMRequestHelper<T> {
  private config: AxiosRequestConfig<T> = {};
  private _request: JMRequestConfig<T>;
  constructor(request: JMRequestConfig<T>) {
    this._request = request;
  }

  private async setupConfig() {
    this.config = {...this._request};
    this.config.url = this.getCompleteUrl();
    this.config.method = this.getHttpMethod();
    this.config.withCredentials = true;
    this.config.headers = await this.getHeaders();
    this.config.data = this.getBodyParams();
    this.config.timeout = this.getTimeout();
    this.config.params = this._request.params;
  }

  async updateRequestDetails() {
    await this.setupConfig();
    return this;
  }

  build(): AxiosRequestConfig<T> {
    if (!this.config.url || !this.config.method) {
      throw new Error(
        `Missing required fields (url => ${this.config.url}, method => ${this.config.method})`,
      );
    }
    return this.config;
  }

  private getBaseUrl() {
    const environmentConfigs: any = getEnvironmentConfig();
    if (environmentConfigs && environmentConfigs?.[this._request.baseUrl]) {
      return environmentConfigs[this._request.baseUrl];
    } else {
      console.error(
        `Base URL not found for Environment: ${environmentConfigs} and Service: ${this._request.baseUrl}`,
      );
      return undefined;
    }
  }

  private getEndPoint() {
    return this._request.endpoint;
  }

  private getCompleteUrl() {
    const baseUrl = this.getBaseUrl();
    if (!isNullOrUndefinedOrEmpty(baseUrl)) {
      return `${baseUrl}${this._request.endpoint}`;
    }
  }

  private getHttpMethod() {
    return this._request.method;
  }

  public async getHeaders() {
    let defaultHeaders = JMDefaultHeaders();

    if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_JCP) {
      const xLocationDetail = await this.getXLocationDetail();
      if (!isNullOrUndefinedOrEmpty(xLocationDetail)) {
        defaultHeaders = {...defaultHeaders, ...xLocationDetail};
      }

      const authorization = this.getAuthoriziation();
      if (!isNullOrUndefinedOrEmpty(authorization)) {
        defaultHeaders = {...defaultHeaders, ...authorization};
      }

      const cookie = await this.getCookie();
      defaultHeaders = {...defaultHeaders, ...cookie};

      const signedHeaders = this.setSignedFyndSignature();
      defaultHeaders = {...defaultHeaders, ...signedHeaders};
    } else {
      const signedHeaders = await this.getBauHeaderDetails();
      defaultHeaders = {...defaultHeaders, ...signedHeaders};
    }

    return {
      ...defaultHeaders,
      ...this._request.headers,
    };
  }

  private parseBaseURL(baseURL: string) {
    const pattern = /^(https?):\/\/([^/:]+)(:\d+)?(\/.*)?$/;
    const match = baseURL.match(pattern);

    if (!match) {
      throw new Error('Invalid URL');
    }

    return {
      protocol: match[1], // e.g. 'https'
      hostname: match[2], // e.g. 'api.example.com'
      port: match[3] ? match[3].slice(1) : '', // e.g. '3000'
      path: match[4] || '/', // e.g. '/v1' or '/'
      host: match[2] + (match[3] || ''), // e.g. 'api.example.com:3000'
    };
  }

  private setSignedFyndSignature = () => {
    try {
      const method = this.getHttpMethod();
      const path = this.getEndPoint();
      const url = this.parseBaseURL(this.getCompleteUrl() ?? '');
      const body = this.getBodyParams();

      const requestToSign: any = {
        method: method,
        host: url.host,
        path: path,
        headers: this.config.headers,
        body: body,
      };

      const signature = sign(requestToSign);

      return {
        'X-Fp-Signature': signature['x-fp-signature'],
        'X-Fp-Date': signature['x-fp-date'],
      };
    } catch (error) {}
  };

  private async getXLocationDetail() {
    let data: any = await JMDatabaseManager.address.getDefaultAddress();
    if (!isNullOrUndefinedOrEmpty(data)) {
      const parseData = JSON.parse(data);
      const xlocation = {
        pincode: parseData?.pin,
        city: parseData?.city,
        state: parseData?.state,
        country_iso_code: parseData?.country_iso_code,
        country: parseData?.country,
      };
      return {
        'X-Location-Detail': JSON.stringify(xlocation),
      };
    }
    return null;
  }

  private async getPincodeHeader() {
    let data: any = await JMDatabaseManager.address.getDefaultAddress();
    if (!isNullOrUndefinedOrEmpty(data)) {
      const parseData = JSON.parse(data);
      return {
        pin: parseData?.pin,
      };
    }
    return {};
  }

  private async getHeadersBySession() {
    const userSession = await JMDatabaseManager.user.getUserSession();
    if (userSession) {
      const userSessionData = JSON.parse(userSession);
      const headers = {
        authtoken: userSessionData?.id,
        userid: userSessionData?.customer_id,
        ['jwt-token']: userSessionData?.id,
      };
      return headers;
    } else {
      const guestUserSession = await JMDatabaseManager.user.getGuestUserSession();
      if (guestUserSession) {
        const userSessionData = JSON.parse(guestUserSession);
        const headers = {
          authtoken: userSessionData?.id ?? '',
          userid: userSessionData?.customer_id ?? '',
          ['jwt-token']: userSessionData?.id,
        };
        return headers;
      }
    }
  }

  private async getBauHeaderDetails() {
    const headers = await this.getHeadersBySession();
    const pincodeHeader = await this.getPincodeHeader();
    return {
      ...headers,
      ...pincodeHeader,
    };
  }

  private async getCookie() {
    return JMCookieManager.getCookieHeader(
      ['f.session.sit'],
      this.getBaseUrl(),
    );
  }
  private getAuthoriziation() {
    const appId = getApplicationId();
    const appToken = getApplicationToken();
    const bearerToken = this.generateBearerToken(appId, appToken);
    return {
      Authorization: bearerToken,
    };
  }

  private generateBearerToken(appId: string, appToken: string) {
    const data = `${appId}:${appToken}`;
    const base64 = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data));
    return `Bearer ${base64}`;
  }

  private getBodyParams() {
    return this._request.data;
  }

  private getTimeout() {
    switch (this._request.endpoint) {
      default:
        return JMNetworkRequestConstants.DEFAULT_TIME_OUT;
    }
  }
}
