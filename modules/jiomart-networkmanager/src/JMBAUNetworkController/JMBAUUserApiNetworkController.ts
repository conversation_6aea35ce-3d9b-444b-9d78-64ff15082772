import {JMBAUUserApiEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON>piClient from '../api/service/JMApiClient';
import JMBaseUserApiNetworkController from '../base/JMBaseUserApiNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {Platform} from 'react-native';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import { JMDatabaseManager } from '../db/JMDatabaseManager';

class JMBAUUserApiNetworkController extends JMBase<PERSON>serApiNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys;
  private headelessBaseUrl: JMBaseUrlKeys;
  private accountBaseUrl: JMBaseUrlKeys;
  constructor() {
    super();
    this.baseUrl = JMBaseUrlKeys.BAU;
    this.headelessBaseUrl = JMBaseUrlKeys.HEADLESS;
    this.accountBaseUrl = JMBaseUrlKeys.ACCOUNT;
  }

  protected fetchUserDetails = async () => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUUserApiEndpointKeys.GET_USER_DETAILS,
          JMHttpMethods.GET,
        ),
      );
      const mstarUserDetails = await this.apiClient.request(requestHelper);
      if (mstarUserDetails?.status === 'success') {
        const craCustumerDetails = await this.fetchCraCustomerDetails();
        if (craCustumerDetails?.success === true) {
          let updatedObj = {
            ...mstarUserDetails,
            result: {
              ...mstarUserDetails?.result,
              your_details: {
                ...mstarUserDetails?.result?.your_details,
                firstname: craCustumerDetails?.data?.first_name,
                lastname: craCustumerDetails?.data?.last_name,
                gender: craCustumerDetails?.data?.gender,
                mobile_no: craCustumerDetails?.data?.mobile,
                email: craCustumerDetails?.data?.email,
              },
            },
          };
          return updatedObj;
        }
        return mstarUserDetails;
      }
    } catch (error) {
      throw error;
    }
  };

  protected fetchGuestUserSession = async () => {
    try {
      const guestUserSession = await JMDatabaseManager.user.getGuestUserSession();
      if (guestUserSession) {
        return;
      }
      const params = {
        channel: `app-${Platform.OS}`,
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUUserApiEndpointKeys.CREATE_GUEST_USER_SESSION,
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      const guestSessionData: any = await this.apiClient.request(requestHelper);
      if (guestSessionData.status === 'success') {
        const jsonString = JSON.stringify(guestSessionData.result.session);
        JMDatabaseManager.user.saveGuestUserSession(jsonString)
      }
    } catch (error) {}
  };

  protected fetchCraCustomerDetails = async () => {
    try {
      const craSessionData = await getPrefString(
        AsyncStorageKeys.CRA_USER_SESSION_DATA,
      );
      if (!craSessionData) {
        return;
      }
      const parsedCraSessionData = JSON.parse(craSessionData);
      const headers = {
        client_id: 'fe41c55b-fa30-494c-9729-f831fb28ad9d',
        authorization: `${parsedCraSessionData?.token_type ?? 'Bearer'} ${
          parsedCraSessionData?.access_token
        }`,
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.accountBaseUrl,
          JMBAUUserApiEndpointKeys.GET_CRA_CUSTOMER_DETAILS,
          JMHttpMethods.GET,
          undefined,
          {},
          headers,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchLoggedInUserSession = async (authCode: string) => {
    try {
      const guestUserSession = await JMDatabaseManager.user.getGuestUserSession();
      let guestUserSessionId = '';
      if (guestUserSession) {
        const userSessionData = JSON.parse(guestUserSession);
        guestUserSessionId = userSessionData?.id ?? '';
      }
      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
      };
      const body = {
        auth_code: authCode,
        channel: `app-${Platform.OS}`,
        merge_session: guestUserSessionId,
      };

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.headelessBaseUrl,
          JMBAUUserApiEndpointKeys.GET_LOGGED_IN_USER_SESSION,
          JMHttpMethods.POST,
          JSON.stringify(body),
          null,
          headers,
        ),
      );
      const userSessionData: any = await this.apiClient.request(requestHelper);
      if (userSessionData.status === 'success') {
        const mstarJsonString = JSON.stringify(
          userSessionData.data.mstar_session,
        );
        const craJsonString = JSON.stringify(userSessionData.data.cra_session);
        const jcpJsonString = JSON.stringify(userSessionData.data.jcp_session);
        console.log('mstarJsonString', mstarJsonString);
        console.log('craJsonString', craJsonString);
        console.log('jcpJsonString', jcpJsonString);
        await JMDatabaseManager.user.saveUserSession(mstarJsonString)
        await addStringPref(
          AsyncStorageKeys.CRA_USER_SESSION_DATA,
          craJsonString,
        );
        await addStringPref(
          AsyncStorageKeys.JCP_USER_SESSION_DATA,
          jcpJsonString,
        );
        return await userSessionData;
      }
    } catch (error) {
      throw error;
    }
  };

  protected logoutUser = async () => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUUserApiEndpointKeys.LOGOUT_USER,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUUserApiNetworkController;
