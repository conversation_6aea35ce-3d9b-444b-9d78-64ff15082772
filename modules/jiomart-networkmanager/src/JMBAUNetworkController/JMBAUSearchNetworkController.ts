import axios from 'axios';
import {JMBAUSearchEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import algoliasearch from 'algoliasearch/lite';
import {FetchAlgoliaIndexSearchProps} from '../models/JMSearch/JMSearchModels';
import {base64DecodeValue} from '../../../jiomart-common/src/Helper';
import {
  AutoCompleteResponse,
  BuyAgain,
  BuyAgainItem,
} from '../../../jiomart-general/src/ui/SearchScreen/types/AutoCompleteResponse';
import {getPrefString} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {JMDatabaseManager} from '../db/JMDatabaseManager';

export default class JMBAUSearchNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys;
  constructor() {
    this.baseUrl = JMBaseUrlKeys.BAU;
  }
  protected getRecommendedProducts = async (params: any) => {
    try {
      let data: any = await JMDatabaseManager.address.getDefaultAddress();
      const parseData = JSON.parse(data);
      const pincode = parseData?.pin;
      const userSessionString = await JMDatabaseManager.user.getUserSession()
      const userSession = JSON.parse(userSessionString || '{}');
      const customHeader = {
        authtoken: userSession?.id,
        userid: userSession?.customer_id,
        'pin-code': pincode,
      };

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUSearchEndpointKeys.RECOMMENDED_ITEMS,
          JMHttpMethods.GET,
          undefined,
          undefined,
          customHeader,
        ),
      );

      const apiData = await this.apiClient.request(requestHelper);
      const itemCodesArray = apiData?.result?.productList;
      return itemCodesArray;
    } catch (error) {
      throw error;
    }
  };

  protected getBuyAgainItems = async (params: any) => {
    let data: any = await JMDatabaseManager.address.getDefaultAddress();
    const parseData = JSON.parse(data);
    const pincode = parseData?.pin;
    const userSessionString = await JMDatabaseManager.user.getUserSession()
    const userSession = JSON.parse(userSessionString || '{}');

    try {
      const customHeader = {
        authtoken: userSession?.id,
        userid: userSession?.customer_id,
        pin: pincode,
      };

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUSearchEndpointKeys.BUY_AGAIN,
          JMHttpMethods.GET,
          undefined,
          undefined,
          customHeader,
        ),
      );
      const apiData = await this.apiClient.request(requestHelper);
      if (apiData != null) {
        const buyAgainItemArray =
          apiData?.data?.all?.map(item => String(item.sku)) || [];
        return buyAgainItemArray;
      }
      return [];
    } catch (error) {
      throw error;
    }
  };

  private convertToAutoCompleteResponse = (
    rawData: any[],
  ): AutoCompleteResponse => {
    return rawData.map(item => ({
      type: 'product',
      display: item.objectID as string,
      departments: false,
      logo: {
        type: '',
        url: '',
      },
      _custom_json: null,
      action: {
        page: {
          type: 'product',
          query: {
            q: item.query as string,
          },
        },
        type: 'product',
      },
    }));
  };

  protected fetchDiscoverMoreData = async (
    config?: FetchAlgoliaIndexSearchProps,
  ) => {
    const {
      algoliaConfig,
      query = '',
      algoliaIndex = 'prod_mart_query_suggestions',
      hitsPerPage,
      configApiId,
      configApiHash,
    } = config || {};

    const decryptedconfigApiId = base64DecodeValue(configApiId);
    const decryptedconfigApiHash = base64DecodeValue(configApiHash);

    if (!decryptedconfigApiId || !decryptedconfigApiHash) {
      throw new Error('Algolia API credentials are missing or invalid.');
    }

    const searchClient = algoliasearch(
      decryptedconfigApiId,
      decryptedconfigApiHash,
    );
    const algoliaData = {
      indexName: algoliaIndex,
      query: query,
      params: {
        hitsPerPage: hitsPerPage,
        filters: algoliaConfig?.endpointDetails?.filters || '',
        analyticsTags: algoliaConfig?.endpointDetails?.analyticsTags,
        clickAnalytics: algoliaConfig?.endpointDetails?.clickAnalytics,
        page: 0,
        facets: ['*'],
      },
    };
    try {
      const index = searchClient.initIndex(algoliaData?.indexName);
      const {hits} = await index?.search(algoliaData.query, algoliaData.params);
      return this.convertToAutoCompleteResponse(hits);
    } catch (error) {
      throw error;
    }
  };

  protected getSearchResults = async (params: any) => {
    console.log('getSearchResults params:', params);
    const headers = {
      Accept: 'application/json, text/plain, */*',
      'Access-Control-Allow-Credentials': 'true',
      Authorization: 'Bearer NjYzZTIzMzJiNzAzMTA0MzU1MWFkNjk0OlB6aGdqaXV4dg==',
      'Cache-Control': 'no-cache',
      'Content-Type': 'application/json',
      Cookie: 'f.session=...',
      'X-Currency-Code': 'INR',
      'X-Fp-Date': '20250509T103748Z',
      'X-Fp-Signature': 'v1.1:...',
      'X-Location-Detail':
        '{"pincode":"400001","city":"Mumbai","state":"Maharashtra","country_iso_code":"IN","country":"India"}',
      'x-company-id': '2',
    };

    try {
      const response = await axios.get(
        'https://jiomart.sit.jiomartjcp.com/ext/algolia/application/api/v1.0/auto-complete',
        {
          params,
          headers,
        },
      );
      return response.data;
    } catch (error) {
      console.error('Error in getSearchResults:', error);
      throw error;
    }
  };

  protected fetchRecommendedItemsServiceability = async (bodyParams: any) => {
    const body = {skuid: bodyParams};
    let data: any = await JMDatabaseManager.address.getDefaultAddress();
    const parseData = JSON.parse(data);
    const pincode = parseData?.pin;
    const userSessionString = await JMDatabaseManager.user.getUserSession()
    const userSession = JSON.parse(userSessionString || '{}');

    try {
      const customHeader = {
        authtoken: userSession?.id,
        userid: userSession?.customer_id,
        pin: pincode,
      };

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUSearchEndpointKeys.RECOMMENDED_ITEMS_SERVICIBILITY,
          JMHttpMethods.POST,
          body,
          null,
          customHeader,
        ),
      );

      const buyAgainData = await this.apiClient.request(requestHelper);
      const tempData = this.transformRecommendedResponse(buyAgainData?.data);
      return {items: tempData} as BuyAgain;
    } catch (error) {
      throw error;
    }
  };

  private transformRecommendedResponse = (rawData: any[]): BuyAgainItem[] => {
    return rawData.map(item => ({
      url: item.url_path,
      type: '',
      name: item.display_name,
      slug: '',
      uid: Number(item.alternate_product_code),
      item_code: item.product_code,
      attributes: {
        'vertical-code': item.vertical_code,
      },
      medias: [
        {
          type: 'image',
          url: item.image,
          alt: '',
        },
      ],
      discount: `${item.discount_pct} % OFF`,
      seller_id: item.seller_id,
      price: {
        effective: {
          min: item.selling_price,
          max: item.selling_price,
        },
        marked: {
          min: item.mrp,
          max: item.mrp,
        },
      },
      sellable: item.in_stock > 0,
    }));
  };
}
