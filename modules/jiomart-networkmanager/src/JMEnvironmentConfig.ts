import { isNullOrUndefinedOrEmpty } from '../../jiomart-common/src/JMObjectUtility';
import { JMSharedViewModel } from '../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../jiomart-common/src/SourceType';
import { APIConstant } from './api/constants/JMNetworkRequestConstants';

export const enum JMEnvironment {
  SIT = 'SIT',
  PROD = 'PROD',
  UAT = 'UAT',
  REPLICA = 'REPLICA',
}

export const enum JMBaseUrlKeys {
  LEGACY = 'LEGACY',
  BIFROST = 'BIFROST',
  AZURE_STORAGE = 'AZURE_STORAGE',
  LEGACY_WEBAPP = 'LEGACY_WEBAPP',
  JCP = 'JCP',
  BAU = 'BAU',
  HEADLESS = 'HEADLESS',
  ACCOUNT = 'ACCOUNT',
  RATING_REVIEW = 'RATING_REVIEW',
}

const baseURLs = {
  [JMEnvironment.SIT]: {
    [AppSourceType.JM_JCP]: APIConstant.JCP_SIT_HOST_URL,
    [AppSourceType.JM_BAU]: APIConstant.BAU_SIT_HOST_URL,
  },
  [JMEnvironment.PROD]: {
    [AppSourceType.JM_JCP]: APIConstant.JCP_PROD_HOST_URL,
    [AppSourceType.JM_BAU]: APIConstant.BAU_PROD_HOST_URL,
  },
  [JMEnvironment.UAT]: {
    [AppSourceType.JM_JCP]: APIConstant.JCP_UAT_HOST_URL,
    [AppSourceType.JM_BAU]: APIConstant.BAU_UAT_HOST_URL,
  },
};

const oneRetailConfig = {
  [JMEnvironment.SIT]: {
    [AppSourceType.JM_JCP]: {
      apiDomain: 'https://api.cctz0.de',
      clientId: 'a5184e2f-acfa-48f5-b1ea-b63a96221a2e',
      returenUiUrl: 'jiomart.sit.jiomartjcp.com',
    },
    [AppSourceType.JM_BAU]: {
      apiDomain: 'https://api.cctz0.de',
      clientId: 'fe41c55b-fa30-494c-9729-f831fb28ad9d',
      returenUiUrl: 'jiomart-sit.jio.com',
    },
  },
  [JMEnvironment.PROD]: {
    [AppSourceType.JM_JCP]: {
      apiDomain: 'https://account.cctz0.de',
      clientId: '',
      returenUiUrl: '',
    },
    [AppSourceType.JM_BAU]: {
      apiDomain: 'https://account.cctz0.de',
      clientId: '',
      returenUiUrl: '',
    },
  },
  [JMEnvironment.UAT]: {
    [AppSourceType.JM_JCP]: {
      apiDomain: 'https://api-sit.account.relianceretail.com',
      clientId: 'fcbb6c5e-01a5-462f-a7d1-588847e4df37',
      returenUiUrl: 'jiomart.rep.jiomartjcp.com',
    },
    [AppSourceType.JM_BAU]: {
      apiDomain: 'https://account.cctz0.de',
      clientId: '',
      returenUiUrl: '',
    },
  },
  [JMEnvironment.REPLICA]: {
    [AppSourceType.JM_JCP]: {
      apiDomain: 'https://sit.account.relianceretail.com/',
      clientId: 'fcbb6c5e-01a5-462f-a7d1-588847e4df37',
      returenUiUrl: 'jiomart.rep.jiomartjcp.com',
    },
    [AppSourceType.JM_BAU]: {
      apiDomain: 'https://account.cctz0.de/',
      clientId: '',
      returenUiUrl: '',
    },
  },
};

const environmentConfig = {
  [JMEnvironment.SIT]: {
    [JMBaseUrlKeys.LEGACY]: 'https://api.sit.health.jiolabs.com',
    [JMBaseUrlKeys.BIFROST]: 'https://bifrostsit.healthhub.net.in',
    [JMBaseUrlKeys.AZURE_STORAGE]: '',
    [JMBaseUrlKeys.LEGACY_WEBAPP]: 'https://rep-healthhub.jiolabs.com',
    [JMBaseUrlKeys.JCP]: 'https://jiomart.sit.jiomartjcp.com',
    [JMBaseUrlKeys.BAU]: 'https://jiomart-sit.jio.com',
    [JMBaseUrlKeys.HEADLESS]: 'https://jiomart-headless.extensions.jiox1.de',
    [JMBaseUrlKeys.ACCOUNT]: 'https://api.cctz0.de',
    [JMBaseUrlKeys.RATING_REVIEW]: 'https://reviews-ratings-dev.jio.com',
  },
  [JMEnvironment.REPLICA]: {
    [JMBaseUrlKeys.LEGACY]: 'https://api.rep.health.jiolabs.com',
    [JMBaseUrlKeys.BIFROST]: 'https://bifrostbeta.healthhub.net.in',
    [JMBaseUrlKeys.AZURE_STORAGE]: '',
    [JMBaseUrlKeys.LEGACY_WEBAPP]: 'https://rep-healthhub.jiolabs.com',
    [JMBaseUrlKeys.JCP]: 'https://jiomart.rep.jiomartjcp.com',
    [JMBaseUrlKeys.BAU]: '',
    [JMBaseUrlKeys.RATING_REVIEW]: 'https://reviews-ratings.jio.com',
  },
  [JMEnvironment.PROD]: {
    [JMBaseUrlKeys.LEGACY]: 'https://api.prod.health.jio.com',
    [JMBaseUrlKeys.BIFROST]: 'https://bifrost.healthhub.net.in',
    [JMBaseUrlKeys.AZURE_STORAGE]: '',
    [JMBaseUrlKeys.LEGACY_WEBAPP]: 'https://healthhub.jio.com',
    [JMBaseUrlKeys.JCP]: 'https://jiomart.rep.jiomartjcp.com',
    [JMBaseUrlKeys.BAU]: 'https://jiomart.com',
    [JMBaseUrlKeys.RATING_REVIEW]: 'https://reviews-ratings.jio.com',
  },
  [JMEnvironment.UAT]: {
    [JMBaseUrlKeys.LEGACY]: 'https://api.prod.health.jio.com',
    [JMBaseUrlKeys.BIFROST]: 'https://bifrost.healthhub.net.in',
    [JMBaseUrlKeys.AZURE_STORAGE]: '',
    [JMBaseUrlKeys.LEGACY_WEBAPP]: 'https://healthhub.jio.com',
    [JMBaseUrlKeys.JCP]: 'https://jiomart.rep.jiomartjcp.com',
    [JMBaseUrlKeys.BAU]: '',
    [JMBaseUrlKeys.RATING_REVIEW]: 'https://reviews-ratings.jio.com',
  },
};

const ApplicationInfo = {
  [JMEnvironment.SIT]: {
    [AppSourceType.JM_JCP]: {
      applicationId: '663e2332b7031043551ad694',
      applicationToken: 'Pzhgjiuxv',
    },
    [AppSourceType.JM_BAU]: {
      applicationId: '',
      applicationToken: '',
    },
  },
  [JMEnvironment.REPLICA]: {
    [AppSourceType.JM_JCP]: {
      applicationId: '6683dfe9972afa89ef95eabf',
      applicationToken: '5kBQpeSPQ',
    },
    [AppSourceType.JM_BAU]: {
      applicationId: '',
      applicationToken: '',
    },
  },
  [JMEnvironment.PROD]: {
    [AppSourceType.JM_JCP]: {
      applicationId: '66d948dd48677d1ae49561a3',
      applicationToken: 'DidVPGVuW',
    },
    [AppSourceType.JM_BAU]: {
      applicationId: '',
      applicationToken: '',
    },
  },
  [JMEnvironment.UAT]: {
    [AppSourceType.JM_JCP]: {
      applicationId: '6683dfe9972afa89ef95eabf',
      applicationToken: '5kBQpeSPQ',
    },
    [AppSourceType.JM_BAU]: {
      applicationId: '',
      applicationToken: '',
    },
  },
};

let currentEnvironment = JMEnvironment.SIT;

export const setCurrentEnvironment = (env: string) => {
  currentEnvironment = !isNullOrUndefinedOrEmpty(env)
    ? (env as JMEnvironment)
    : JMEnvironment.SIT;
};

export const getCurrentEnvironment = () => currentEnvironment;

export const getEnvironmentConfig = () => {
  return environmentConfig[currentEnvironment];
};

export const getApplicationId = () => {
  const appSource = JMSharedViewModel.Instance.appSource;
  const envConfig = ApplicationInfo[currentEnvironment];
  return (envConfig?.[appSource as keyof typeof envConfig]?.applicationId) ?? "";
};
export const getApplicationToken = () => {
  const appSource = JMSharedViewModel.Instance.appSource;
  const envConfig = ApplicationInfo[currentEnvironment];
  return envConfig?.[appSource as keyof typeof envConfig]?.applicationToken ?? "";
};

export const getBaseURL = () => {
  const env = currentEnvironment as keyof typeof baseURLs;
  const appSource = JMSharedViewModel.Instance.appSource as keyof (typeof baseURLs)[typeof env];
  return baseURLs?.[env]?.[appSource];
};

export const getOneRetailsConfig = () => {
  const env = currentEnvironment as keyof typeof oneRetailConfig;
  const appSource = JMSharedViewModel.Instance.appSource as keyof (typeof oneRetailConfig)[typeof env];
  return oneRetailConfig?.[env]?.[appSource];
};

export const enum JMDataLoaderUrl {
  getDataLoader = '/api/service/application/content/v1.0/data-loader',
}