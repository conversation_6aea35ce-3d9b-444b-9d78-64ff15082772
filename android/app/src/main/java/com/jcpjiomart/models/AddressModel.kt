package com.jcpjiomart.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import com.google.android.gms.maps.model.LatLng
import kotlinx.parcelize.RawValue


@Parcelize
data class Address(

    @SerializedName("id")
    var id: String? = null,

    @SerializedName("name")
    var name: String? = null,

    @SerializedName("phone")
    var phone: String? = null,

    @SerializedName("address_type")
    var addressType: String? = null,

    @SerializedName("address")
    var address: String? = null,

    @SerializedName("flat_or_house_no")
    var flatOrHouseNo: String? = null,

    @SerializedName("floor_no")
    var floorNo: String? = null,

    @SerializedName("tower_no")
    var towerNo: String? = null,

    @SerializedName("area")
    var area: String? = null,

    @SerializedName("landmark")
    var landmark: String? = null,

    @SerializedName("city")
    var city: String? = null,

    @SerializedName("state")
    var state: String? = null,

    @SerializedName("pin")
    var pin: String? = null,

    @SerializedName("lat")
    var lat: Double? = null,

    @SerializedName("lon")
    var lon: Double? = null,

    @SerializedName("input_mode")
    var inputMode: String? = null,

    @SerializedName("is_default_address")
    var isDefaultAddress: Boolean? = null,

    @SerializedName("created_time")
    var createdTime: String? = null,

    @SerializedName("updated_time")
    var updatedTime: String? = null,

    @SerializedName("country_iso_code")
    var countryIsoCode: String? = null,

    @SerializedName("country")
    var country: String? = null

) : Parcelable {

    var coordinate: LatLng?
        get() = if (lat != null && lon != null) LatLng(lat!!, lon!!) else null
        set(value) {
            lat = value?.latitude
            lon = value?.longitude
        }
}