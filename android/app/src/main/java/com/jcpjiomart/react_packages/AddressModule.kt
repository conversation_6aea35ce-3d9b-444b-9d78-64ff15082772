package com.jcpjiomart.react_packages

import android.location.Address
import android.location.Geocoder
import android.util.Log
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.gson.Gson
import java.util.Locale
import com.google.android.gms.maps.model.LatLng



class AddressModule internal constructor(private var reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(
        reactContext
    ) {
    val TAG:String = this.name

    val geoCoder = Geocoder(reactContext, Locale("en", "IN"))

    val allowedForAddressRegex = Regex("^[a-zA-Z0-9_!@#\$%^&*()\\-+=|\\\\;:'\",.<>?/\\[\\]{} ]+$")

    @ReactMethod
    fun getReverseGeoCodeFromLatLong(latitude: Double, longitude: Double, promise: Promise) {
//        scope.launch {
//            withContext(Dispatchers.IO) {
                try {
                    val address = geoCoder.getFromLocation(latitude, longitude, 5)
                    if (address.isNullOrEmpty().not()) {
                        for (i in 0 until address?.size!!) {
                            address.get(i).latitude = latitude
                            address.get(i).longitude = longitude
                        }

                        promise.resolve(Gson().toJson(getAddressFromGeocoderResponse(address)))
                    }
                } catch (e: Exception) {
//                    addres1LiveData.postError(errorMessage = application.getString(R.string.map_place_not_found_))
                }
//            }
//        }
    }

    private fun isAddressTextInValid(text: String): Boolean {
        Log.d("","addressResponse isAddressTextInValid--"+text+"||||"+text.matches(Regex("^[a-zA-Z][a-zA-Z, \\s]*")).not())
        return text.matches(Regex("^[a-zA-Z][a-zA-Z, \\s]*")).not()
    }

    private fun getRawAddressString(address: Address): String{
        val sb = StringBuilder()
        address.premises?.let { sb.append(it).append(", ") }
        address.subAdminArea?.let { sb.append(it).append("\n") }
        address.locality?.let { sb.append(it).append(", ") }
        address.adminArea?.let { sb.append(it).append(", ") }
        address.countryName?.let { sb.append(it).append(", ") }
        address.postalCode?.let { sb.append(it) }

        Log.d("","addressResponse getRawAddressString--"+sb)
        return sb.toString()
    }
    private fun dontContainsNonEnglishChar(address: Address): Boolean{
/*
        Console.debug("addressResponse","dontContainsHindiChar---"+address+"||"+address.getAddressLine(address.maxAddressLineIndex))
        if(allowedRegex.matches(address.getAddressLine(address.maxAddressLineIndex))){
            Console.debug("addressResponse","dontContainsHindiChar---inside if")
        }
        else{
            Console.debug("addressResponse","dontContainsHindiChar---inside else")
        }*/
        return allowedForAddressRegex.matches(address.getAddressLine(address.maxAddressLineIndex))
    }
    fun getAddressFromGeocoderResponse(
        addressResponse: List<Address>?
    ): com.jcpjiomart.models.Address? {
        return try {
            Log.d("addressResponse", "addressResponse--$addressResponse")

            val address = addressResponse?.firstOrNull {
                it.postalCode.isNullOrEmpty().not() && dontContainsNonEnglishChar(it)
            }

            Log.d("addressResponse", "address--$address")

            if (address != null) {
                if (!address.countryName.equals("India", ignoreCase = true)) {
                    return null
                }

                val sb = StringBuilder()
                if (address.maxAddressLineIndex >= 0) {
                    for (i in 0..address.maxAddressLineIndex) {
                        address.getAddressLine(i)?.let {
                            sb.append(it)
                            if (i != address.maxAddressLineIndex) sb.append("\n")
                        }
                    }
                } else {
                    address.premises?.let { sb.append(it).append(", ") }
                    address.subAdminArea?.let { sb.append(it).append("\n") }
                    address.locality?.let { sb.append(it).append(", ") }
                    address.adminArea?.let { sb.append(it).append(", ") }
                    address.countryName?.let { sb.append(it).append(", ") }
                    address.postalCode?.let { sb.append(it) }
                }

                val city = address.locality ?: address.subAdminArea ?: ""
                val state = address.adminArea ?: ""
                val latLng = LatLng(address.latitude, address.longitude)

                return com.jcpjiomart.models.Address().apply {
                    this.coordinate = latLng
                    this.city = city
                    this.state = state
                    this.pin = address.postalCode
                    this.country = address.countryName
                    this.address = removePlaceIdFromAddress(sb.toString())
                    this.area = address.subLocality
                    this.lat = address.latitude
                    this.lon = address.longitude
                }
            }

            null
        } catch (e: Exception) {
            Log.e("getAddressFromGeocoderResponse", "Error: ", e)
            null
        }
    }


    private fun removePlaceIdFromAddress(addressString: String) : String{
        val addressArray = addressString.split(",")
        val newaddressArray = arrayListOf<String>()

        val match = addressArray.filter { it.contains("+", ignoreCase = true) /*&& it.first().isUpperCase()  && it.last().isUpperCase()*/ }

        if(match.isNullOrEmpty().not())
            addressArray.filterTo(newaddressArray, { it != match[0] })

        val match1 = addressArray.filter { it.contains("\"", ignoreCase = true) /*&& it.first().isUpperCase()  && it.last().isUpperCase()*/ }

        if(match1.isNullOrEmpty().not())
            addressArray.filterTo(newaddressArray, { it != match1[0] })

        return if(newaddressArray.isNullOrEmpty().not())
            newaddressArray.joinToString(separator = ",")
        else
            addressString
    }

    override fun getName(): String {
        return "JMRNAddressModule"
    }
}